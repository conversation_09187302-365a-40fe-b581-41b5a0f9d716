const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:3000'; // Adjust based on your server configuration
const API_ENDPOINT = '/api/equipment/front-search';

// Sample test parameters
const testParams = {
  page: 0,
  perPage: 20,
  sort: 'desc',
};

// Function to test API performance
async function testAPIPerformance() {
  console.log('🚀 Testing Equipment Front Search API Performance...\n');

  try {
    // Test without cache (first request)
    console.log('📊 Test 1: First request (no cache)');
    const startTime1 = Date.now();

    const response1 = await axios.get(`${BASE_URL}${API_ENDPOINT}`, {
      params: testParams,
      headers: {
        Authorization: 'Bearer YOUR_TOKEN_HERE', // Replace with actual token
        'Content-Type': 'application/json',
      },
    });

    const endTime1 = Date.now();
    const duration1 = endTime1 - startTime1;

    console.log(`⏱️  Response time: ${duration1}ms`);
    console.log(`📦 Response size: ${JSON.stringify(response1.data).length} characters`);
    console.log(`✅ Status: ${response1.status}`);
    console.log(`📋 Records count: ${response1.data.data?.allRecordsCount || 0}\n`);

    // Test with cache (second request)
    console.log('📊 Test 2: Second request (with cache)');
    const startTime2 = Date.now();

    const response2 = await axios.get(`${BASE_URL}${API_ENDPOINT}`, {
      params: testParams,
      headers: {
        Authorization: 'Bearer YOUR_TOKEN_HERE', // Replace with actual token
        'Content-Type': 'application/json',
      },
    });

    const endTime2 = Date.now();
    const duration2 = endTime2 - startTime2;

    console.log(`⏱️  Response time: ${duration2}ms`);
    console.log(`📦 Response size: ${JSON.stringify(response2.data).length} characters`);
    console.log(`✅ Status: ${response2.status}`);
    console.log(`📋 Records count: ${response2.data.data?.allRecordsCount || 0}\n`);

    // Performance analysis
    console.log('📈 Performance Analysis:');
    console.log(
      `🔄 Cache improvement: ${duration1 - duration2}ms (${(
        ((duration1 - duration2) / duration1) *
        100
      ).toFixed(1)}% faster)`
    );

    if (duration1 <= 1000) {
      console.log('🎉 SUCCESS: First request completed in under 1 second!');
    } else {
      console.log('⚠️  WARNING: First request took more than 1 second');
    }

    if (duration2 <= 500) {
      console.log('🎉 SUCCESS: Cached request completed in under 500ms!');
    } else {
      console.log('⚠️  WARNING: Cached request took more than 500ms');
    }

    // Verify response structure integrity
    console.log('\n🔍 Response Structure Verification:');
    const data = response1.data.data;

    if (data && data.inventoryData && Array.isArray(data.inventoryData)) {
      console.log('✅ inventoryData array exists');

      if (data.inventoryData.length > 0) {
        const firstItem = data.inventoryData[0];
        const requiredFields = ['_id', 'name', 'equipmentType', 'warehouse', 'inventoryLocation'];

        requiredFields.forEach(field => {
          if (firstItem.hasOwnProperty(field)) {
            console.log(`✅ ${field} field exists`);
          } else {
            console.log(`❌ ${field} field missing`);
          }
        });
      }
    } else {
      console.log('❌ inventoryData structure invalid');
    }
  } catch (error) {
    console.error('❌ Test failed:', error.message);

    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  console.log('⚠️  Note: Make sure to replace YOUR_TOKEN_HERE with a valid JWT token');
  console.log('⚠️  Note: Make sure your server is running on the correct port\n');

  testAPIPerformance();
}

module.exports = { testAPIPerformance };
