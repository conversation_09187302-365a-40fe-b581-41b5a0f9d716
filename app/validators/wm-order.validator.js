const { body, param, constantUtils, commonUtils } = require('../validators/parent.validator');

exports.approveRejectPMOrderValidationRule = () => {
  return [
    body('pmOrderManage').isArray().notEmpty().withMessage(constantUtils.PM_ORDER_DATA_REQUIRED),
  ];
};

exports.linkEquipmentWithOrderValidationRule = () => {
  return [
    body('equipmentId').notEmpty().withMessage(constantUtils.QR_CODE_REQUIRED),
    body('quantity').notEmpty().withMessage(constantUtils.QUANTITY_REQUIRED),
  ];
};

exports.updateOrderStatusValidationRule = () => {
  return [body('status').notEmpty().withMessage(constantUtils.STATUS_REQUIRED)];
};

exports.validateParamIds = () => {
  return [
    param('orderId').custom(value => {
      if (value && !commonUtils.isValidId(value)) {
        throw new Error(constantUtils.INVALID_ORDER_ID);
      }
      return true;
    }),
  ];
};
