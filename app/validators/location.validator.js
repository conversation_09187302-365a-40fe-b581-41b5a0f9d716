const { body, constantUtils } = require('../validators/parent.validator');

exports.locationValidationRule = () => {
  return [
    body('title').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_TITLE),
    body('project').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_PROJECT),
    body('longitude')
      .optional({ checkFalsy: false })
      .custom(async value => {
        if (Number(value) < -180 || Number(value) > 180) {
          throw new Error(constantUtils.ERROR_LONGITUDE);
        }
        return true;
      }),
    body('latitude')
      .optional({ checkFalsy: false })
      .custom(async value => {
        if (Number(value) < -90 || Number(value) > 90) {
          throw new Error(constantUtils.ERROR_LATITUDE);
        }
        return true;
      }),
  ];
};

exports.updateLocationValidationRule = () => {
  return [
    body('title')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_TITLE)
      .optional({ checkFalsy: false }),
    body('longitude')
      .optional({ checkFalsy: false })
      .custom(async value => {
        if (value && (Number(value) < -180 || Number(value) > 180)) {
          throw new Error(constantUtils.ERROR_LONGITUDE);
        }
        return true;
      }),
    body('latitude')
      .optional({ checkFalsy: false })
      .custom(async value => {
        if (value && (Number(value) < -90 || Number(value) > 90)) {
          throw new Error(constantUtils.ERROR_LATITUDE);
        }
        return true;
      }),
  ];
};
