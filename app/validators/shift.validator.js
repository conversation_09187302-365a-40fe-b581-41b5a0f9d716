const { body, constantUtils } = require('../validators/parent.validator');

exports.shiftValidationRule = () => {
  return [
    body('project').notEmpty().withMessage(constantUtils.PROJECT_SELECTION_REQUIRED),
    body('member').notEmpty().withMessage(constantUtils.MEMBER_SELECTION_REQUIRED),
    body('startDate').notEmpty().withMessage(constantUtils.START_DATE_REQUIRED),
    body('team').notEmpty().withMessage(constantUtils.TEAM_SELECTION_REQUIRED),
  ];
};

exports.updateShiftValidationRule = () => {
  return [
    body('project')
      .notEmpty()
      .withMessage(constantUtils.PROJECT_SELECTION_REQUIRED)
      .optional({ checkFalsy: false }),
    body('member')
      .notEmpty()
      .withMessage(constantUtils.MEMBER_SELECTION_REQUIRED)
      .optional({ checkFalsy: false }),
    body('startDate')
      .notEmpty()
      .withMessage(constantUtils.START_DATE_REQUIRED)
      .optional({ checkFalsy: false }),
    body('team')
      .notEmpty()
      .withMessage(constantUtils.TEAM_SELECTION_REQUIRED)
      .optional({ checkFalsy: false }),
  ];
};
