const { body, constantUtils } = require('../validators/parent.validator');
const equipmentCategoryService = require('../services/equipment-category.service');

exports.createEquipmentCategoryValidationRule = () => {
  return [
    body('name')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CATEGORY_NAME_REQUIRED)
      .custom(async (value, { req }) => {
        let requestData = {
          name: value,
          account: req.userData.account,
          deletedAt: null,
        };
        const isExist = await equipmentCategoryService.getSingleEquipmentCategoryByFilter(
          requestData
        );

        if (isExist) {
          throw new Error(constantUtils.EQUIPMENT_CATEGORY_NAME_EXIST);
        }
      }),
    body('abbreviation')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CATEGORY_ABBRIVATION_REQUIRED)
      .custom(async (value, { req }) => {
        let requestData = {
          abbreviation: value,
          account: req.userData.account,
          deletedAt: null,
        };
        const isExist = await equipmentCategoryService.getSingleEquipmentCategoryByFilter(
          requestData
        );

        if (isExist) {
          throw new Error(constantUtils.EQUIPMENT_CATEGORY_ABBRIVATION_EXIST);
        }
      }),
    body('abbreviation', 'Abbreviation must be in uppercase').isUppercase(),
    body('abbreviation', 'Abbreviation must be 3 characters long').isLength({ min: 3, max: 3 }),
  ];
};

exports.updateEquipmentCategoryValidationRule = () => {
  return [
    body('name')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CATEGORY_NAME_REQUIRED)
      .optional({ checkFalsy: false }),
    body('abbreviation')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CATEGORY_ABBRIVATION_REQUIRED)
      .optional({ checkFalsy: false }),
    body('abbreviation', 'Abbreviation must be in uppercase')
      .isUppercase()
      .optional({ checkFalsy: false }),
    body('abbreviation', 'Abbreviation must be 3 characters long')
      .isLength({ min: 3, max: 3 })
      .optional({ checkFalsy: false }),
  ];
};
