const { body, constantUtils } = require('../validators/parent.validator');
const bicValidator = require('bic-validator');

exports.filedValidationRule = () => {
  return [
    body('identityProof')
      .notEmpty()
      .withMessage(constantUtils.CONTRACTUAL_DOCUMENT_REQUIRED)
      .custom(value => {
        if (value.length > global.constant.PASSPORT_FILE_LIMIT) {
          throw new Error(constantUtils.INVALID_FILE_LIMIT);
        }
        return true;
      }),
    body('birthDate').isDate().notEmpty().withMessage(constantUtils.INSERT_BIRTH_DATE),
    body('bankName')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.CONTRACTUAL_BANK_NAME_REQUIRED)
      .optional({ checkFalsy: false }),
    body('bankAccount')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.CONTRACTUAL_BANK_ACCOUNT_REQUIRED)
      .optional({ checkFalsy: false }),
    body('bicSwift')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.CONTRACTUAL_DETAILS_REQUIRED)
      .custom(async value => {
        if (!bicValidator.isValid(value)) {
          throw new Error(constantUtils.INVALID_BIC);
        }
      })
      .optional({ checkFalsy: false }),
  ];
};

exports.updatefiledValidationRule = () => {
  return [
    body('identityProof')
      .notEmpty()
      .withMessage(constantUtils.CONTRACTUAL_DOCUMENT_REQUIRED)
      .optional({ checkFalsy: false })
      .custom(value => {
        if (value.length > global.constant.PASSPORT_FILE_LIMIT) {
          throw new Error(constantUtils.INVALID_FILE_LIMIT);
        }
        return true;
      }),
    body('birthDate')
      .isDate()
      .notEmpty()
      .withMessage(constantUtils.INSERT_BIRTH_DATE)
      .optional({ checkFalsy: false }),
    body('bankName')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.CONTRACTUAL_BANK_NAME_REQUIRED)
      .optional({ checkFalsy: false }),
    body('bankAccount')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.CONTRACTUAL_BANK_ACCOUNT_REQUIRED)
      .optional({ checkFalsy: false }),
    body('bicSwift')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.CONTRACTUAL_DETAILS_REQUIRED)
      .custom(async value => {
        if (!bicValidator.isValid(value)) {
          throw new Error(constantUtils.INVALID_BIC);
        }
      })
      .optional({ checkFalsy: false }),
  ];
};
