const { body, constantUtils } = require('../validators/parent.validator');

exports.teamValidationRule = () => {
  return [body('teamsWfmName').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_NAME)];
};

exports.updateTeamValidationRule = () => {
  return [
    body('teamsWfmName')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_NAME)
      .optional({ checkFalsy: false }),
  ];
};
