const { body, constantUtils } = require('../validators/parent.validator');

exports.reportValidationRule = () => {
  return [
    body('project').notEmpty().withMessage(constantUtils.PM_PROJECT_REQUIRED),
    body('reportType').notEmpty().withMessage(constantUtils.REPORT_TYPE_REQUIRED),
    body('location').notEmpty().withMessage(constantUtils.REPORT_LOCATION_REQUIRED),
    body('cable').notEmpty().withMessage(constantUtils.REPORT_CABLE_REQUIRED),
  ];
};
