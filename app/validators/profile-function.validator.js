const { body, constantUtils } = require('../validators/parent.validator');

exports.profileFunctionValidationRule = () => {
  return [
    body('name').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_PROFILE_NAME),
    body('isActive').isBoolean().withMessage(constantUtils.SELECT_VALID_PROFILE_STATUS),
  ];
};

exports.updateProfileFunctionValidationRule = () => {
  return [
    body('name')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_PROFILE_NAME)
      .optional({ checkFalsy: false }),
    body('isActive')
      .isBoolean()
      .withMessage(constantUtils.SELECT_VALID_PROFILE_STATUS)
      .optional({ checkFalsy: false }),
  ];
};
