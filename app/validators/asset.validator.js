const { body, constantUtils } = require('../validators/parent.validator');

exports.assetValidationRule = () => {
  return [
    body('project').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_PROJECT),
    body('fromLocation').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_FROMLOCATION),
    body('toLocation').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_TOLOCATION),
    body('cableName').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_CABLENAME),
  ];
};

exports.updateAssetValidationRule = () => {
  return [
    body('project')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_PROJECT)
      .optional({ checkFalsy: false }),
    body('fromLocation')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_FROMLOCATION)
      .optional({ checkFalsy: false }),
    body('toLocation')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_TOLOCATION)
      .optional({ checkFalsy: false }),
    body('cableName')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_CABLENAME)
      .optional({ checkFalsy: false }),
  ];
};
