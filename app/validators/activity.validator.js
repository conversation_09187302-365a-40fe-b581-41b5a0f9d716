const { body, constantUtils } = require('../validators/parent.validator');

exports.activityValidationRule = () => {
  return [
    body('name').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_NAME),
    body('project').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_PROJECT),
  ];
};

exports.updateActivityValidationRule = () => {
  return [
    body('name')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_NAME)
      .optional({ checkFalsy: false }),
    body('project')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_PROJECT)
      .optional({ checkFalsy: false }),
  ];
};
