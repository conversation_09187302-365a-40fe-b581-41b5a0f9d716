const { body, constantUtils } = require('../validators/parent.validator');

exports.createEquipmentUnitValidationRule = () => {
  return [
    body('title')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CERTIFICATE_TITLE_REQUIRED),
    body('abbreviation')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CATEGORY_ABBRIVATION_REQUIRED),
  ];
};
