const { body, constantUtils } = require('../validators/parent.validator');

exports.feedbackValidationRule = () => {
  return [
    body('type').isString().notEmpty().withMessage(constantUtils.FEEDBACK_TYPE_REQUIRED),
    body('subject').isString().notEmpty().withMessage(constantUtils.SUBJECT_REQUIRED),
    body('description').isString().notEmpty().withMessage(constantUtils.DESCRIPTION_REQUIRED),
  ];
};
