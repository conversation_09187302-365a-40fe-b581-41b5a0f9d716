const { body, constantUtils, validateParamIds } = require('./parent.validator');

const shoppingCartValidationRule = () => {
  return [
    body('title').notEmpty().isString().withMessage(constantUtils.TITLE_REQUIRED),
    body('project').notEmpty().withMessage(constantUtils.PROJECT_ID_REQUIRED),
    body('fromDate').notEmpty().withMessage(constantUtils.FROM_DATE_REQUIRED),
    body('toDate').notEmpty().withMessage(constantUtils.TO_DATE_REQUIRED),
  ];
};

module.exports = {
  shoppingCartValidationRule,
  validateParamIds,
};
