const { body, constantUtils } = require('../validators/parent.validator');

exports.memberValidationRule = () => {
  return [
    body('project').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_PROJECT),
    body('function').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_FUNCTION),
    body('user').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_USER),
  ];
};

exports.updateMemberValidationRule = () => {
  return [
    body('function')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_FUNCTION)
      .optional({ checkFalsy: false }),
  ];
};
