const { body, validateParamIds, constantUtils, commonUtils } = require('./parent.validator');

const reportQuestionValidationRule = () => {
  return [
    body('title')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CERTIFICATE_TITLE_REQUIRED),
    body('report').custom(value => {
      if (value && !commonUtils.isValidId(value)) {
        throw new Error(constantUtils.INVALID_REPORT_ID);
      }
      return true;
    }),
    body('sortOrder').isInt().notEmpty().withMessage(constantUtils.SORT_ORDER_REQUIRED),
    body('duration').isNumeric().notEmpty().withMessage(constantUtils.DURATION_REQUIRED),
    body('answers').isArray().notEmpty().withMessage(constantUtils.ANSWERS_REQUIRED),
  ];
};

module.exports = {
  reportQuestionValidationRule,
  validateParamIds,
};
