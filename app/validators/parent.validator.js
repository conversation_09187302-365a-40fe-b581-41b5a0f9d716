const { body, param, query, validationResult } = require('express-validator');
const commonUtils = require('../utils/common.utils');
const constantUtils = require('../utils/constants.utils');

const validateParamIds = () => {
  return [
    param('id').custom(value => {
      if (value) {
        return commonUtils.validateId(value, constantUtils.INVALID_ID);
      }
      return true;
    }),
    param('reportId').custom(value => {
      if (value) {
        return commonUtils.validateId(value, constantUtils.INVALID_REPORT_ID);
      }
      return true;
    }),
    param('questionId').custom(value => {
      if (value) {
        return commonUtils.validateId(value, constantUtils.INVALID_QUESTION_ID);
      }
      return true;
    }),
    query('project').custom(value => {
      if (value) {
        return commonUtils.validateId(value, constantUtils.INVALID_PROJECT_ID);
      }
      return true;
    }),
  ];
};

module.exports = {
  body,
  param,
  validationResult,
  validateParamIds,
  constantUtils,
  commonUtils,
};
