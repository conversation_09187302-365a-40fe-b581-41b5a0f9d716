const { body, constantUtils } = require('../validators/parent.validator');
const warehouseService = require('../services/warehouse.service');

exports.warehouseValidationRule = () => {
  return [
    body('name').isString().notEmpty().withMessage(constantUtils.WAREHOUSE_NAME_REQUIRED),
    body('street').isString().notEmpty().withMessage(constantUtils.WAREHOUSE_STREET_REQUIRED),
    body('city').isString().notEmpty().withMessage(constantUtils.WAREHOUSE_CITY_REQUIRED),
    body('state').isString().notEmpty().withMessage(constantUtils.WAREHOUSE_STATE_REQUIRED),
    body('country').isString().notEmpty().withMessage(constantUtils.WAREHOUSE_COUNTRY_REQUIRED),
    body('zipCode').isString().notEmpty().withMessage(constantUtils.WAREHOUSE_ZIPCODE_REQUIRED),
    body('email')
      .isEmail()
      .normalizeEmail()
      .isString()
      .notEmpty()
      .withMessage(constantUtils.WAREHOUSE_EMAIL_REQUIRED)
      .custom(async value => {
        const emailExist = await warehouseService.getWarehouseByEmail(value);
        if (emailExist) {
          throw new Error(constantUtils.EMAIL_TAKEN);
        }
      }),
    body('contactNumber')
      .isObject()
      .withMessage(constantUtils.INVALID_CONTACT_NUMBER_FORMAT)
      .notEmpty()
      .withMessage(constantUtils.EMPTY_CONTACT_NUMBER)
      .bail()
      .custom(value => {
        const { number } = value;
        if (!number) {
          throw new Error(constantUtils.INVALID_CONTACT_NUMBER);
        }
        return true;
      })
      .bail(),
  ];
};

exports.updateWarehouseValidationRule = () => {
  return [
    body('name')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.WAREHOUSE_NAME_REQUIRED)
      .optional({ checkFalsy: false }),
    body('street')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.WAREHOUSE_STREET_REQUIRED)
      .optional({ checkFalsy: false }),
    body('city')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.WAREHOUSE_CITY_REQUIRED)
      .optional({ checkFalsy: false }),
    body('state')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.WAREHOUSE_STATE_REQUIRED)
      .optional({ checkFalsy: false }),
    body('country')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.WAREHOUSE_COUNTRY_REQUIRED)
      .optional({ checkFalsy: false }),
    body('zipCode')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.WAREHOUSE_ZIPCODE_REQUIRED)
      .optional({ checkFalsy: false }),
    body('email')
      .isEmail()
      .normalizeEmail()
      .isString()
      .notEmpty()
      .withMessage(constantUtils.WAREHOUSE_EMAIL_REQUIRED)
      .custom(async value => {
        const emailExist = await warehouseService.getWarehouseByEmail(value);
        if (emailExist) {
          throw new Error(constantUtils.EMAIL_TAKEN);
        }
      })
      .bail()
      .optional({ checkFalsy: false }),
    body('contactNumber')
      .isObject()
      .withMessage(constantUtils.INVALID_CONTACT_NUMBER_FORMAT)
      .notEmpty()
      .withMessage(constantUtils.EMPTY_CONTACT_NUMBER)
      .bail()
      .custom(value => {
        const { number } = value;
        if (!number) {
          throw new Error(constantUtils.INVALID_CONTACT_NUMBER);
        }
        return true;
      })
      .bail()
      .optional({ checkFalsy: false }),
  ];
};
