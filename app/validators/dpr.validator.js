const { body, constantUtils } = require('./parent.validator');

exports.dprCreateValidationRule = () => {
  return [
    body('version').isString().notEmpty().withMessage(constantUtils.VERSION_ID_REQUIRED),
    body('project').notEmpty().withMessage(constantUtils.PROJECT_ID_REQUIRED),
    body('dprDate').notEmpty().withMessage(constantUtils.DPR_DATE_REQUIRED),
  ];
};

exports.dprUpdateValidationRule = () => {
  return [
    body('version').notEmpty().isString().withMessage(constantUtils.VERSION_ID_REQUIRED),
    body('project').notEmpty().withMessage(constantUtils.PROJECT_ID_REQUIRED),
    body('status').isString().notEmpty().withMessage(constantUtils.STATUS_REQUIRED),
    body('dprData')
      .notEmpty()
      .withMessage(constantUtils.DPR_DATA_REQUIRED)
      .custom(value => {
        if (typeof value !== 'object' || Object.keys(value).length === 0) {
          throw new Error(constantUtils.DPR_DATA_REQUIRED);
        }
        return true;
      }),
  ];
};
