const { body, constantUtils } = require('../validators/parent.validator');

exports.teamMemberValidationRule = () => {
  return [
    body('member').notEmpty().withMessage(constantUtils.MEMBER_SELECTION_REQUIRED),
    body('shift').notEmpty().withMessage(constantUtils.SHIFT_REQUIRED),
    body('function')
      .notEmpty()
      .withMessage(constantUtils.FUNCTION_REQUIRED)
      .optional({ checkFalsy: false }),
  ];
};

exports.updateTeamMemberValidationRule = () => {
  return [
    body('function')
      .notEmpty()
      .withMessage(constantUtils.FUNCTION_REQUIRED)
      .optional({ checkFalsy: false }),
  ];
};

exports.personnelValidationRule = () => {
  return [
    body('personnelListData')
      .isArray()
      .withMessage(constantUtils.TEAM_MEMBER_PERSONNEL_SHOULD_BE_ARRAY)
      .notEmpty()
      .withMessage(constantUtils.TEAM_MEMBER_PERSONNEL_INVALID_TYPE),

    body('personnelListData.*.teamMemberId')
      .notEmpty()
      .withMessage(constantUtils.TEAM_MEMBER_ID_REQUIRED)
      .isMongoId()
      .withMessage(constantUtils.TEAM_MEMBER_ID_REQUIRED),

    body('personnelListData.*.isWorking')
      .optional()
      .isBoolean()
      .withMessage(constantUtils.TEAM_MEMBER_INVALID_STATUS),

    body('personnelListData.*.status')
      .optional()
      .isBoolean()
      .withMessage(constantUtils.TEAM_MEMBER_INVALID_STATUS),

    body('personnelListData.*')
      .custom(item => {
        return Object.hasOwn(item, 'isWorking') || Object.hasOwn(item, 'status');
      })
      .withMessage(constantUtils.TEAM_MEMBER_STATUS_REQUIRED),
  ];
};
