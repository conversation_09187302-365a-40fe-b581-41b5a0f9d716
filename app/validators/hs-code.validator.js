const { body, constantUtils } = require('../validators/parent.validator');
const HSCodeController = require('../controllers/hs-code.controller');

exports.createHSCodeValidationRule = () => {
  return [
    body('name').isString().notEmpty().withMessage(constantUtils.EQUIPMENT_CATEGORY_NAME_REQUIRED),
    body('code')
      .notEmpty()
      .withMessage(constantUtils.HS_CODE_REQUIRED)
      .custom(async (value, { req }) => {
        let requestData = {
          code: value,
          account: req.userData.account,
        };
        return await HSCodeController.checkUniqueHSCode(requestData);
      }),
  ];
};

exports.updateHSCodeValidationRule = () => {
  return [
    body('name')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CATEGORY_NAME_REQUIRED)
      .optional({ checkFalsy: false }),
    body('code')
      .notEmpty()
      .withMessage(constantUtils.HS_CODE_REQUIRED)
      .optional({ checkFalsy: false })
      .custom(async (value, { req }) => {
        if (typeof value !== 'undefined') {
          let requestData = {
            code: value,
            account: req.userData.account,
          };
          return await HSCodeController.checkUniqueHSCode(requestData, req.params?.id);
        }
      }),
  ];
};
