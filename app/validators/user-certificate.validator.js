const { body, constantUtils } = require('../validators/parent.validator');

exports.filedValidationRule = () => {
  let fileds = [
    'cv',
    'medical',
    'chesterStep',
    'gwoManualHandling',
    'gwoFristAid',
    'gwoFireAwareness',
    'gwoWorkingAtHeight',
    'gwoSeaSurvival',
  ];

  let validaterData = [];

  fileds.forEach(ele => {
    validaterData = [
      ...validaterData,
      body(ele)
        .notEmpty()
        .withMessage(constantUtils.CONTRACTUAL_DOCUMENT_REQUIRED)
        .optional({ checkFalsy: false })
        .custom(value => {
          return this.checkValidDocLength(value);
        }),
    ];
  });

  return validaterData;
};

exports.checkValidDocLength = data => {
  if (data.length > global.constant.UPLOAD_FILE_LIMIT) {
    throw new Error(constantUtils.INVALID_FILE_LIMIT);
  }
  return true;
};
