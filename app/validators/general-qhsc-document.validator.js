const { body, constantUtils } = require('../validators/parent.validator');

exports.createGeneralQhscDocumentValidationRule = () => {
  return [
    body('documentName').isString().notEmpty().withMessage(constantUtils.INVALID_DOCUMENT_NAME),
    body('documentUrl').isString().notEmpty().withMessage(constantUtils.INVALID_DOCUMENT_URL),
  ];
};

exports.updateGeneralQhscDocumentValidationRule = () => {
  return [
    body('documentName')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.INVALID_DOCUMENT_NAME)
      .optional({ checkFalsy: false }),
    body('documentUrl')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.INVALID_DOCUMENT_URL)
      .optional({ checkFalsy: false }),
  ];
};
