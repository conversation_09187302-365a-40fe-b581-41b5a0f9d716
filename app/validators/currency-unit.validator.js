const { body, constantUtils } = require('../validators/parent.validator');
const currencyUnitController = require('../controllers/currency-unit.controller');

exports.createCurrencyUnitValidationRule = () => {
  return [
    body('name').isString().notEmpty().withMessage(constantUtils.EQUIPMENT_CATEGORY_NAME_REQUIRED),
    body('symbol').isString().notEmpty().withMessage(constantUtils.SYMBOL_REQUIRED),
    body('isDefault')
      .optional({ checkFalsy: false })
      .custom(async (value, { req }) => {
        if (typeof value !== 'undefined' && value === true) {
          let requestData = {
            isDefault: value,
            account: req.userData.account,
          };
          return await currencyUnitController.checkDefaultCurrency(requestData);
        }
        return false;
      }),
  ];
};

exports.updateCurrencyUnitValidationRule = () => {
  return [
    body('name')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.EQUIPMENT_CATEGORY_NAME_REQUIRED)
      .optional({ checkFalsy: false }),
    body('symbol')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SYMBOL_REQUIRED)
      .optional({ checkFalsy: false }),
    body('isDefault')
      .optional({ checkFalsy: false })
      .custom(async (value, { req }) => {
        if (typeof value !== 'undefined' && value === true) {
          let requestData = {
            isDefault: value,
            account: req.userData.account,
          };
          return await currencyUnitController.checkDefaultCurrency(requestData, req.params?.id);
        }
        return false;
      }),
  ];
};
