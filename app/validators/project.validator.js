const { body, constantUtils } = require('../validators/parent.validator');

exports.projectValidationRule = () => {
  return [
    body('title').isString().notEmpty().withMessage(constantUtils.SELECT_VALID_TITLE),
    body('projectNumber')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_PROJECT_NUMBER)
      .isLength({ max: 255 })
      .withMessage(constantUtils.INVALID_PROJECT_NUMBER),
    body('client')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.INVALID_CLIENT)
      .isLength({ max: 255 })
      .withMessage(constantUtils.INVALID_CLIENT_LENGTH),
  ];
};

exports.updateProjectValidationRule = () => {
  return [
    body('title')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_TITLE)
      .optional({ checkFalsy: false }),
    body('projectNumber')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.SELECT_VALID_PROJECT_NUMBER)
      .isLength({ max: 255 })
      .withMessage(constantUtils.INVALID_PROJECT_NUMBER)
      .optional({ checkFalsy: false }),
    body('client')
      .isString()
      .notEmpty()
      .withMessage(constantUtils.INVALID_CLIENT)
      .isLength({ max: 255 })
      .withMessage(constantUtils.INVALID_CLIENT_LENGTH)
      .optional({ checkFalsy: false }),
  ];
};
