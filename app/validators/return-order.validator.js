const { body, param, constantUtils, commonUtils } = require('../validators/parent.validator');

exports.returnEquipmentOrderValidationRule = () => {
  return [
    body('returnOrder').isArray().notEmpty().withMessage(constantUtils.RETURN_ORDER_REQUIRED),
  ];
};

exports.validateParamIds = () => {
  return [
    param('projectId').custom(value => {
      if (value && !commonUtils.isValidId(value)) {
        throw new Error(constantUtils.INVALID_PROJECT_ID);
      }
      return true;
    }),
    param('id').custom(value => {
      if (value && !commonUtils.isValidId(value)) {
        throw new Error(constantUtils.INVALID_ID);
      }
      return true;
    }),
  ];
};
