const UserLog = require('../models/userlog.model');

/**
 * Create User Log
 *
 * @param {*} logData
 * @returns
 */
exports.createUserLog = async logData => {
  return await UserLog.create(logData);
};

/**
 * Check if Previous Log Exist
 *
 * @param {*} userId
 * @returns
 */
exports.checkIfUserLogExist = async userId => {
  return await UserLog.findOne({ userId: userId });
};

/**
 * Update User Log After login
 *
 * @param {*} userId
 * @param {*} data
 * @returns
 */
exports.updateUserLogExist = async (userId, data) => {
  return await UserLog.findByIdAndUpdate(userId, { $set: data }, { new: true });
};
