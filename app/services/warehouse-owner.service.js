const WarehouseOwner = require('../models/warehouse-owner.model');

/**
 * Create warehouse owner
 *
 * @param {*} requestData
 * @returns
 */
exports.createWarehouseOwner = async requestData => {
  return await WarehouseOwner.create(requestData);
};

/**
 * Get warehouse owners
 *
 * @param {*} warehouse
 * @param {*} account
 * @returns
 */
exports.getOwners = async (warehouse, account) => {
  return await WarehouseOwner.find({ warehouse, account });
};

/**
 * Delete warehouse owners
 *
 * @param {*} id
 */
exports.deleteOwners = async id => {
  await WarehouseOwner.deleteMany({ _id: id });
};

/**
 * Get single data by filter
 *
 * @param {*} filter
 * @returns
 */
exports.getSingleDataByFilter = async filter => {
  return await WarehouseOwner.findOne(filter);
};

exports.getAllWarehouses = async filter => {
  return await WarehouseOwner.find(filter);
};
