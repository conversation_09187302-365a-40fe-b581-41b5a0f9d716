const VersionInfo = require('../models/version-info.model');

/**
 * Create VersionInfo
 *
 * @param {*} category
 * @returns
 */
exports.createVersionInfo = async requestData => {
  return await VersionInfo.create(requestData);
};

exports.getAllVersionInfoByFilter = async (filter = {}) => {
  return await VersionInfo.find(filter, { __v: 0 });
};

exports.getVersionInfoByFilter = async filter => {
  return await VersionInfo.findOne(filter, { __v: 0 });
};

exports.updateVersionInfo = async (id, requestData) => {
  return await VersionInfo.findByIdAndUpdate(id, requestData, { new: true });
};

exports.deleteVersionInfo = async id => {
  return await VersionInfo.deleteOne({ _id: id });
};
