const formbuilderModel = require('../models/form-builder.model');
const { toObjectId } = require('../utils/common.utils');

/**
 * Create Question
 *
 * @param {*} data
 * @returns
 */
exports.create = async data => {
  return await formbuilderModel.create(data);
};

/**
 * Get Question By Id
 *
 * @param {*} id
 * @returns
 */
exports.getQuestionById = async id => {
  return await formbuilderModel.findById(toObjectId(id));
};

/**
 * Get All Fields
 *
 * @param {*} account
 * @returns
 */
exports.getAllFields = async (account, type) => {
  return await formbuilderModel.find({ account, isActive: true, cardType: type }).populate([
    {
      path: 'user createdBy',
      select: { fullName: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Get Fields By Id
 *
 * @param {Array} ids
 * @returns
 */
exports.getFields = async ids => {
  return await formbuilderModel.find({ _id: { $in: ids } }).populate([
    {
      path: 'user createdBy',
      select: { fullName: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Delete Question
 *
 * @param {*} id
 * @returns
 */
exports.disableField = async id => {
  return await formbuilderModel.updateOne({ _id: toObjectId(id) }, { $set: { isActive: false } });
};

exports.getFieldByFilter = async filter => {
  return await formbuilderModel.find(filter).populate([
    {
      path: 'user createdBy',
      select: { fullName: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Update Fields
 *
 * @param {*} id
 * @param {*} fields
 * @returns
 */
exports.updateField = async (id, fields) => {
  return await formbuilderModel.updateOne({ _id: toObjectId(id) }, { $set: fields });
};
