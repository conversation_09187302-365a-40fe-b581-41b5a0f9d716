/* models */
const UserReport = require('../models/user-report.model');

/**
 * Create User Report
 *
 * @param {*} requestData
 * @param {*} session
 * @returns
 */
exports.createUserReport = async (requestData, session = null) => {
  if (session) {
    return UserReport.create([requestData], { session });
  } else {
    return UserReport.create(requestData);
  }
};

exports.updateUserReportById = async (id, updateData, session = null) => {
  return UserReport.findByIdAndUpdate(id, updateData, { new: true, session });
};

/**
 * Get User Reports
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.userReports = async (filter, page, perPage, sort) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
  ];
  if (page != null && perPage != null) {
    aggregateFunction.push(
      {
        $skip: parseInt(page) * parseInt(perPage),
      },
      {
        $limit: parseInt(perPage),
      }
    );
  }
  return UserReport.aggregate(aggregateFunction);
};

/**
 * Get User Reports Details
 *
 * @param {*} filter
 * @returns
 */
exports.userReportsDetails = async filter => {
  let aggregateFunction = [
    {
      $match: { _id: filter },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'signatureBy',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'signatureBy',
      },
    },
    {
      $unwind: {
        path: '$signatureBy',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, projectNumber: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: '$project',
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1 } }],
        as: 'location',
      },
    },
    {
      $unwind: '$location',
    },
    {
      $lookup: {
        from: 'assets',
        localField: 'asset.asset',
        foreignField: '_id',
        as: 'asset',
        pipeline: [
          {
            $project: {
              _id: 1,
              cableName: 1,
              manufacturer: 1,
              typeMm2: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'report',
        foreignField: '_id',
        pipeline: [
          {
            $match: {
              isPublish: true,
            },
          },
          { $project: { title: 1, type: 1, status: 1, isProgressable: 1 } },
        ],
        as: 'report',
      },
    },
    {
      $unwind: '$report',
    },
    {
      $project: {
        _id: 1,
        project: 1,
        location: 1,
        asset: 1,
        report: 1,
        userProjectReport: 1,
        signature: 1,
        signatureBy: 1,
        status: 1,
        createdBy: 1,
        createdAt: 1,
      },
    },
  ];
  return UserReport.aggregate(aggregateFunction);
};

/**
 * Get User Reports Questions
 *
 * @param {*} filter
 * @returns
 */
exports.userReportsQuestions = async filter => {
  let aggregateFunction = [
    {
      $match: { _id: filter },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: 'report',
        foreignField: 'report',
        let: { userReportId: '$_id' },
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $sort: { sortOrder: 1 },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              sortOrder: 1,
              isRequired: 1,
              duration: 1,
              weight: 1,
              supportedContent: 1,
              answerTypes: 1,
              userReportId: '$$userReportId',
            },
          },
          {
            $lookup: {
              from: 'report-question-answers',
              localField: '_id',
              foreignField: 'reportQuestion',
              let: { userReportId: '$userReportId' },
              pipeline: [
                {
                  $match: {
                    deletedAt: null,
                  },
                },
                {
                  $sort: { sortOrder: 1 },
                },
                {
                  $project: {
                    _id: 1,
                    title: {
                      $filter: {
                        input: '$title',
                        as: 'titleObj',
                        cond: { $eq: ['$$titleObj.isActive', true] },
                      },
                    },
                    parameterType: 1,
                    numberOfAnswers: 1,
                    option: 1,
                    range: 1,
                    userAnswers: 1,
                    userReportId: '$$userReportId',
                    sortOrder: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'parameter-types',
                    localField: 'parameterType',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $match: {
                          isActive: true,
                          deletedAt: null,
                        },
                      },
                      { $project: { _id: 1, name: 1, uniqueKey: 1 } },
                    ],
                    as: 'parameterType',
                  },
                },
                {
                  $unwind: '$parameterType',
                },
                {
                  $lookup: {
                    from: 'user-report-answers',
                    localField: '_id',
                    foreignField: 'reportQuestionAnswer',
                    let: { userReportId: '$userReportId' },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $and: [
                              { $eq: ['$userReport', '$$userReportId'] },
                              { $eq: ['$deletedAt', null] },
                            ],
                          },
                        },
                      },
                      { $project: { _id: 1, answers: 1, createdBy: 1, createdAt: 1 } },
                      {
                        $lookup: {
                          from: 'users',
                          localField: 'createdBy',
                          foreignField: '_id',
                          pipeline: [
                            { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
                          ],
                          as: 'createdBy',
                        },
                      },
                      {
                        $unwind: '$createdBy',
                      },
                    ],
                    as: 'userAnswers',
                  },
                },
              ],
              as: 'answerTypes',
            },
          },
        ],
        as: 'reportQuestions',
      },
    },
    {
      $project: {
        reportQuestions: 1,
      },
    },
  ];
  return UserReport.aggregate(aggregateFunction);
};

exports.getUserReports = async (filter, page, perPage, sort) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'signatureBy',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'signatureBy',
      },
    },
    {
      $unwind: {
        path: '$signatureBy',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, projectNumber: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: '$project',
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1 } }],
        as: 'location',
      },
    },
    {
      $unwind: '$location',
    },
    {
      $lookup: {
        from: 'assets',
        localField: 'asset',
        foreignField: '_id',
        as: 'asset',
        pipeline: [
          {
            $project: {
              _id: 1,
              cableName: 1,
              manufacturer: 1,
              typeMm2: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: 'report',
        foreignField: 'report',
        let: { userReportId: '$_id' },
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              sortOrder: 1,
              isRequired: 1,
              duration: 1,
              weight: 1,
              supportedContent: 1,
              answerTypes: 1,
              userReportId: '$$userReportId',
            },
          },
          {
            $lookup: {
              from: 'report-question-answers',
              localField: '_id',
              foreignField: 'reportQuestion',
              let: { userReportId: '$userReportId' },
              pipeline: [
                {
                  $match: {
                    deletedAt: null,
                  },
                },
                {
                  $project: {
                    _id: 1,
                    title: {
                      $filter: {
                        input: '$title',
                        as: 'titleObj',
                        cond: { $eq: ['$$titleObj.isActive', true] },
                      },
                    },
                    parameterType: 1,
                    numberOfAnswers: 1,
                    option: 1,
                    range: 1,
                    userAnswers: 1,
                    userReportId: '$$userReportId',
                  },
                },
                {
                  $lookup: {
                    from: 'parameter-types',
                    localField: 'parameterType',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $match: {
                          isActive: true,
                          deletedAt: null,
                        },
                      },
                      { $project: { _id: 1, name: 1, uniqueKey: 1 } },
                    ],
                    as: 'parameterType',
                  },
                },
                {
                  $unwind: '$parameterType',
                },
                {
                  $lookup: {
                    from: 'user-report-answers',
                    localField: '_id',
                    foreignField: 'reportQuestionAnswer',
                    let: { userReportId: '$userReportId' },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $and: [
                              { $eq: ['$userReport', '$$userReportId'] },
                              { $eq: ['$deletedAt', null] },
                            ],
                          },
                        },
                      },
                      { $project: { _id: 1, answers: 1 } },
                    ],
                    as: 'userAnswers',
                  },
                },
              ],
              as: 'answerTypes',
            },
          },
        ],
        as: 'reportQuestions',
      },
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'report',
        foreignField: '_id',
        pipeline: [
          {
            $match: {
              isPublish: true,
            },
          },
          { $project: { title: 1, type: 1, status: 1, isProgressable: 1 } },
        ],
        as: 'report',
      },
    },
    {
      $unwind: '$report',
    },
    {
      $project: {
        _id: 1,
        project: 1,
        location: 1,
        asset: 1,
        report: 1,
        reportQuestions: 1,
        signature: 1,
        signatureBy: 1,
        status: 1,
        createdBy: 1,
        createdAt: 1,
      },
    },
  ];

  if (page != null && perPage != null) {
    aggregateFunction.push(
      {
        $skip: parseInt(page) * parseInt(perPage),
      },
      {
        $limit: parseInt(perPage),
      }
    );
  }
  return UserReport.aggregate(aggregateFunction);
};

exports.searchUserReport = async filter => {
  return UserReport.findOne(filter);
};

exports.getUserReportById = async (id, session = null) => {
  if (session) {
    return UserReport.findById(id).session(session);
  }
  return UserReport.findById(id);
};

/**
 * Get Report By Filter
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.getReports = async (filter, page, perPage, sort) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $group: {
        _id: {
          report: '$report',
          location: '$location',
          asset: { $ifNull: ['$asset.asset', ''] },
          userProjectReport: { $ifNull: ['$userProjectReport', ''] },
        },
        status: { $push: '$status' },
        id: { $push: '$_id' },
        project: { $first: '$project' },
        users: { $addToSet: '$createdBy' },
        createdAt: { $last: '$createdAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
        pipeline: [{ $project: { _id: 1, title: 1, projectNumber: 1 } }],
      },
    },
    {
      $unwind: '$project',
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id.location',
        foreignField: '_id',
        as: 'location',
        pipeline: [
          { $project: { _id: 1, title: 1, deletedAt: 1, deletedBy: 1 } },
          {
            $lookup: {
              from: 'users',
              localField: 'deletedBy',
              foreignField: '_id',
              as: 'deletedBy',
              pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
            },
          },
          {
            $unwind: { path: '$deletedBy', preserveNullAndEmptyArrays: true },
          },
        ],
      },
    },
    {
      $unwind: '$location',
    },
    {
      $lookup: {
        from: 'reports',
        localField: '_id.report',
        foreignField: '_id',
        as: 'report',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$report',
    },
    {
      $lookup: {
        from: 'assets',
        localField: '_id.asset',
        foreignField: '_id',
        as: '_id.asset',
        pipeline: [
          {
            $project: {
              _id: 1,
              cableName: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$asset', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'users',
        foreignField: '_id',
        as: 'users',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
              email: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: '_id.report',
        foreignField: 'report',
        as: 'reportQuestions',
        pipeline: [
          {
            $sort: { sortOrder: 1 },
          },
          {
            $match: {
              deletedAt: null,
              duration: { $gt: 0 },
            },
          },
          { $project: { _id: 1, title: 1, deletedAt: 1 } },
        ],
      },
    },
    {
      $lookup: {
        from: 'user-report-answers',
        localField: 'reportQuestions._id',
        foreignField: 'reportQuestion',
        as: 'userReportAnswers',
        let: { idArray: '$id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  {
                    $in: ['$userReport', '$$idArray'],
                  },
                  {
                    $eq: ['$deletedAt', null],
                  },
                ],
              },
            },
          },
          {
            $addFields: {
              answers: {
                $filter: {
                  input: '$answers',
                  as: 'answer',
                  cond: { $eq: ['$$answer.isActive', true] },
                },
              },
            },
          },
          {
            $unwind: '$answers',
          },
          {
            $group: {
              _id: {
                reportQuestion: '$reportQuestion',
              },
              groupedAnswers: { $addToSet: '$answers.answerTitleId' },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'report-question-answers',
        localField: 'reportQuestions._id',
        foreignField: 'reportQuestion',
        as: 'reportQuestionAnswers',
        pipeline: [
          {
            $unwind: '$title',
          },
          {
            $match: {
              'title.isActive': true,
              deletedAt: null,
              $or: [
                { 'title.isRequired': { $exists: false } }, // Match if isRequired does not exist
                { 'title.isRequired': true }, // Match if isRequired is true
              ],
            },
          },
          {
            $group: {
              _id: {
                reportQuestion: '$reportQuestion',
              },
              groupedTitles: { $addToSet: '$title._id' },
            },
          },
        ],
      },
    },
    {
      $addFields: {
        matchedCount: {
          $map: {
            input: '$reportQuestionAnswers',
            as: 'reportQA',
            in: {
              $size: {
                $filter: {
                  input: '$userReportAnswers',
                  as: 'userRA',
                  cond: {
                    $and: [
                      { $eq: ['$$reportQA._id.reportQuestion', '$$userRA._id.reportQuestion'] },
                      { $setIsSubset: ['$$reportQA.groupedTitles', '$$userRA.groupedAnswers'] },
                    ],
                  },
                },
              },
            },
          },
        },
      },
    },
    {
      $addFields: {
        totalQuestions: { $size: '$reportQuestions' },
        totalAnsweredQuestions: {
          $reduce: {
            input: '$matchedCount',
            initialValue: 0,
            in: { $sum: ['$$value', '$$this'] },
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        id: 1,
        project: 1,
        location: '$location',
        report: '$report',
        asset: '$_id.asset',
        status: '$status',
        userProjectReport: '$_id.userProjectReport',
        users: 1,
        createdAt: 1,
        reportQuestions: 1,
        userReportAnswers: 1,
        reportQuestionAnswers: 1,
        totalQuestions: 1,
        totalAnsweredQuestions: 1,
      },
    },
  ];

  if (page != null && perPage != null) {
    aggregateFunction.push(
      {
        $skip: parseInt(page) * parseInt(perPage),
      },
      {
        $limit: parseInt(perPage),
      }
    );
  }

  return UserReport.aggregate(aggregateFunction);
};

/**
 * Get Report By Filter - v2 optimized
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.getReportsOptimized = async (filter, page, perPage, sort) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $group: {
        _id: {
          report: '$report',
          location: '$location',
          asset: { $ifNull: ['$asset.asset', ''] },
          userProjectReport: { $ifNull: ['$userProjectReport', ''] },
        },
        status: { $push: '$status' },
        id: { $push: '$_id' },
        project: { $first: '$project' },
        users: { $addToSet: '$createdBy' },
        createdAt: { $last: '$createdAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
    {
      $facet: {
        data: [
          ...(page != null && perPage != null
            ? [{ $skip: parseInt(page) * parseInt(perPage) }, { $limit: parseInt(perPage) }]
            : []),
          {
            $lookup: {
              from: 'projects',
              localField: 'project',
              foreignField: '_id',
              as: 'project',
              pipeline: [{ $project: { _id: 1, title: 1, projectNumber: 1 } }],
            },
          },
          {
            $unwind: '$project',
          },
          {
            $lookup: {
              from: 'locations',
              localField: '_id.location',
              foreignField: '_id',
              as: 'location',
              pipeline: [
                { $project: { _id: 1, title: 1, deletedAt: 1, deletedBy: 1 } },
                {
                  $lookup: {
                    from: 'users',
                    localField: 'deletedBy',
                    foreignField: '_id',
                    as: 'deletedBy',
                    pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
                  },
                },
                {
                  $unwind: { path: '$deletedBy', preserveNullAndEmptyArrays: true },
                },
              ],
            },
          },
          {
            $unwind: '$location',
          },
          {
            $lookup: {
              from: 'reports',
              localField: '_id.report',
              foreignField: '_id',
              as: 'report',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    title: 1,
                  },
                },
              ],
            },
          },
          {
            $unwind: '$report',
          },
          {
            $lookup: {
              from: 'assets',
              localField: '_id.asset',
              foreignField: '_id',
              as: '_id.asset',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    cableName: 1,
                  },
                },
              ],
            },
          },
          {
            $unwind: { path: '$asset', preserveNullAndEmptyArrays: true },
          },
          {
            $lookup: {
              from: 'users',
              localField: 'users',
              foreignField: '_id',
              as: 'users',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    callingName: 1,
                    firstName: 1,
                    lastName: 1,
                    email: 1,
                  },
                },
              ],
            },
          },
          {
            $lookup: {
              from: 'report-questions',
              localField: '_id.report',
              foreignField: 'report',
              as: 'reportQuestions',
              pipeline: [
                {
                  $sort: { sortOrder: 1 },
                },
                {
                  $match: {
                    deletedAt: null,
                    duration: { $gt: 0 },
                  },
                },
                { $project: { _id: 1, title: 1, deletedAt: 1 } },
              ],
            },
          },
          {
            $lookup: {
              from: 'user-report-answers',
              localField: 'reportQuestions._id',
              foreignField: 'reportQuestion',
              as: 'userReportAnswers',
              let: { idArray: '$id' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        {
                          $in: ['$userReport', '$$idArray'],
                        },
                        {
                          $eq: ['$deletedAt', null],
                        },
                      ],
                    },
                  },
                },
                {
                  $addFields: {
                    answers: {
                      $filter: {
                        input: '$answers',
                        as: 'answer',
                        cond: { $eq: ['$$answer.isActive', true] },
                      },
                    },
                  },
                },
                {
                  $unwind: '$answers',
                },
                {
                  $group: {
                    _id: {
                      reportQuestion: '$reportQuestion',
                    },
                    groupedAnswers: { $addToSet: '$answers.answerTitleId' },
                  },
                },
              ],
            },
          },
          {
            $lookup: {
              from: 'report-question-answers',
              localField: 'reportQuestions._id',
              foreignField: 'reportQuestion',
              as: 'reportQuestionAnswers',
              pipeline: [
                {
                  $unwind: '$title',
                },
                {
                  $match: {
                    'title.isActive': true,
                    deletedAt: null,
                    $or: [{ 'title.isRequired': { $exists: false } }, { 'title.isRequired': true }],
                  },
                },
                {
                  $group: {
                    _id: {
                      reportQuestion: '$reportQuestion',
                    },
                    groupedTitles: { $addToSet: '$title._id' },
                  },
                },
              ],
            },
          },
          {
            $addFields: {
              matchedCount: {
                $map: {
                  input: '$reportQuestionAnswers',
                  as: 'reportQA',
                  in: {
                    $size: {
                      $filter: {
                        input: '$userReportAnswers',
                        as: 'userRA',
                        cond: {
                          $and: [
                            {
                              $eq: ['$$reportQA._id.reportQuestion', '$$userRA._id.reportQuestion'],
                            },
                            {
                              $setIsSubset: ['$$reportQA.groupedTitles', '$$userRA.groupedAnswers'],
                            },
                          ],
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          {
            $addFields: {
              totalQuestions: { $size: '$reportQuestions' },
              totalAnsweredQuestions: {
                $reduce: {
                  input: '$matchedCount',
                  initialValue: 0,
                  in: { $sum: ['$$value', '$$this'] },
                },
              },
            },
          },
          {
            $project: {
              _id: 0,
              id: 1,
              project: 1,
              location: '$location',
              report: '$report',
              asset: '$_id.asset',
              status: '$status',
              userProjectReport: '$_id.userProjectReport',
              users: 1,
              createdAt: 1,
              reportQuestions: 1,
              userReportAnswers: 1,
              reportQuestionAnswers: 1,
              totalQuestions: 1,
              totalAnsweredQuestions: 1,
            },
          },
        ],
        totalCount: [{ $count: 'count' }],
      },
    },
  ];

  const result = await UserReport.aggregate(aggregateFunction);

  return {
    data: result[0].data,
    totalCount: result[0].totalCount[0]?.count || 0,
  };
};

exports.getReportsForCompletion = async (filter, page, perPage, sort) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $group: {
        _id: {
          report: '$report',
          location: '$location',
          asset: { $ifNull: ['$asset.asset', ''] },
          userProjectReport: { $ifNull: ['$userProjectReport', ''] },
        },
        status: { $push: '$status' },
        id: { $push: '$_id' },
        project: { $first: '$project' },
        users: { $addToSet: '$createdBy' },
        createdAt: { $last: '$createdAt' },
        updatedAt: { $last: '$updatedAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
        pipeline: [{ $project: { _id: 1, title: 1, projectNumber: 1 } }],
      },
    },
    {
      $unwind: '$project',
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id.location',
        foreignField: '_id',
        as: 'location',
        pipeline: [{ $project: { _id: 1, title: 1 } }],
      },
    },
    {
      $unwind: '$location',
    },
    {
      $lookup: {
        from: 'scopes',
        localField: '_id.report',
        foreignField: 'reports',
        as: 'scopes',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$scopes',
    },
    {
      $lookup: {
        from: 'reports',
        localField: '_id.report',
        foreignField: '_id',
        as: 'report',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              type: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$report',
    },
    {
      $lookup: {
        from: 'assets',
        localField: '_id',
        foreignField: '_id',
        as: '_id.asset',
        pipeline: [
          {
            $project: {
              _id: 1,
              cableName: 1,
              fromLocation: 1,
              toLocation: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id.asset.fromLocation',
        foreignField: '_id',
        as: 'assetFromLocation',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$assetFromLocation', preserveNullAndEmptyArrays: true },
    },
    {
      $addFields: {
        '_id.asset.fromLocation': '$assetFromLocation.title',
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id.asset.toLocation',
        foreignField: '_id',
        as: 'assetToLocation',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: { path: '$assetToLocation', preserveNullAndEmptyArrays: true },
    },
    {
      $addFields: {
        '_id.asset.toLocation': '$assetToLocation.title',
      },
    },
    {
      $unwind: { path: '$asset', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'users',
        foreignField: '_id',
        as: 'users',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
              email: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: '_id.report',
        foreignField: 'report',
        as: 'reportQuestions',
        pipeline: [
          {
            $match: {
              deletedAt: null,
              duration: { $gt: 0 },
            },
          },
          { $project: { _id: 1, title: 1, duration: 1, deletedAt: 1 } },
        ],
      },
    },
    {
      $lookup: {
        from: 'user-report-answers',
        localField: 'reportQuestions._id',
        foreignField: 'reportQuestion',
        as: 'userReportAnswers',
        let: { idArray: '$id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $in: ['$userReport', '$$idArray'],
              },
            },
          },
          {
            $addFields: {
              answers: {
                $filter: {
                  input: '$answers',
                  as: 'answer',
                  cond: { $eq: ['$$answer.isActive', true] },
                },
              },
            },
          },
          {
            $unwind: '$answers',
          },
          {
            $group: {
              _id: {
                reportQuestion: '$reportQuestion',
              },
              groupedAnswers: { $addToSet: '$answers.answerTitleId' },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'report-question-answers',
        localField: 'reportQuestions._id',
        foreignField: 'reportQuestion',
        as: 'reportQuestionAnswers',
        pipeline: [
          {
            $sort: { createdAt: 1 },
          },
          {
            $unwind: '$title',
          },
          {
            $match: {
              'title.isActive': true,
              deletedAt: null,
              $or: [
                { 'title.isRequired': { $exists: false } }, // Match if isRequired does not exist
                { 'title.isRequired': true }, // Match if isRequired is true
              ],
            },
          },
          {
            $group: {
              _id: {
                reportQuestion: '$reportQuestion',
              },
              groupedTitles: { $addToSet: '$title._id' },
            },
          },
        ],
      },
    },
    {
      $addFields: {
        matchedCount: {
          $map: {
            input: '$reportQuestionAnswers',
            as: 'reportQA',
            in: {
              count: {
                $size: {
                  $filter: {
                    input: '$userReportAnswers',
                    as: 'userRA',
                    cond: {
                      $and: [
                        { $eq: ['$$reportQA._id.reportQuestion', '$$userRA._id.reportQuestion'] },
                        { $setIsSubset: ['$$reportQA.groupedTitles', '$$userRA.groupedAnswers'] },
                      ],
                    },
                  },
                },
              },
              duration: {
                $first: {
                  $filter: {
                    input: '$reportQuestions',
                    as: 'rq',
                    cond: { $eq: ['$$rq._id', '$$reportQA._id.reportQuestion'] },
                  },
                },
              },
            },
          },
        },
      },
    },
    {
      $addFields: {
        totalQuestions: { $size: '$reportQuestions' },
        totalAnsweredQuestions: {
          $reduce: {
            input: '$matchedCount',
            initialValue: 0,
            in: { $sum: ['$$value', '$$this.count'] },
          },
        },
        totalDuration: {
          $sum: '$reportQuestions.duration',
        },
        totalAnsweredDuration: {
          $reduce: {
            input: '$matchedCount',
            initialValue: 0,
            in: {
              $cond: [
                { $gt: ['$$this.count', 0] },
                { $sum: ['$$value', '$$this.duration.duration'] },
                '$$value',
              ],
            },
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        id: 1,
        location: '$location',
        report: '$report',
        asset: '$_id.asset',
        userProjectReport: '$_id.userProjectReport',
        status: '$status',
        scopes: 1,
        users: 1,
        createdAt: 1,
        updatedAt: 1,
        totalQuestions: 1,
        totalAnsweredQuestions: 1,
        totalDuration: 1,
        totalAnsweredDuration: 1,
      },
    },
  ];

  if (page != null && perPage != null) {
    aggregateFunction.push(
      {
        $skip: parseInt(page) * parseInt(perPage),
      },
      {
        $limit: parseInt(perPage),
      }
    );
  }

  return UserReport.aggregate(aggregateFunction);
};

exports.getUserReportDetails = async (filter, userReportIds, reportPdf = false) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        as: 'createdBy',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'signatureBy',
        foreignField: '_id',
        as: 'signatureBy',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
      },
    },
    {
      $unwind: {
        path: '$signatureBy',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
        pipeline: [{ $project: { _id: 1, title: 1, projectNumber: 1 } }],
      },
    },
    {
      $unwind: '$project',
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        pipeline: [
          { $project: { _id: 1, title: 1, deletedAt: 1, deletedBy: 1 } },
          {
            $lookup: {
              from: 'users',
              localField: 'deletedBy',
              foreignField: '_id',
              as: 'deletedBy',
              pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
            },
          },
          {
            $unwind: { path: '$deletedBy', preserveNullAndEmptyArrays: true },
          },
        ],
        as: 'location',
      },
    },
    {
      $unwind: '$location',
    },
    {
      $lookup: {
        from: 'assets',
        localField: 'asset.asset',
        foreignField: '_id',
        as: 'asset',
        pipeline: [
          {
            $project: {
              _id: 1,
              cableName: 1,
              manufacturer: 1,
              typeMm2: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: 'report',
        foreignField: 'report',
        pipeline: [
          {
            $sort: {
              createdAt: 1,
            },
          },
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              sortOrder: 1,
              duration: 1,
              weight: 1,
              supportedContent: 1,
              answerTypes: 1,
            },
          },
          {
            $lookup: {
              from: 'report-question-answers',
              localField: '_id',
              foreignField: 'reportQuestion',
              pipeline: [
                {
                  $match: {
                    deletedAt: null,
                  },
                },
                {
                  $sort: { sortOrder: 1 },
                },
                {
                  $project: {
                    _id: 1,
                    title: 1,
                    parameterType: 1,
                    numberOfAnswers: 1,
                    option: 1,
                    range: 1,
                    userAnswers: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'parameter-types',
                    localField: 'parameterType',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $match: {
                          isActive: true,
                          deletedAt: null,
                        },
                      },
                      { $project: { _id: 1, name: 1, uniqueKey: 1 } },
                    ],
                    as: 'parameterType',
                  },
                },
                {
                  $unwind: '$parameterType',
                },
                {
                  $lookup: {
                    from: 'user-report-answers',
                    localField: '_id',
                    foreignField: 'reportQuestionAnswer',
                    pipeline: [
                      {
                        $match: {
                          userReport: {
                            $in: userReportIds,
                          },
                          deletedAt: null,
                        },
                      },
                      {
                        $project: {
                          _id: 1,
                          reportQuestion: 1,
                          reportQuestionAnswer: 1,
                          userReport: 1,
                          answers: reportPdf
                            ? {
                                $filter: {
                                  input: '$answers',
                                  as: 'answer',
                                  cond: { $eq: ['$$answer.isActive', true] },
                                },
                              }
                            : 1,
                          createdBy: 1,
                          createdAt: 1,
                          updatedBy: 1,
                          updatedAt: 1,
                        },
                      },
                      {
                        $lookup: {
                          from: 'users',
                          localField: 'createdBy',
                          foreignField: '_id',
                          as: 'createdBy',
                          pipeline: [
                            { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
                          ],
                        },
                      },
                      {
                        $unwind: {
                          path: '$createdBy',
                          preserveNullAndEmptyArrays: true,
                        },
                      },
                      {
                        $lookup: {
                          from: 'users',
                          localField: 'updatedBy',
                          foreignField: '_id',
                          as: 'updatedBy',
                          pipeline: [
                            { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } },
                          ],
                        },
                      },
                      {
                        $unwind: {
                          path: '$updatedBy',
                          preserveNullAndEmptyArrays: true,
                        },
                      },
                    ],
                    as: 'userAnswers',
                  },
                },
              ],
              as: 'answerTypes',
            },
          },
        ],
        as: 'reportQuestions',
      },
    },
    {
      $unwind: '$reportQuestions',
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'report',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, type: 1, status: 1, isProgressable: 1 } }],
        as: 'report',
      },
    },
    {
      $unwind: '$report',
    },
    {
      $group: {
        _id: {
          questionId: '$reportQuestions._id',
          title: '$reportQuestions.title',
          sortOrder: '$reportQuestions.sortOrder',
          duration: '$reportQuestions.duration',
          weight: '$reportQuestions.weight',
          supportedContent: '$reportQuestions.supportedContent',
          answerTypes: '$reportQuestions.answerTypes',
        },
        location: { $first: '$location' },
        asset: { $first: '$asset' },
        report: { $first: '$report' },
        project: { $first: '$project' },
        userProjectReport: { $first: '$userProjectReport' },
        userReportStatus: {
          $push: {
            userReportId: '$_id',
            status: '$status',
            signature: '$signature',
            signatureBy: '$signatureBy',
            createdAt: '$createdAt',
            createdBy: '$createdBy',
            updatedAt: '$updatedAt',
          },
        },
      },
    },
    {
      $group: {
        _id: {
          report: '$report',
          project: '$project',
          location: '$location',
          asset: '$asset',
          userReportStatus: '$userReportStatus',
          userProjectReport: '$userProjectReport',
        },
        reportQuestions: {
          $push: {
            questionId: '$_id.questionId',
            title: '$_id.title',
            sortOrder: '$_id.sortOrder',
            duration: '$_id.duration',
            weight: '$_id.weight',
            supportedContent: '$_id.supportedContent',
            answerTypes: '$_id.answerTypes',
          },
        },
      },
    },
    { $unwind: '$reportQuestions' }, // Unwind the array
    { $sort: { 'reportQuestions.sortOrder': 1 } }, // Sort by sortOrder
    {
      $group: {
        _id: '$_id',
        reportQuestions: {
          $push: {
            questionId: '$reportQuestions.questionId',
            title: '$reportQuestions.title',
            sortOrder: '$reportQuestions.sortOrder',
            duration: '$reportQuestions.duration',
            weight: '$reportQuestions.weight',
            supportedContent: '$reportQuestions.supportedContent',
            answerTypes: '$reportQuestions.answerTypes',
          },
        },
      },
    },
  ];

  return UserReport.aggregate(aggregateFunction);
};

/**
 * Delete Report
 *
 * @param {*} id
 * @param {*} updateData
 * @returns
 */

exports.deleteReportByIds = async (id, deletedAt) => {
  return UserReport.updateMany({ _id: { $in: id } }, { $set: deletedAt }, { multi: true });
};

exports.getUserReportByFilter = async filter => {
  return UserReport.find(filter);
};

/**
 * Get All User Reports With Answers
 *
 * @param {*} filter
 * @returns
 */
exports.getAllUserReportsWithAnswers = async filter => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        as: 'createdBy',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'signatureBy',
        foreignField: '_id',
        as: 'signatureBy',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
      },
    },
    {
      $unwind: {
        path: '$signatureBy',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
        pipeline: [{ $project: { _id: 1, title: 1, projectNumber: 1 } }],
      },
    },
    {
      $unwind: '$project',
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1 } }],
        as: 'location',
      },
    },
    {
      $unwind: '$location',
    },
    {
      $lookup: {
        from: 'assets',
        localField: 'asset.asset',
        foreignField: '_id',
        as: 'asset',
        pipeline: [
          {
            $project: {
              _id: 1,
              cableName: 1,
              manufacturer: 1,
              typeMm2: 1,
            },
          },
        ],
      },
    },
    {
      $group: {
        _id: {
          userProjectReport: '$userProjectReport',
        },
        userReportIds: {
          $push: '$_id',
        },
        report: { $first: '$report' },
        project: { $first: '$project' },
        location: { $first: '$location' },
        asset: { $first: '$asset' },
      },
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: 'report',
        foreignField: 'report',
        let: { userReportIds: '$userReportIds' },
        pipeline: [
          {
            $sort: {
              createdAt: 1,
            },
          },
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              sortOrder: 1,
              duration: 1,
              weight: 1,
              supportedContent: 1,
              answerTypes: 1,
              userReportIds: '$$userReportIds',
            },
          },
          {
            $lookup: {
              from: 'report-question-answers',
              localField: '_id',
              foreignField: 'reportQuestion',
              let: { userReportIds: '$userReportIds' },
              pipeline: [
                {
                  $match: {
                    deletedAt: null,
                  },
                },
                {
                  $project: {
                    _id: 1,
                    title: 1,
                    parameterType: 1,
                    numberOfAnswers: 1,
                    option: 1,
                    range: 1,
                    userAnswers: 1,
                    userReportIds: '$$userReportIds',
                  },
                },
                {
                  $lookup: {
                    from: 'parameter-types',
                    localField: 'parameterType',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $match: {
                          isActive: true,
                          deletedAt: null,
                        },
                      },
                      { $project: { _id: 1, name: 1, uniqueKey: 1 } },
                    ],
                    as: 'parameterType',
                  },
                },
                {
                  $unwind: '$parameterType',
                },
                {
                  $lookup: {
                    from: 'user-report-answers',
                    localField: '_id',
                    foreignField: 'reportQuestionAnswer',
                    let: { userReportIds: '$userReportIds' },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $and: [
                              {
                                $in: [
                                  '$userReport',
                                  {
                                    $map: {
                                      input: '$$userReportIds',
                                      as: 'id',
                                      in: { $toObjectId: '$$id' },
                                    },
                                  },
                                ],
                              },
                              { $eq: ['$deletedAt', null] },
                            ],
                          },
                        },
                      },
                      {
                        $project: {
                          _id: 1,
                          userReport: 1,
                          answers: 1,
                          createdBy: 1,
                          createdAt: 1,
                          updatedBy: 1,
                          updatedAt: 1,
                        },
                      },
                      {
                        $lookup: {
                          from: 'users',
                          localField: 'createdBy',
                          foreignField: '_id',
                          as: 'createdBy',
                          pipeline: [
                            {
                              $lookup: {
                                from: 'roles',
                                localField: 'role',
                                foreignField: '_id',
                                as: 'role',
                              },
                            },
                            { $unwind: { path: '$role', preserveNullAndEmptyArrays: true } },
                            {
                              $project: {
                                _id: 1,
                                callingName: 1,
                                firstName: 1,
                                lastName: 1,
                                'role._id': 1,
                                'role.title': 1,
                              },
                            },
                          ],
                        },
                      },
                      { $unwind: { path: '$createdBy', preserveNullAndEmptyArrays: true } },
                      {
                        $lookup: {
                          from: 'users',
                          localField: 'updatedBy',
                          foreignField: '_id',
                          as: 'updatedBy',
                          pipeline: [
                            {
                              $lookup: {
                                from: 'roles',
                                localField: 'role',
                                foreignField: '_id',
                                as: 'role',
                              },
                            },
                            { $unwind: { path: '$role', preserveNullAndEmptyArrays: true } },
                            {
                              $project: {
                                _id: 1,
                                callingName: 1,
                                firstName: 1,
                                lastName: 1,
                                'role._id': 1,
                                'role.title': 1,
                              },
                            },
                          ],
                        },
                      },
                      { $unwind: { path: '$updatedBy', preserveNullAndEmptyArrays: true } },
                    ],
                    as: 'userAnswers',
                  },
                },
              ],
              as: 'answerTypes',
            },
          },
        ],
        as: 'reportQuestions',
      },
    },
    {
      $unwind: '$reportQuestions',
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'report',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, type: 1, status: 1, isProgressable: 1 } }],
        as: 'report',
      },
    },
    {
      $unwind: '$report',
    },
    {
      $group: {
        _id: {
          questionId: '$reportQuestions._id',
          title: '$reportQuestions.title',
          sortOrder: '$reportQuestions.sortOrder',
          duration: '$reportQuestions.duration',
          weight: '$reportQuestions.weight',
          supportedContent: '$reportQuestions.supportedContent',
          answerTypes: '$reportQuestions.answerTypes',
        },
        location: { $first: '$location' },
        asset: { $first: '$asset' },
        report: { $first: '$report' },
        project: { $first: '$project' },
        userProjectReport: { $first: '$_id.userProjectReport' },
        userReportStatus: {
          $push: {
            userReportId: '$_id',
            status: '$status',
            signature: '$signature',
            signatureBy: '$signatureBy',
            createdAt: '$createdAt',
            createdBy: '$createdBy',
            updatedAt: '$updatedAt',
          },
        },
      },
    },
    {
      $group: {
        _id: {
          report: '$report',
          project: '$project',
          location: '$location',
          asset: '$asset',
          userProjectReport: '$userProjectReport',
        },
        reportQuestions: {
          $push: {
            questionId: '$_id.questionId',
            title: '$_id.title',
            sortOrder: '$_id.sortOrder',
            duration: '$_id.duration',
            weight: '$_id.weight',
            supportedContent: '$_id.supportedContent',
            answerTypes: '$_id.answerTypes',
          },
        },
      },
    },
    { $unwind: '$reportQuestions' }, // Unwind the array
    { $sort: { 'reportQuestions.sortOrder': 1 } }, // Sort by sortOrder
    {
      $group: {
        _id: '$_id',
        reportQuestions: {
          $push: {
            questionId: '$reportQuestions.questionId',
            title: '$reportQuestions.title',
            sortOrder: '$reportQuestions.sortOrder',
            duration: '$reportQuestions.duration',
            weight: '$reportQuestions.weight',
            supportedContent: '$reportQuestions.supportedContent',
            answerTypes: '$reportQuestions.answerTypes',
          },
        },
      },
    },
  ];

  return UserReport.aggregate(aggregateFunction);
};
