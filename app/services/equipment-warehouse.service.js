const EquipmentWarehouse = require('../models/equipment-warehouse.model');

/**
 * Create EquipmentWarehouse
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipmentWarehouse = async requestData => {
  return await EquipmentWarehouse.create(requestData);
};

/**
 * Filter EquipmentWarehouse
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getEquipmentWarehouse = async (filter, perPage, page, sort) => {
  return await EquipmentWarehouse.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort({ createdAt: sort })
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'equipment',
        select: { _id: 1, name: 1 },
      },
      {
        path: 'warehouse',
        select: { _id: 1, name: 1 },
      },
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'deletedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]);
};

/**
 * Get EquipmentWarehouse by Filter
 *
 * @param {*} id
 * @returns
 */
exports.getEquipmentWarehouseByFilter = async filter => {
  filter.deletedAt = null;
  filter.isActive = true;
  return await EquipmentWarehouse.findOne(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  }).populate([
    {
      path: 'equipment',
      select: { _id: 1, name: 1 },
    },
    {
      path: 'warehouse',
      select: { _id: 1, name: 1 },
    },
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Update EquipmentWarehouse
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateEquipmentWarehouse = async (id, requestData) => {
  return await EquipmentWarehouse.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Delete EquipmentWarehouse
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteEquipmentWarehouse = async (id, deletedAt) => {
  return await EquipmentWarehouse.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};
