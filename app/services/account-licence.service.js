const AccountLicence = require('../models/account-licence.model');
const RoleAgreement = require('../models/role-agreement.model');
const Role = require('../models/role.model');

/**
 * Create AccountLicence
 * @param {*} requestData
 * @returns
 */
exports.createAccountLicence = async requestData => {
  return await AccountLicence.create(requestData);
};

/**
 * Get data by account and permission ids
 * @param {*} account
 * @param {*} permission
 * @returns
 */
exports.getAccountLicence = async (account, permission) => {
  return await AccountLicence.findOne({ account, permission });
};

/**
 * Get all records by account id
 * @param {*} account
 * @param {*} fileds
 * @returns
 */
exports.getAccountLicenceByAccountId = async (filter, fileds) => {
  return await AccountLicence.find(filter).select(fileds);
};

/**
 * Get all account licence data
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getAllAccountLicence = async (page, perPage, filter = {}) => {
  return await AccountLicence.find(filter, { __v: 0, updatedAt: 0 })
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'licence',
        select: 'name',
      },
      {
        path: 'account',
        select: { name: 1, logo: 1 },
        populate: {
          path: 'accountOwner',
          model: 'user',
          select: { callingName: 1, firstName: 1, lastName: 1, email: 1 },
        },
      },
      {
        path: 'permission',
        select: 'name',
      },
    ]);
};

/**
 * Find Record by Account
 *
 * @param {*} account
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getAccountLicenceByAccount = async (filter, page, perPage) => {
  return await AccountLicence.find(filter)
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'licence',
        select: 'name',
      },
      {
        path: 'account',
        select: 'name',
      },
      {
        path: 'permission',
        select: 'name',
      },
    ]);
};

/**
 * Update Account Licence
 *
 * @param {*} id
 * @param {*} update
 * @returns
 */
exports.updateAccountLicence = async (id, update) => {
  return await AccountLicence.findByIdAndUpdate(id, update, { new: true });
};

/**
 * Request for account licence
 *
 * @param {*} requestData
 * @returns
 */
exports.requestAccountLicence = async requestData => {
  try {
    const { account, permission } = requestData;

    /**Checked the record, insert or update as per accountLicenceData  */
    permission.forEach(async element => {
      const accountLicenceData = await this.getAccountLicence(account, element);
      requestData.permission = element;
      if (accountLicenceData === null) {
        await this.createAccountLicence(requestData);
      } else {
        await this.updateAccountLicence(accountLicenceData._id, requestData);
      }
    });
    return true;
  } catch (err) {
    throw new Error(err);
  }
};

/**
 * Get all account licence
 *
 * @param {*} filter
 * @returns
 */
exports.getLicenceByAccountId = async filter => {
  return AccountLicence.find(filter);
};

/**
 * Create or modify role agreements
 *
 * @param {*} requestData
 * @param {*} userData
 */
exports.modifyRoleAgreements = async (requestData, userData) => {
  try {
    let { account, permission } = requestData;
    let allPermission = [permission];
    await this.processPermission(account, requestData, userData, allPermission);
  } catch (err) {
    throw new Error(err);
  }
};
/**
 * Processes each permission for the given account.
 *
 * @param {*} account
 * @param {*} permission
 * @param {*} requestData
 * @param {*} userData
 * @param {*} allPermission
 */

exports.processPermission = async (account, requestData, userData, allPermission) => {
  for (const key of Object.keys(allPermission)) {
    let accountLicence = await AccountLicence.find({
      account,
      permission: allPermission[key],
    });
    if (accountLicence.length > 0) {
      let roles = await Role.find({ account });
      for (const role of roles) {
        await this.processRole(account, accountLicence[0]._id, role, requestData, userData);
      }
    }
  }
};

/**
 * Processes each role for the given account and account licence.
 *
 * @param {*} account
 * @param {*} accountLicenceId
 * @param {*} role
 * @param {*} requestData
 * @param {*} userData
 */

exports.processRole = async (account, accountLicenceId, role, requestData, userData) => {
  let roleAgreement = await RoleAgreement.find({
    accountLicence: accountLicenceId,
    account,
    role: role._id,
  });

  let agreement = {
    create: false,
    read: false,
    update: false,
    delete: false,
  };

  if (
    requestData.isApproved === true &&
    requestData.isRejected === false &&
    role.title === global.constant.ADMIN_ROLE
  ) {
    agreement.create = true;
    agreement.read = true;
    agreement.update = true;
    agreement.delete = true;
  }

  let agreementData = {
    role: role._id,
    accountLicence: accountLicenceId,
    isActive: requestData.isApproved,
    account,
    agreement,
    createdBy: userData._id,
    updatedBy: userData._id,
  };

  if (roleAgreement.length > 0) {
    await RoleAgreement.findByIdAndUpdate(
      roleAgreement[0]._id,
      { $set: agreementData },
      { new: true }
    );
  } else {
    await RoleAgreement.create(agreementData);
  }
};
