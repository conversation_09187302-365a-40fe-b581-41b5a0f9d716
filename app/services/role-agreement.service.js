const RoleAgreement = require('../models/role-agreement.model');
const roleAgreementUtils = require('../utils/role-agreement.utils');
/**
 * Create New Role Agreement
 *
 * @param {*} req
 */
exports.createRoleAgreement = async (req, role) => {
  return await roleAgreementUtils.modifyRoleAgreement(req.userData.account, req.userData.id, role);
};

/**
 * Create New Role Agreement
 *
 * @param {*} req
 * @param {*} role
 * @returns
 */
exports.create = async (element, accountLicenceData, account) => {
  return await RoleAgreement.create({
    role: element._id,
    accountLicence: accountLicenceData._id,
    account: account._id,
    agreement: {
      create: false,
      read: false,
      update: false,
      delete: false,
    },
    createdBy: element.createdBy,
    updatedBy: element.createdBy,
  });
};

/**
 * Create Crewing Role Agreement
 *
 * @param {*} req
 * @param {*} role
 * @param {*} accountLicenceData
 * @returns
 */
exports.createCrewingRoleAgreement = async (req, role, accountLicenceData) => {
  return await roleAgreementUtils.crewingRoleAgreement(
    req.userData.account,
    req.userData.id,
    role,
    accountLicenceData
  );
};

/**
 * Update RoleAgreement
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateRoleAgreement = async (id, requestData) => {
  return await RoleAgreement.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Filter RoleAgreement
 *
 * @param {*} requestData
 * @returns
 */
exports.filterRoleAgreements = async requestData => {
  return RoleAgreement.aggregate([
    {
      $match: requestData,
    },
    {
      $lookup: {
        from: 'roles',
        localField: 'role',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, title: 1 } }],
        as: 'role',
      },
    },
    {
      $lookup: {
        from: 'accountlicences',
        localField: 'accountLicence',
        foreignField: '_id',
        pipeline: [
          {
            $project: { _id: 1, licence: 1, permission: 1 },
          },
          {
            $lookup: {
              from: 'licences',
              localField: 'licence',
              foreignField: '_id',
              pipeline: [{ $project: { _id: 1, name: 1 } }],
              as: 'licence',
            },
          },
          {
            $lookup: {
              from: 'permissions',
              localField: 'permission',
              foreignField: '_id',
              pipeline: [{ $project: { _id: 1, name: 1, sortOrder: 1 } }],
              as: 'permission',
            },
          },
        ],
        as: 'accountLicence',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $unwind: '$accountLicence',
    },
    {
      $unwind: '$account',
    },
    {
      $unwind: '$role',
    },
    {
      $sort: { 'accountLicence.permission.sortOrder': 1 },
    },
    {
      $unset: ['updatedBy', 'updatedAt', 'deletedAt', 'deletedBy', 'isActive', '__v'],
    },
  ]);
};

/**
 * Get RoleAgreement By Id
 *
 * @param {*} id
 * @returns
 */
exports.getRoleAgreementById = async id => await RoleAgreement.findById(id);

/**
 * Get role agreements by request data
 *
 * @param {*} requestData
 * @returns
 */
exports.getRoleAgreementByRequestData = async requestData => {
  return RoleAgreement.find(requestData);
};

/**
 * Get role by account
 * @param {*} account
 * @returns
 */
exports.getRoleIdOfAccount = async account => {
  return RoleAgreement.find({ account });
};

/**
 * Get All Role Agreements
 *
 * @param {*} filter
 * @returns
 */
exports.getRoleAgreements = async filter => {
  return await RoleAgreement.find(filter).populate([
    {
      path: 'roles role',
      select: { title: 1, _id: 1, isActive: 1, accessType: 1 },
      strictPopulate: false,
    },
  ]);
};
