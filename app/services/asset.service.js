const Asset = require('../models/asset.model');
const {
  accountPipelineFields,
  projectPipelineFields,
  stringPipelineFields,
  fromLocationPipelineFields,
  toLocationPipelineFields,
  toObjectId,
} = require('../utils/common.utils');

/**
 * Create Asset
 *
 * @param {*} asset
 * @returns
 */
exports.createAsset = async asset => {
  return await Asset.create(asset);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} asset
 * @returns
 */
exports.updateAsset = async (id, asset) => {
  return Asset.findByIdAndUpdate(id, { $set: asset }, { new: true }).populate([
    ...projectPipelineFields,
    ...fromLocationPipelineFields,
    ...toLocationPipelineFields,
    ...accountPipelineFields,
    ...stringPipelineFields,
  ]);
};

/**
 * Delete By Id
 *
 * @param {*} id
 * @returns
 */
exports.deleteAsset = async (id, deletedAt) => {
  return Asset.findByIdAndUpdate(id, { $set: deletedAt });
};

/**
 * Get All Asset
 *
 * @returns
 */
exports.getAllAsset = async (
  filter = {},
  page = null,
  perPage = null,
  sort = { createdAt: -1 }
) => {
  return Asset.find(filter, { createdAt: 0, updatedAt: 0, __v: 0 })
    .collation({ locale: 'en', strength: 2 })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      ...projectPipelineFields,
      ...fromLocationPipelineFields,
      ...toLocationPipelineFields,
      ...accountPipelineFields,
      ...stringPipelineFields,
    ]);
};

/**
 * Get Asset By Id
 *
 * @param {*} id
 * @returns
 */
exports.getAssetById = async id => {
  return Asset.findOne(
    { $and: [{ _id: id }, { deletedAt: null }] },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  )
    .select('_id')
    .populate([
      ...projectPipelineFields,
      ...fromLocationPipelineFields,
      ...toLocationPipelineFields,
      ...accountPipelineFields,
    ]);
};

/**
 * Get Asset By Name
 *
 * @param {*} assetName
 * @returns
 */
exports.getAssetByName = async filter => Asset.findOne(filter);

/**
 * Get All Asset By Account Id
 *
 * @param {*} filter
 * @returns
 */
exports.getAllAssetByAccountId = async filter => {
  return Asset.find(filter, { createdAt: 0, updatedAt: 0, __v: 0 }).populate([
    ...projectPipelineFields,
    ...fromLocationPipelineFields,
    ...toLocationPipelineFields,
    ...accountPipelineFields,
    ...stringPipelineFields,
  ]);
};

/**
 * Get All Asset By Account Id
 *
 * @param {*} account
 * @returns
 */
exports.getAllDefaultAssetByAccountId = async account => {
  return Asset.find(
    { $and: [{ account: account }, { isDefault: true }, { deletedAt: null }] },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  ).populate([
    ...projectPipelineFields,
    ...fromLocationPipelineFields,
    ...toLocationPipelineFields,
    ...accountPipelineFields,
    ...stringPipelineFields,
  ]);
};

/**
 * Delete All Project Asset
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectAsset = async (projectId, deletedAt) => {
  return await Asset.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

exports.createAssets = async asset => {
  return await Asset.insertMany(asset);
};

exports.pushReportInAsset = async (id, report) => {
  return Asset.findByIdAndUpdate(id, { $push: { reports: report } });
};

exports.pullReportInAsset = async (id, report) => {
  return Asset.findByIdAndUpdate(id, { $pull: { reports: report } });
};

/**
 * Get all assets by location
 * @param {*} locationId
 * @returns
 */
exports.getAssetByLocation = async locationId => {
  let filter = {
    $or: [
      {
        fromLocation: toObjectId(locationId),
      },
      {
        toLocation: toObjectId(locationId),
      },
    ],
  };
  let pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'reports',
        foreignField: '_id',
        as: 'reports',
        pipeline: [
          {
            $sort: { sortOrder: 1 },
          },
          {
            $match: {
              deletedAt: null,
              isPublish: true,
              isProgressable: true,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              type: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$reports',
    },
    {
      $lookup: {
        from: 'scopes',
        localField: 'reports._id',
        foreignField: 'reports',
        as: 'scopes',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$scopes',
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: 'reports._id',
        foreignField: 'report',
        as: 'reportQuestions',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $group: {
              _id: null,
              totalDuration: { $sum: '$duration' },
            },
          },
        ],
      },
    },
    {
      $unwind: '$reportQuestions',
    },
    {
      $addFields: {
        'reports.scopes': '$scopes',
        'reports.totalDuration': '$reportQuestions.totalDuration',
      },
    },
    {
      $group: {
        _id: '$_id',
        cableName: { $first: '$cableName' },
        fromLocation: { $first: '$fromLocation' },
        toLocation: { $first: '$toLocation' },
        reports: {
          $push: '$reports',
        },
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'fromLocation',
        foreignField: '_id',
        as: 'fromLocation',
      },
    },
    {
      $unwind: '$fromLocation',
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'toLocation',
        foreignField: '_id',
        as: 'toLocation',
      },
    },
    {
      $unwind: '$toLocation',
    },
    {
      $project: {
        _id: 1,
        cableName: 1,
        fromLocation: '$fromLocation.title',
        toLocation: '$toLocation.title',
        reports: 1,
      },
    },
  ];

  return await Asset.aggregate(pipeline);
};

/**
 * Get all assets by location by project for project tracker
 * @param {*} projectId
 * @returns
 */

exports.getAssetByProjectForProjectTracker = async filter => {
  let pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'fromLocation',
        foreignField: '_id',
        as: 'fromLocation',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$fromLocation',
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'toLocation',
        foreignField: '_id',
        as: 'toLocation',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$toLocation',
    },
    {
      $lookup: {
        from: 'reports',
        localField: 'reports',
        foreignField: '_id',
        as: 'reports',
        pipeline: [
          {
            $match: {
              deletedAt: null,
              isPublish: true,
              isProgressable: true,
            },
          },
          {
            $project: {
              _id: 1,
              title: 1,
              type: 1,
              createdAt: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$reports',
    },
    {
      $lookup: {
        from: 'scopes',
        localField: 'reports._id',
        foreignField: 'reports',
        as: 'scopes',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$scopes',
    },
    {
      $lookup: {
        from: 'report-questions',
        localField: 'reports._id',
        foreignField: 'report',
        as: 'reportQuestions',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $group: {
              _id: null,
              totalDuration: { $sum: '$duration' },
            },
          },
        ],
      },
    },
    {
      $unwind: '$reportQuestions',
    },
    {
      $addFields: {
        'reports.scopes': '$scopes',
        'reports.totalDuration': '$reportQuestions.totalDuration',
      },
    },
    {
      $group: {
        _id: '$_id',
        cableName: { $first: '$cableName' },
        fromLocation: { $first: '$fromLocation' },
        toLocation: { $first: '$toLocation' },
        reports: {
          $push: '$reports',
        },
        createdAt: { $first: '$createdAt' },
      },
    },
    {
      $sort: { createdAt: 1 },
    },
    {
      $project: {
        _id: 1,
        cableName: 1,
        fromLocation: 1,
        toLocation: 1,
        reports: 1,
      },
    },
  ];

  return await Asset.aggregate(pipeline);
};
