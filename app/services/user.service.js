/* models */
const User = require('../models/user.model');
const accountService = require('../services/account.service');
const roleService = require('../services/role.service');
const memeberService = require('../services/member.service');

// utils
const { toObjectId, isValidId } = require('../utils/common.utils');
const commonUtils = require('../utils/common.utils');

/* create user */
exports.createUser = async user => {
  return await User.create(user);
};

/* get by email */
exports.getByEmail = async email => {
  return User.findOne({
    email: { $eq: email },
    isDeleted: false,
  });
};

/* get by email */
exports.getByResourceNumber = async resourceNumber => {
  return User.findOne({
    resourceNumber: { $eq: resourceNumber },
    isDeleted: false,
  });
};

/**
 * Update User
 *
 * @param {*} userId
 * @param {*} account
 * @returns
 */
exports.updateUserById = async (userId, updateUser) => {
  return await User.findOneAndUpdate({ _id: userId }, { $set: updateUser });
};

/**
 * Get User By Id
 *
 * @param {*} userId
 * @returns
 */
exports.getUserById = async (userId, type) => {
  const pipeline = [
    {
      $match: {
        _id: toObjectId(userId),
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1, _id: 0 } }],
        as: 'createdBy',
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [
          {
            $project: { name: 1, _id: 1, logo: 1, organizationAddress: 1, organizationCountry: 1 },
          },
        ],
        as: 'account',
      },
    },
    {
      $lookup: {
        from: 'roles',
        localField: 'role',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, _id: 1, isActive: 1, accessType: 1 } }],
        as: 'role',
      },
    },
    {
      $lookup: {
        from: 'contractual-details',
        localField: 'contractualDetailId',
        foreignField: '_id',
        pipeline: [
          {
            $project: { userId: 0, createdBy: 0, createdAt: 0, updatedAt: 0, __v: 0 },
          },
          {
            $addFields: {
              birthDate: { $ifNull: ['$birthDate', ''] },
            },
          },
        ],
        as: 'contractualDetail',
      },
    },
    {
      $lookup: {
        from: 'nextofkins',
        localField: '_id',
        foreignField: 'user',
        pipeline: [
          {
            $project: {
              user: 0,
              createdBy: 0,
              createdAt: 0,
              updatedAt: 0,
              __v: 0,
              updatedBy: 0,
              deletedBy: 0,
              deletedAt: 0,
            },
          },
        ],
        as: 'nextOfKin',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'rating.ratedBy',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              callingName: 1,
              firstName: 1,
              lastName: 1,
              profileImage: 1,
            },
          },
        ],
        as: 'userDetails',
      },
    },
    {
      $addFields: {
        rating: {
          $map: {
            input: '$rating',
            as: 'ratingItem',
            in: {
              $mergeObjects: [
                '$$ratingItem',
                {
                  ratedByDetails: {
                    $arrayElemAt: [
                      {
                        $filter: {
                          input: '$userDetails',
                          as: 'user',
                          cond: { $eq: ['$$user._id', '$$ratingItem.ratedBy'] },
                        },
                      },
                      0,
                    ],
                  },
                },
              ],
            },
          },
        },
      },
    },
    {
      $addFields: {
        rating: {
          $sortArray: {
            input: '$rating',
            sortBy: { ratedAt: -1 }, // Ensure the `rating` array is sorted by `ratedAt`
          },
        },
      },
    },
  ];
  if (type === 'mobile') {
    pipeline.push({
      $lookup: {
        from: 'upload-certificates',
        localField: '_id',
        foreignField: 'user',
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [{ $eq: ['$deletedAt', null] }, { $eq: ['$internal', false] }],
              },
            },
          },

          {
            $lookup: {
              from: 'certificate-types',
              localField: 'certificateType',
              foreignField: '_id',
              as: 'certificateType',
              pipeline: [
                {
                  $project: { _id: 1, name: 1, validityDate: 1 },
                },
              ],
            },
          },
          {
            $unwind: '$certificateType',
          },
          {
            $project: {
              _id: 1,
              certificateType: 1,
              link: 1,
              fileName: 1,
              size: 1,
              name: 1,
              status: 1,
              startDate: 1,
              endDate: 1,
              isActive: 1,
              version: 1,
            },
          },
        ],
        as: 'userCertificate',
      },
    });
  } else {
    pipeline.push({
      $lookup: {
        from: 'upload-certificates',
        localField: '_id',
        foreignField: 'user',
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$deletedAt', null],
              },
            },
          },
          {
            $lookup: {
              from: 'certificate-types',
              localField: 'certificateType',
              foreignField: '_id',
              as: 'certificateType',
              pipeline: [
                {
                  $project: { _id: 1, name: 1, validityDate: 1, deletedAt: 1 },
                },
              ],
            },
          },
          {
            $unwind: '$certificateType',
          },
          {
            $project: {
              _id: 1,
              certificateType: 1,
              link: 1,
              fileName: 1,
              size: 1,
              name: 1,
              status: 1,
              startDate: 1,
              endDate: 1,
              isActive: 1,
              version: 1,
              internal: 1,
            },
          },
        ],
        as: 'userCertificate',
      },
    });
  }
  pipeline.push(
    {
      $lookup: {
        from: 'questions',
        localField: 'account',
        foreignField: 'account',
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [{ $eq: ['$isPublished', true] }, { $eq: ['$deletedAt', null] }],
              },
            },
          },
          {
            $project: {
              questionId: '$_id',
              title: '$title',
              description: '',
              _id: 0,
            },
          },
        ],
        as: 'questions',
      },
    },
    {
      $lookup: {
        from: 'profile-functions',
        localField: 'profileFunction',
        foreignField: '_id',
        as: 'profileFunction',
      },
    },
    {
      $unwind: {
        path: '$profileFunction',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        isAdmin: {
          $in: ['$role.title', [global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE]],
        },
      },
    },
    {
      $addFields: {
        profileFunction: {
          $cond: {
            if: '$isAdmin',
            then: '$$REMOVE',
            else: '$profileFunction',
          },
        },
      },
    },
    {
      $unset: ['isAdmin'],
    },
    {
      $unwind: { path: '$createdBy', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$account', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$contractualDetail', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$role', preserveNullAndEmptyArrays: true },
    },
    {
      $addFields: {
        account: { $ifNull: ['$account', {}] },
        contractualDetail: { $ifNull: ['$contractualDetail', {}] },
        nextOfKin: { $ifNull: ['$nextOfKin', {}] },
        medical: {
          $cond: {
            if: {
              $eq: ['$medical', []],
            },
            then: '$questions',
            else: '$medical',
          },
        },
        rating: { $ifNull: ['$rating', []] },
      },
    },
    {
      $unset: [
        '__v',
        'createdAt',
        'updatedBy',
        'updatedAt',
        'password',
        '__id',
        'resetExpiresIn',
        'resetToken',
        'certificateId',
        'contractualDetailId',
        'questions',
        'medical._id',
      ],
    },
    { $limit: 1 }
  );
  return await User.aggregate(pipeline);
};

/**
 * Delete By Id
 *
 * @param {*} userId
 * @returns
 */
exports.deleteUser = async userId => {
  return User.findByIdAndDelete({ _id: userId });
};

/**
 * Update By User Id
 *
 * @param {*} id
 * @param {*} userData
 * @returns
 */
exports.updateUser = async (id, userData) => {
  return User.findByIdAndUpdate(id, { $set: userData }, { new: true });
};

/**
 * Get All Users
 *
 * @param {*} page
 * @param {*} perPage
 * @param {*} account
 * @returns
 */
exports.getAllUsers = async (page, perPage, filter, assignedProject = '') => {
  const isAdminUser = await accountService.findAccountById(filter.account);

  filter.email = { $ne: isAdminUser.email };

  let usersQuery = User.find({
    $and: [filter],
  })
    .populate([
      {
        path: 'roles role',
        select: { title: 1, _id: 1, allProject: 1, isActive: 1, accessType: 1 },
        strictPopulate: false,
      },
      {
        path: 'profileFunction',
        select: { name: 1, _id: 1, isActive: 1 },
        strictPopulate: false,
      },
    ])
    .sort({ createdAt: -1 }) // Sort users by `createdAt` descending
    .lean();

  let usersData = await usersQuery.exec();

  if (assignedProject !== '') {
    // eslint-disable-next-line no-undef
    usersData = await Promise.all(
      usersData.map(async user => {
        const userProject = await memeberService.getMemberByUserId({
          project: commonUtils.toObjectId(assignedProject),
          user: commonUtils.toObjectId(user._id),
          deletedAt: null,
        });

        const projectDetails = userProject?.[0]?.projectDetails || [];

        return {
          ...user,
          projectDetails: projectDetails,
        };
      })
    );

    usersData = usersData.filter(
      user =>
        Array.isArray(user.projectDetails) &&
        user.projectDetails.some(
          project => project._id?.toString() === toObjectId(assignedProject).toString()
        )
    );
  }

  // Filter and sort the `rating` array within each user
  usersData.forEach(user => {
    if (user.rating) {
      user.rating = user.rating
        .filter(rating => !rating.isDeleted)
        .sort((a, b) => new Date(b.ratedAt) - new Date(a.ratedAt)); // Sort ratings by `ratedAt` descending
    }
  });

  return usersData;
};

/**
 * Change user status to inactive
 *
 * @param {*} req
 * @param {*} id
 * @returns
 */
exports.changeStatus = async user => {
  return User.findByIdAndUpdate({ _id: user }, { $set: { isActive: false } }, { new: true });
};

/**
 * Get active users
 *
 * @param {*} user
 * @returns
 */
exports.getActiveUsers = async user => {
  return User.find({
    _id: { $in: user },
    isActive: true,
  });
};

/**
 * Get active users
 *
 * @param {*} user
 * @returns
 */
exports.getUsersByFilter = async filter => {
  return await User.find(filter);
};

/**
 * get user data
 *
 * @param {*} req
 * @param {*} id
 * @returns
 */
exports.getUserData = async id => {
  return User.findById({ _id: id }).populate([
    {
      path: 'roles role',
      select: { title: 1, _id: 1, isActive: 1, accessType: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Get by firstname, lastname , country or nationality
 *
 * @param {*} searchName
 * @returns
 */
exports.searchUser = async (req, page, perPage, assignedProject = '') => {
  let { search = '', nationality = '', country = '' } = req.query;

  const searchName = isValidId(search) ? search : search.replace(/_/g, ' ');
  const regex = isValidId(search) ? toObjectId(search) : new RegExp(searchName, 'i');

  if (nationality.includes('_')) {
    nationality = commonUtils.replaceUnderscoreWithSpace(nationality);
  }

  if (country.includes('_')) {
    country = commonUtils.replaceUnderscoreWithSpace(country);
  }

  nationality = nationality.toLowerCase() === 'all' ? '' : new RegExp(nationality, 'i');
  country = country.toLowerCase() === 'all' ? '' : new RegExp(country, 'i');
  const isAdminUser = await accountService.findAccountById(req.userData.account);

  // Build the match conditions
  const matchConditions = [];

  if ('isActive' in req.query) {
    matchConditions.push({ isActive: req.query.isActive == 'true' });
  }

  if (search) {
    if (isValidId(search)) {
      matchConditions.push({ _id: regex });
    } else {
      // Use $or for name search
      matchConditions.push({
        $or: [{ callingName: regex }, { firstName: regex }, { lastName: regex }],
      });
    }
  }

  if ('profileFunction' in req.query) {
    matchConditions.push({ profileFunction: toObjectId(req.query.profileFunction) });
  }

  // Create the pipeline
  let pipeline = [
    {
      $match: {
        // Only add $and if we have conditions
        ...(matchConditions.length > 0 ? { $and: matchConditions } : {}),
        account: req.userData.account,
        deletedAt: null,
      },
    },
    { $match: { email: { $ne: isAdminUser.email } } },
    {
      $lookup: {
        from: 'roles',
        localField: 'role',
        foreignField: '_id',
        as: 'role',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              description: 1,
              allProject: 1,
              isActive: 1,
              accessType: 1,
              accounts: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$role',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'profile-functions',
        localField: 'profileFunction',
        foreignField: '_id',
        as: 'profileFunction',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              isActive: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$profileFunction',
        preserveNullAndEmptyArrays: true,
      },
    },
  ];

  // Add nationality filter to the first $match stage
  if (nationality !== '' && nationality !== 'all') {
    pipeline[0].$match.nationality = nationality;
  }

  // Add country filter to the first $match stage
  if (country !== '' && country !== 'all') {
    pipeline[0].$match.country = country;
  }

  if (page == '' && perPage == '') {
    return User.aggregate(pipeline);
  }

  if (assignedProject !== '') {
    let usersData = await User.aggregate(pipeline);
    // eslint-disable-next-line
    usersData = await Promise.all(
      usersData.map(async user => {
        const userProject = await memeberService.getMemberByUserId({
          project: commonUtils.toObjectId(assignedProject),
          user: commonUtils.toObjectId(user._id),
          deletedAt: null,
        });

        const projectDetails = userProject?.[0]?.projectDetails || [];

        return {
          ...user,
          projectDetails: projectDetails,
        };
      })
    );

    usersData = usersData.filter(
      user =>
        Array.isArray(user.projectDetails) &&
        user.projectDetails.some(
          project => project._id?.toString() === toObjectId(assignedProject).toString()
        )
    );

    if (page !== '' && perPage !== '') {
      const pageNumber = parseInt(page, 10) || 0;
      const itemsPerPage = parseInt(perPage, 10) || 10;

      const start = pageNumber * itemsPerPage;
      const end = start + itemsPerPage;

      usersData = usersData.slice(start, end);
    }

    return usersData;
  }

  // Always add sorting
  pipeline.push({
    $sort: { createdAt: -1 },
  });

  // Get all matching records first to get accurate count
  const allMatchingRecords = await User.aggregate([...pipeline]);

  // Then add pagination if needed
  if (page !== '' && perPage !== '') {
    const pageNumber = parseInt(page, 10) || 0;
    const itemsPerPage = parseInt(perPage, 10) || 10;

    pipeline.push(
      {
        $skip: pageNumber * itemsPerPage,
      },
      {
        $limit: itemsPerPage,
      }
    );

    // Execute the paginated query
    const paginatedResults = await User.aggregate(pipeline);

    // Return both the paginated results and the total count
    return {
      data: paginatedResults,
      totalCount: allMatchingRecords.length,
    };
  }

  // If no pagination, return all records
  return {
    data: allMatchingRecords,
    totalCount: allMatchingRecords.length,
  };
};

/**
 * Get all active users
 *
 * @returns
 */
exports.getAllActiveUsers = async account => {
  const isAdminUser = await accountService.findAccountById(account);

  return User.find(
    {
      $and: [{ account: account, isActive: true, email: { $ne: isAdminUser.email } }],
    },
    { _id: 1, callingName: 1, firstName: 1, lastName: 1, role: 1 }
  )
    .populate([
      {
        path: 'roles role',
        select: { title: 1, _id: 1, allProject: 1, isActive: 1, accessType: 1 },
        strictPopulate: false,
      },
    ])
    .sort({ createdAt: -1 });
};

/**
 * Get single user
 *
 * @param {*} id
 * @param {*} account
 * @returns
 */
exports.getSingleUser = async (id, account) => {
  return User.find(
    {
      $and: [{ _id: id }, { account: account }, { isActive: true }],
    },
    { _id: 1, callingName: 1, firstName: 1, lastName: 1, role: 1 }
  );
};

/**
 * Get all active users by role
 *
 * @param {*} filter
 * @returns
 */
exports.getAllActiveUsersByRole = async filter => {
  const roleId = await roleService.getRoleByName(filter.role, filter.account);
  filter.role = roleId;
  return User.find(filter, { _id: 1, callingName: 1, firstName: 1, lastName: 1, role: 1 })
    .populate([
      {
        path: 'roles role',
        select: { title: 1, _id: 1, allProject: 1, isActive: 1, accessType: 1 },
        strictPopulate: false,
      },
      {
        path: 'profileFunction',
        select: { name: 1, _id: 1, isActive: 1 },
        strictPopulate: false,
      },
    ])
    .sort({ createdAt: -1 });
};

/**
 * Get user profile functions
 *
 * @param {*} filter
 * @returns
 */
exports.getUserProfileFunction = async filter => {
  return User.find(filter);
};

/**
 * Get all admin role
 *
 * @param {*} filter
 * @returns
 */
exports.getAllAdminRole = async filter => {
  return await User.find(filter).populate([
    {
      path: 'roles role',
      select: { title: 1, _id: 1, allProject: 1, isActive: 1, accessType: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Update user rating
 *
 * @param {*} id
 * @param {*} rating
 * @returns
 */
exports.updateuserRating = async (id, rating) => {
  return await User.findByIdAndUpdate(id, { $push: { rating } }, { new: true });
};

/**
 * Delete user rating
 *
 * @param {*} id
 * @param {*} ratingId
 * @returns
 */
exports.deleteUserRating = async (id, ratingId) => {
  return User.findOneAndUpdate(
    {
      _id: id,
      'rating._id': ratingId,
    },
    {
      $set: { 'rating.$.isDeleted': true },
    },
    { new: true }
  );
};
