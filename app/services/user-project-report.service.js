/* models */
const UserProjectReport = require('../models/user-project-report.model');

/**
 * Create User Project Report
 *
 * @param {*} requestData
 * @param {*} session
 * @returns
 */
exports.createUserProjectReport = async (requestData, session = null) => {
  const createUserProjectReport = new UserProjectReport(requestData);

  return await createUserProjectReport.save({ session });
};

exports.updateUserProjectReport = async (filter, requestData) => {
  return await UserProjectReport.findOneAndUpdate(filter, { $set: requestData }, { new: true });
};

exports.getSingleUserProjectReportByFilter = async filter => {
  return UserProjectReport.findOne(filter, {
    updatedAt: 0,
    updatedBy: 0,
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  });
};

/**
 * Check And Create User Project Report
 *
 * @param {*} reqBody
 * @param {*} account
 * @param {*} createdBy
 * @returns
 */
exports.checkAndCreateUserProjectReport = async (reqBody, account, createdBy) => {
  let requestData = reqBody;
  requestData.account = account;

  // Find existing UserProjectReport
  const getUserProjectReport = await UserProjectReport.findOne({
    project: requestData.project,
    report: requestData.report,
    location: requestData.location,
    account: account,
    ...(requestData.asset && requestData.asset.length > 0
      ? {
          asset: {
            $size: requestData.asset.length,
          },
          'asset.asset': {
            $all: requestData.asset.map(asset => asset.asset),
          },
        }
      : { asset: [] }),
  }).lean();

  if (getUserProjectReport) {
    // Update `asset` in `requestData` to store in same asset sequence
    if (getUserProjectReport.asset && getUserProjectReport.asset.length > 0) {
      requestData.asset = getUserProjectReport.asset.map(storedAsset => ({
        asset: storedAsset.asset,
      }));
    }
    return getUserProjectReport._id;
  } else {
    // Create new UserProjectReport
    requestData.createdBy = createdBy;
    const createUserProjectReport = await UserProjectReport.create(requestData);

    return createUserProjectReport._id;
  }
};
