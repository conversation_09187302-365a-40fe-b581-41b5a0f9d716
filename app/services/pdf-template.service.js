// utils
const { generateAndExportPdf } = require('../utils/export-pdf.utils');
const { convertImageUrlToBase64Image } = require('../utils/common-function.utils');
const { orderTemplate } = require('../../pdf_templates/order.template');
const { shiftTemplate } = require('../../pdf_templates/shiftDetails.template');
const { toolboxTemplate } = require('../../pdf_templates/toolboxTalk.template');
const { safetyCardTemplate } = require('../../pdf_templates/safetyCard.template');
const { header, footer } = require('../../pdf_templates/header-footer.template');
const { userReport } = require('../../pdf_templates/userReport.template');
const { locationProgressList } = require('../../pdf_templates/locationProgressList.template');
const { dprPdfTemplate } = require('../../pdf_templates/dpr.template');
const HTTP_STATUS = require('../utils/status-codes');

exports.pdfTemplateCommonComponents = async () => {
  const companyLogo = global.constant.APP_LOGO;
  const reynardLogo = global.constant.FOOTER_PDF_APP_LOGO;

  const base64ReynardLogo = await convertImageUrlToBase64Image(reynardLogo);
  const base64CompanyLogo = await convertImageUrlToBase64Image(companyLogo);

  const headerTemplate = await header({ base64CompanyLogo });
  const footerTemplate = await footer({ base64ReynardLogo });

  let options = {
    format: 'A4',
    orientation: 'portrait',
    printBackground: true,
    margin: {
      top: '30mm',
      right: '10mm',
      bottom: '20mm',
      left: '10mm',
    },
    displayHeaderFooter: true,
    headerTemplate: headerTemplate,
    footerTemplate: footerTemplate,
  };
  const templateData = {
    companyPrimaryColor: global.constant.Reynard_PRIMARY_COLOR,
  };

  return { options, templateData };
};

exports.exportOrderPDF = async (requestData, res) => {
  try {
    const { options, templateData } = await this.pdfTemplateCommonComponents();
    templateData.requestData = requestData;
    let pdfHtmlTemplate = await orderTemplate(templateData);
    // Generate and send PDF using the utility function
    await generateAndExportPdf(pdfHtmlTemplate, res, options);
  } catch (error) {
    res.status(500).send(error.toString());
  }
};

exports.exportShiftPDF = async (requestData, res) => {
  try {
    const { options, templateData } = await this.pdfTemplateCommonComponents();
    templateData.requestData = requestData;
    let pdfHtmlTemplate = await shiftTemplate(templateData);
    // Generate and send PDF using the utility function
    await generateAndExportPdf(pdfHtmlTemplate, res, options);
  } catch (error) {
    res.status(500).send(error.toString());
  }
};

/**
 * Get Safety Card pdf Details
 *
 * @param {*} requestData
 * @param {*} res
 */
exports.exportSafetyCardPDF = async (requestData, res) => {
  try {
    const { options, templateData } = await this.pdfTemplateCommonComponents();
    templateData.requestData = requestData;
    let pdfHtmlTemplate = await safetyCardTemplate(templateData);
    // Generate and send PDF using the utility function
    await generateAndExportPdf(pdfHtmlTemplate, res, options);
  } catch (error) {
    res.status(500).send(error.toString());
  }
};

/**
 * Get Toolbox talk pdf Details
 *
 * @param {*} requestData
 * @param {*} res
 * @returns
 */

exports.exportToolboxTalkPDF = async (requestData, res) => {
  try {
    const { options, templateData } = await this.pdfTemplateCommonComponents();
    templateData.requestData = requestData;
    let pdfHtmlTemplate = await toolboxTemplate(templateData);
    // Generate and send PDF using the utility function
    await generateAndExportPdf(pdfHtmlTemplate, res, options);
  } catch (error) {
    res.status(500).send(error.toString());
  }
};

/**
 * Export Report PDF Service
 *
 * @param {*} requestData
 * @param {*} res
 */
exports.exportReportPDF = async (requestData, res) => {
  try {
    const { options, templateData } = await this.pdfTemplateCommonComponents();
    templateData.requestData = requestData;
    let pdfHtmlTemplate = await userReport(templateData);
    // Generate and send PDF using the utility function
    await generateAndExportPdf(pdfHtmlTemplate, res, options);
  } catch (error) {
    res.status(500).send(error.toString());
  }
};

/**
 * Export Location Progress PDF Service
 *
 * @param {*} requestData
 * @param {*} res
 */
exports.exportLocationProgressPDF = async (requestData, res) => {
  try {
    const { options, templateData } = await this.pdfTemplateCommonComponents();
    templateData.requestData = requestData;
    let pdfHtmlTemplate = await locationProgressList(templateData);
    // Generate and send PDF using the utility function
    await generateAndExportPdf(pdfHtmlTemplate, res, options);
  } catch (error) {
    res.status(500).send(error.toString());
  }
};

/**
 * Export DPR PDF Service
 *
 * @param {*} requestData
 * @param {*} res
 */
exports.exportDprPdf = async (requestData, res) => {
  try {
    const { options, templateData } = await this.pdfTemplateCommonComponents();
    templateData.requestData = requestData;
    let pdfHtmlTemplate = await dprPdfTemplate(templateData);
    await generateAndExportPdf(pdfHtmlTemplate, res, options);
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).send(error.toString());
  }
};
