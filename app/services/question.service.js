// model
const Question = require('../models/question.model');

/**
 * Create Question
 * @param {*} requestData
 * @returns
 */
exports.createQuestion = async requestData => {
  return await Question.create(requestData);
};

/**
 * Get data by question titile
 * @param {*} titile
 * @returns
 */
exports.getQuestionByTitle = async title => {
  return await Question.findOne({ title });
};

/**
 * Get Question By Id
 *
 * @param {*} id
 * @returns
 */
exports.getQuestionById = async id => {
  return await Question.findById(id, {
    updatedAt: 0,
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Get all questions
 * @param {*} filter
 * @returns
 */
exports.getAllQuestions = async (filter, sort) => {
  return await Question.find(filter, {
    updatedAt: 0,
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort({ createdAt: sort ?? -1 })
    .populate([
      {
        path: 'account',
        select: { _id: 1, name: 1 },
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]);
};

/**
 * Update question
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateQuestion = async (id, requestData) => {
  return Question.findByIdAndUpdate(id, { $set: requestData }, { new: true }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Soft delete question
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.softDeleteQuestion = async (id, deletedAt) => {
  return Question.findByIdAndUpdate(id, { $set: deletedAt }, { new: true }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Hard delete question
 *
 * @param {*} id
 * @returns
 */
exports.hardDeleteQuestion = async id => {
  return Question.findByIdAndDelete(id);
};

/**
 * Get the question data count as per the filter
 *
 * @param {*} filter
 * @returns
 */
exports.questionsCount = async filter => {
  return Question.find(filter).countDocuments();
};

/**
 * Check the user questions difference with question master
 *
 * @param {*} userData
 * @returns
 */
exports.checkUserQuestionDiffrence = async userData => {
  // fetch all the questions
  let questions = await this.getAllQuestions({
    account: userData.account,
    isPublished: true,
    deletedAt: null,
  });

  // check the medical and gdpr questions not in user data then add default questions
  let newQuestions = [];
  let newGDPR = [];
  if (!('medical' in userData) && !('gdpr' in userData)) {
    newGDPR = [
      {
        title: global.constant.GDPR_QUESTION_TITLE[0],
      },
      {
        title: global.constant.GDPR_QUESTION_TITLE[1],
      },
    ];
    Object.keys(questions).forEach(key => {
      newQuestions = [
        ...newQuestions,
        {
          questionId: questions[key]._id,
          title: questions[key].title,
          description: '',
        },
      ];
    });
    userData.gdpr = newGDPR;
    userData.medical = newQuestions;
    return userData;
  }

  // check the gdpr empty in user data then add default questions
  if ('gdpr' in userData && userData.gdpr.length === 0) {
    userData.gdpr = [
      {
        title: global.constant.GDPR_QUESTION_TITLE[0],
      },
      {
        title: global.constant.GDPR_QUESTION_TITLE[1],
      },
    ];
  }

  // check the length
  if (userData.medical.length === questions.length) {
    let count = 0;
    Object.keys(userData.medical).forEach(key => {
      if (userData.medical[key].questionId.toString() === questions[key]._id.toString()) {
        count++;
      }
    });

    if (count === questions.length) {
      return userData;
    }
  }

  // prepare array of stored questions ids'
  let medicalIds = [];
  Object.keys(userData.medical).forEach(key => {
    if (userData.medical[key].questionId.toString() !== questions[key]._id.toString()) {
      delete userData.medical[key];
    } else {
      medicalIds = [...medicalIds, userData.medical[key].questionId.toString()];
    }
  });

  // filter out empty item
  userData.medical = userData.medical.filter(item => item !== '');

  // check and add the new question in stored array
  Object.keys(questions).forEach(key => {
    if (!medicalIds.includes(questions[key]._id.toString())) {
      userData.medical = [
        ...userData.medical,
        {
          questionId: questions[key]._id,
          title: questions[key].title,
          answer: null,
          description: '',
        },
      ];
    }
  });

  return userData;
};
