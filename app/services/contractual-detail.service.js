/* models */
const ContractualDetail = require('../models/contractual-detail.model');

/**
 * Create Data
 *
 * @param {*} requestData
 * @returns
 */
exports.createData = async requestData => ContractualDetail.create(requestData);

/**
 * Find single record by filter
 *
 * @param {*} filter
 * @returns
 */
exports.getSingleRecord = async filter => {
  return ContractualDetail.findOne(filter, { createdAt: 0, updatedAt: 0, __v: 0 });
};

/**
 * Update Details
 *
 * @param {*} id
 * @param {*} update
 * @returns
 */
exports.updateDetail = async (id, update) =>
  ContractualDetail.findByIdAndUpdate(id, update, { new: true });

/**
 * Remove document link
 *
 * @param {*} id
 * @param {*} file
 * @returns
 */
exports.removeFile = async (id, file) => {
  return ContractualDetail.updateOne({ _id: id }, { $pull: { identityProof: { _id: file } } });
};

/**
 * Update File Data
 *
 * @param {*} fieldSearch
 * @param {*} setData
 * @returns
 */
exports.updateFileData = (fieldSearch, setData) => {
  return ContractualDetail.updateOne(fieldSearch, { $set: setData });
};

/**
 * Get File Data
 *
 * @param {*} reportId
 * @param {*} filerFiled
 * @returns
 */
exports.getFileData = async (reportId, filerFiled) => {
  return ContractualDetail.findById(reportId, filerFiled);
};
