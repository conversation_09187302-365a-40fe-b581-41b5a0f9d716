// models
const Equipment = require('../models/equipment.model');
const EquipmentOrderHistory = require('../models/equipment-order-history.model');
const InventoryHistory = require('../models/inventory-history.model');

// services
const equipmentTypeService = require('../services/equipment-type.service');
const inventoryHistoryService = require('../services/inventory-history.service');

/**
 * Create Equipment
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipment = async requestData => {
  return await Equipment.create(requestData);
};

/**
 * Filter Equipments
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getEquipments = async (filter, page, perPage, sort) => {
  return await Equipment.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .collation({ locale: 'en' })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate(this.populateEquipmentFields());
};

/**
 * Get Equipment by Filter
 *
 * @param {*} id
 * @returns
 */
exports.getSingleEquipmentByFilter = async filter => {
  filter.deletedAt = null;
  filter.isActive = true;
  return await Equipment.findOne(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  }).populate(this.populateEquipmentFields());
};

/**
 * Update Equipment
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateEquipment = async (id, requestData, session) => {
  return await Equipment.findByIdAndUpdate(id, { $set: requestData }, { new: true, session });
};

/**
 * Delete Equipment
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteEquipment = async (id, deletedAt) => {
  return await Equipment.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

/**
 * Populate Equipment Fields
 *
 * @returns
 */
exports.populateEquipmentFields = () => {
  return [
    {
      path: 'equipmentType',
      model: 'equipment-type',
      select: {
        _id: 1,
        type: 1,
        equipmentCategory: 1,
        equipmentUnit: 1,
        currencyUnit: 1,
        price: 1,
        quantityType: 1,
        hsCode: 1,
      },
      populate: [
        {
          path: 'equipmentCategory',
          model: 'equipment-category',
          select: { _id: 1, name: 1, abbreviation: 1 },
        },
        {
          path: 'equipmentUnit',
          model: 'equipment-unit',
          select: { _id: 1, title: 1, abbreviation: 1 },
        },
        {
          path: 'currencyUnit',
          model: 'currency-unit',
          select: { _id: 1, name: 1, symbol: 1 },
        },
        {
          path: 'quantityType',
          model: 'equipment-quantity-type',
          select: { _id: 1, name: 1, priceType: 1, quantityType: 1 },
        },
        {
          path: 'hsCode',
          model: 'hs-code',
          select: { _id: 1, name: 1, code: 1 },
        },
      ],
    },
    {
      path: 'certificateType',
      model: 'equipment-certificate-type',
      select: { _id: 1, title: 1 },
    },
    {
      path: 'warehouse',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ];
};

/**
 * Auto generate equipment number
 *
 * @param {*} equipmentType
 * @param {*} account
 * @returns
 */
exports.equipmentNumberGenerator = async (equipmentType, account) => {
  try {
    const equipType = await equipmentTypeService.getEquipmentTypeById(equipmentType);
    const [equipCategory] = await equipmentTypeService.getEquipmentType(
      {
        _id: equipmentType,
        account: account,
        deletedAt: null,
      },
      0,
      1,
      -1
    );

    let equipTypeData = await equipmentTypeService.getEquipmentType(
      {
        equipmentCategory: equipCategory.equipmentCategory._id,
        account: account,
        deletedAt: null,
      },
      0,
      100,
      -1
    );
    let Ids = equipTypeData.map(equipmentType => equipmentType._id);

    let filterData = {
      equipmentType: { $in: Ids },
      account: account,
    };
    const equipment = await this.getEquipments(filterData, 0, 1, { updatedAt: -1 });

    let newEquipmentNumber = `${equipType.equipmentCategory.abbreviation}${global.constant.DEFAULT_POST_NUMBER_FORMAT}`;
    if (equipment[0]?.equipmentNumber) {
      let existingNumber = equipment[0].equipmentNumber;
      existingNumber = existingNumber.slice(3, 8);
      existingNumber = parseInt(existingNumber) + 1;
      existingNumber = existingNumber.toString().padStart(5, '0');
      newEquipmentNumber = `${equipType.equipmentCategory.abbreviation}${existingNumber}`;
    }
    return newEquipmentNumber;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * Check the equipment value
 *
 * @param {*} equipmentTypeId
 * @returns
 */
exports.checkEquipmentValue = async equipmentTypeId => {
  try {
    const equipmentType = await equipmentTypeService.getEquipmentTypeById(equipmentTypeId);
    return equipmentType.price;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * Equipment Search By name, type or category - Optimized Version
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.equipmentSearchByKeyword = async (defaultFilter, page, perPage, sort, filter = {}) => {
  let aggregateFilter = [];
  if (Object.keys(filter).length === 0 && filter.constructor === Object) {
    // Optimized pipeline for simple queries
    aggregateFilter = [
      {
        $match: defaultFilter,
      },
      // Single optimized lookup with all necessary joins
      {
        $lookup: {
          from: 'equipment-types',
          let: { equipmentTypeId: '$equipmentType' },
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$equipmentTypeId'] } } },
            {
              $lookup: {
                from: 'equipment-categories',
                localField: 'equipmentCategory',
                foreignField: '_id',
                as: 'equipmentCategory',
                pipeline: [{ $project: { _id: 1, name: 1, abbreviation: 1 } }]
              },
            },
            {
              $lookup: {
                from: 'equipment-units',
                localField: 'equipmentUnit',
                foreignField: '_id',
                as: 'equipmentUnit',
                pipeline: [{ $project: { _id: 1, title: 1, abbreviation: 1 } }]
              },
            },
            {
              $lookup: {
                from: 'currency-units',
                localField: 'currencyUnit',
                foreignField: '_id',
                as: 'currencyUnit',
                pipeline: [{ $project: { _id: 1, name: 1, symbol: 1 } }]
              },
            },
            {
              $lookup: {
                from: 'equipment-quantity-types',
                localField: 'quantityType',
                foreignField: '_id',
                as: 'quantityType',
                pipeline: [{ $project: { _id: 1, name: 1, priceType: 1, quantityType: 1 } }]
              },
            },
            {
              $lookup: {
                from: 'hs-codes',
                localField: 'hsCode',
                foreignField: '_id',
                as: 'hsCode',
                pipeline: [{ $project: { _id: 1, name: 1, code: 1 } }]
              },
            },
            {
              $project: {
                _id: 1,
                type: 1,
                equipmentCategory: { $arrayElemAt: ['$equipmentCategory', 0] },
                equipmentUnit: { $arrayElemAt: ['$equipmentUnit', 0] },
                currencyUnit: { $arrayElemAt: ['$currencyUnit', 0] },
                price: 1,
                quantityType: '$quantityType',
                hsCode: { $arrayElemAt: ['$hsCode', 0] },
                certificateTypes: 1,
                isTemporary: { $ifNull: ['$isTemporary', false] },
              },
            }
          ],
          as: 'equipmentType',
        },
      },
      {
        $lookup: {
          from: 'warehouses',
          localField: 'warehouse',
          foreignField: '_id',
          as: 'warehouse',
          pipeline: [{ $project: { _id: 1, name: 1 } }]
        },
      },
      // Optimize field processing
      {
        $addFields: {
          equipmentType: { $arrayElemAt: ['$equipmentType', 0] },
          warehouse: { $arrayElemAt: ['$warehouse', 0] },
        },
      },
      {
        $project: {
          deletedBy: 0,
          deletedAt: 0,
          isDeleted: 0,
          __v: 0,
        },
      },
      {
        $sort: sort,
      },
    ];

    // Add pagination if specified
    if (page !== '' && perPage !== '') {
      aggregateFilter.push(
        { $skip: parseInt(page) * parseInt(perPage) },
        { $limit: parseInt(perPage) }
      );
    }
  } else {
    // Optimized pipeline for filtered queries
    aggregateFilter = [
      {
        $match: defaultFilter,
      },
      // Single optimized lookup with all necessary joins
      {
        $lookup: {
          from: 'equipment-types',
          let: { equipmentTypeId: '$equipmentType' },
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$equipmentTypeId'] } } },
            {
              $lookup: {
                from: 'equipment-categories',
                localField: 'equipmentCategory',
                foreignField: '_id',
                as: 'equipmentCategory',
                pipeline: [{ $project: { _id: 1, name: 1, abbreviation: 1 } }]
              },
            },
            {
              $lookup: {
                from: 'equipment-units',
                localField: 'equipmentUnit',
                foreignField: '_id',
                as: 'equipmentUnit',
                pipeline: [{ $project: { _id: 1, title: 1, abbreviation: 1 } }]
              },
            },
            {
              $lookup: {
                from: 'currency-units',
                localField: 'currencyUnit',
                foreignField: '_id',
                as: 'currencyUnit',
                pipeline: [{ $project: { _id: 1, name: 1, symbol: 1 } }]
              },
            },
            {
              $lookup: {
                from: 'equipment-quantity-types',
                localField: 'quantityType',
                foreignField: '_id',
                as: 'quantityType',
                pipeline: [{ $project: { _id: 1, name: 1, priceType: 1, quantityType: 1 } }]
              },
            },
            {
              $lookup: {
                from: 'hs-codes',
                localField: 'hsCode',
                foreignField: '_id',
                as: 'hsCode',
                pipeline: [{ $project: { _id: 1, name: 1, code: 1 } }]
              },
            },
            {
              $project: {
                _id: 1,
                type: 1,
                equipmentCategory: { $arrayElemAt: ['$equipmentCategory', 0] },
                equipmentUnit: { $arrayElemAt: ['$equipmentUnit', 0] },
                currencyUnit: { $arrayElemAt: ['$currencyUnit', 0] },
                price: 1,
                quantityType: '$quantityType',
                hsCode: { $arrayElemAt: ['$hsCode', 0] },
                certificateTypes: 1,
              },
            }
          ],
          as: 'equipmentType',
        },
      },
      {
        $lookup: {
          from: 'warehouses',
          localField: 'warehouse',
          foreignField: '_id',
          as: 'warehouse',
          pipeline: [{ $project: { _id: 1, name: 1 } }]
        },
      },
      // Optimize field processing
      {
        $addFields: {
          equipmentType: { $arrayElemAt: ['$equipmentType', 0] },
          warehouse: { $arrayElemAt: ['$warehouse', 0] },
        },
      },
      {
        $project: {
          deletedBy: 0,
          deletedAt: 0,
          isDeleted: 0,
          __v: 0,
        },
      },
      {
        $match: filter,
      },
      {
        $sort: sort,
      },
      {
        $facet: {
          metadata: [{ $count: 'totalDocuments' }],
          data: [
            ...(page !== '' && perPage !== ''
              ? [{ $skip: parseInt(page) * parseInt(perPage) }, { $limit: parseInt(perPage) }]
              : []),
          ],
        },
      },
    ];
  }
  let coll = { collation: { locale: 'en' } };
  return await Equipment.aggregate(aggregateFilter, coll);
};

/**
 * Bind QR Code
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.bindQRCode = async (id, requestData) => {
  let getQR = await Equipment.findById(id, { qrCode: 1 });
  if (getQR?.qrCode.length > 0 && getQR.qrCode[0].code === undefined) {
    return this.updateEquipment(id, { qrCode: requestData });
  }
  return Equipment.updateOne({ _id: id }, { $push: { qrCode: requestData } });
};

/**
 * Find QR Code
 *
 * @param {*} qrCodeNumber
 * @returns
 */
exports.findQRCode = async (qrCodeNumber, equipmentType = null) => {
  let filter = {
    ...(equipmentType ? { equipmentType } : {}),
    qrCode: { $elemMatch: { code: qrCodeNumber, isActive: true } },
    deletedAt: null,
  };
  return Equipment.find(filter, { deletedAt: 0, deletedBy: 0, __v: 0 }).populate(
    this.populateEquipmentFields()
  );
};

/**
 * Check QR Code Out Dated
 *
 * @param {*} equipmentData
 * @param {*} qrCode
 * @returns
 */
exports.checkQRCodeOutDated = async (equipmentData, qrCode) => {
  const index = equipmentData[0].qrCode.findIndex(item => item.code === qrCode);
  return index === equipmentData[0].qrCode.length - 1;
};

/**
 * Check Unique QR code
 *
 * @param {*} qrCode
 */
exports.checkUniqueQrCode = async filter => {
  return await Equipment.findOne(filter);
};

/**
 * Generate Product Number (New Logic)
 *
 * @param {*} equipmentType
 * @returns
 */
exports.productNumberGenerator = async equipmentType => {
  const equipType = await equipmentTypeService.getEquipmentTypeById(equipmentType);
  const equipment = await this.getEquipments({}, 0, 1, { createdAt: -1 });

  let newEquipmentNumber = `${equipType.equipmentCategory.abbreviation}${global.constant.DEFAULT_POST_NUMBER_FORMAT}`;
  if (equipment[0]?.equipmentNumber) {
    let existingNumber = await this.makeEquipmentNumber(equipment[0].equipmentNumber);
    newEquipmentNumber = `${equipType.equipmentCategory.abbreviation}${existingNumber}`;
    newEquipmentNumber = await this.recursiveEquipmentNumber(newEquipmentNumber, equipType);
  }
  return newEquipmentNumber;
};

/**
 * Equipment Number Process
 *
 * @param {*} equipmentNumber
 * @returns
 */
exports.makeEquipmentNumber = async equipmentNumber => {
  let equipNumber = equipmentNumber;
  equipNumber = equipNumber.slice(3, 9);
  equipNumber = parseInt(equipNumber) + 1;
  equipNumber = equipNumber.toString().padStart(6, '0');
  return equipNumber;
};

/**
 * Recursive Function For Duplicate Equipment Number
 *
 * @param {*} equipmentNumber
 * @param {*} equipType
 * @returns
 */
exports.recursiveEquipmentNumber = async (equipmentNumber, equipType) => {
  let newEquipmentNumber = equipmentNumber;
  let checkEquipment = await Equipment.find({ equipmentNumber: newEquipmentNumber });
  if (checkEquipment.length > 0) {
    let changeNewEquipmentNumber = await this.makeEquipmentNumber(newEquipmentNumber);
    newEquipmentNumber = `${equipType.equipmentCategory.abbreviation}${changeNewEquipmentNumber}`;
    newEquipmentNumber = await this.recursiveEquipmentNumber(newEquipmentNumber);
  }
  return newEquipmentNumber;
};

/**
 * Check Equipment Location - Optimized Version
 * Replaces N+1 query pattern with bulk aggregation
 *
 * @param {*} requestData
 * @returns
 */
exports.checkEquipmentLocation = async requestData => {
  if (!requestData || requestData.length === 0) {
    return requestData;
  }

  // Get unique equipment IDs that need location tracking
  const uniqueEquipmentIds = requestData
    .filter(data =>
      data.equipmentType &&
      data.equipmentType.quantityType &&
      data.equipmentType.quantityType.length > 0 &&
      data.equipmentType.quantityType[0].quantityType === 'unique'
    )
    .map(data => data._id);

  if (uniqueEquipmentIds.length === 0) {
    // No unique equipment, just set empty locations
    requestData.forEach(data => {
      data.inventoryLocation = '';
    });
    return requestData;
  }

  // Bulk query to get latest inventory history for all unique equipment
  const inventoryLocations = await InventoryHistory.aggregate([
    {
      $match: {
        equipment: { $in: uniqueEquipmentIds },
        deletedAt: null
      }
    },
    {
      $sort: { equipment: 1, createdAt: -1 }
    },
    {
      $group: {
        _id: '$equipment',
        latestTracker: { $first: '$tracker' }
      }
    }
  ]);

  // Create a map for quick lookup
  const locationMap = new Map();
  inventoryLocations.forEach(item => {
    locationMap.set(item._id.toString(), item.latestTracker || '');
  });

  // Apply locations to equipment data
  requestData.forEach(data => {
    if (data.equipmentType &&
        data.equipmentType.quantityType &&
        data.equipmentType.quantityType.length > 0 &&
        data.equipmentType.quantityType[0].quantityType === 'unique') {
      data.inventoryLocation = locationMap.get(data._id.toString()) || '';
    } else {
      data.inventoryLocation = '';
    }
  });

  return requestData;
};

/**
 * Check Multiple Type Equipment Detail
 *
 * @param {*} equipmentId
 * @returns
 */
exports.checkMultipleTypeEquipmentDetail = async equipmentId => {
  // Get Equipment Order History Data
  let historyData = await EquipmentOrderHistory.aggregate([
    {
      $match: {
        equipment: equipmentId,
        returnOrder: { $eq: [] },
        deletedAt: null,
      },
    },
    {
      $group: {
        _id: '$pmOrder',
        quantity: { $sum: '$wmDispatchQuantity' },
      },
    },
    {
      $lookup: {
        from: 'pm-orders',
        localField: '_id',
        foreignField: '_id',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          {
            $project: {
              _id: 1,
              orderNumber: 1,
              project: 1,
            },
          },
        ],
        as: 'pmOrder',
      },
    },
    {
      $unwind: '$pmOrder',
    },
    {
      $group: {
        _id: '$pmOrder.project',
        pmOrderData: {
          $push: {
            orderId: '$pmOrder._id',
            orderNumber: '$pmOrder.orderNumber',
          },
        },
      },
    },
  ]);

  // Prepare Inventory Tracker Data
  let prepareInventoryData = {
    totalQuantity: 0,
    data: [],
  };
  prepareInventoryData = await this.getTotalQuanityFromInventoryHistory(
    equipmentId,
    prepareInventoryData
  );

  if (historyData.length > 0) {
    for (let data of historyData) {
      let inventoryInnerData = {
        project: data._id,
      };
      let calculateQunatity = 0;
      let inventoryTracker = '';
      for (let pmOrder of data.pmOrderData) {
        let inventoryData = await inventoryHistoryService.getInventoryHistoryOneByFilter(
          {
            equipment: equipmentId,
            pmOrder: pmOrder.orderId,
          },
          -1
        );
        calculateQunatity += inventoryData.quantity;
        inventoryTracker = inventoryData.tracker;
      }
      inventoryInnerData.quantity = calculateQunatity;
      inventoryInnerData.tracker = inventoryTracker;
      prepareInventoryData.data.push(inventoryInnerData);
    }
  } else {
    await this.getTotalQuanityFromInventoryHistory(equipmentId, prepareInventoryData, 'tracker');
  }
  return prepareInventoryData;
};

/**
 * Get Total Quantity From Inventory History
 *
 * @param {*} equipmentId
 * @param {*} inventoryData
 * @param {*} type
 * @returns
 */
exports.getTotalQuanityFromInventoryHistory = async (equipmentId, inventoryData, type = null) => {
  let inventoryHistoryLastPurchase = await InventoryHistory.findOne({
    equipment: equipmentId,
    type: 'purchase',
    deletedAt: null,
  }).sort({ createdAt: -1 });

  if (type === 'tracker') {
    return (inventoryData.data = [
      {
        project: '',
        quantity: inventoryHistoryLastPurchase?.quantity,
        tracker: inventoryHistoryLastPurchase?.tracker,
      },
    ]);
  }

  inventoryData.totalQuantity = inventoryHistoryLastPurchase?.quantity || 0;

  let removeQunaity = await InventoryHistory.find({
    equipment: equipmentId,
    type: 'cancel',
    createdAt: {
      $gt: inventoryHistoryLastPurchase?.createdAt,
    },
    deletedAt: null,
  });

  if (removeQunaity.length > 0) {
    for (let data of removeQunaity) {
      inventoryData.totalQuantity = inventoryData.totalQuantity - data.quantity;
    }
  }
  return inventoryData;
};

/**
 * Get equipment summary
 *
 * @param {String} account
 * @param {String} projectId
 * @param {String} dprDate
 * @returns
 */
exports.getEquipmentSummary = async (account, projectId, dprDate, equipmentTypeIds) => {
  const dateOnly = new Date(dprDate).toISOString().split('T')[0];

  const pipeline = [
    {
      $match: {
        account: account,
        deletedAt: null,
        createdAt: {
          $gte: new Date(`${dateOnly}T00:00:00.000Z`),
          $lt: new Date(`${dateOnly}T23:59:59.999Z`),
        },
      },
    },
    {
      $project: {
        _id: 1,
        account: 1,
        equipment: 1,
        equipmentType: 1,
        pmOrder: 1,
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipment',
        foreignField: '_id',
        as: 'equipmentDetails',
        pipeline: [
          { $match: { deletedAt: null } },
          {
            $project: {
              _id: 1,
              name: 1,
              equipmentNumber: 1,
              serialNumber: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentDetails',
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              type: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $match: {
        'equipmentTypeDetails._id': { $in: equipmentTypeIds },
      },
    },
    {
      $lookup: {
        from: 'pm-orders',
        localField: 'pmOrder',
        foreignField: '_id',
        as: 'pmOrderDetails',
        pipeline: [
          {
            $match: {
              project: projectId,
              status: { $in: ['partially-check-in', 'check-in'] },
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              orderNumber: 1,
              status: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$pmOrderDetails',
    },
    {
      $group: {
        _id: '$_id',
        equipmentType: {
          $first: '$equipmentTypeDetails.type',
        },
        equipmentName: {
          $first: '$equipmentDetails.name',
        },
        equipmentNumber: {
          $first: '$equipmentDetails.equipmentNumber',
        },
        serialNumber: {
          $first: '$equipmentDetails.serialNumber',
        },
        orderNo: {
          $first: '$pmOrderDetails.orderNumber',
        },
      },
    },
  ];
  return await EquipmentOrderHistory.aggregate(pipeline);
};
