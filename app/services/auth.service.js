const User = require('../models/user.model');
const { toObjectId } = require('../utils/common.utils');
const jwt = require('jsonwebtoken');

// Utils
const constantUtils = require('../utils/constants.utils');

/**
 * Get user by email
 *
 * @param {*} mail
 * @returns
 */
exports.getUserByEmail = async mail => {
  return await User.findOne({
    email: { $eq: mail },
    isDeleted: false,
  }).populate([
    {
      path: 'roles role',
      select: {
        title: 1,
        _id: 1,
        allProject: 1,
        isActive: 1,
        accessType: 1,
        isAssignAllProjects: 1,
      },
      strictPopulate: false,
    },
  ]);
};

/**
 * Get user by id
 *
 * @param {*} userId
 * @returns
 */
exports.getUserById = async userId => {
  return await User.findById(toObjectId(userId))
    .select('-password')
    .populate([
      {
        path: 'roles role',
        select: {
          title: 1,
          _id: 1,
          allProject: 1,
          isActive: 1,
          accessType: 1,
          isAssignAllProjects: 1,
        },
        strictPopulate: false,
      },
    ]);
};

/**
 * Update Password By Id
 *
 * @param {*} id
 * @param {*} hashPassword
 * @returns
 */
exports.updatePasswordById = async (id, hashPassword) => {
  return await User.findByIdAndUpdate(toObjectId(id), {
    $set: { password: hashPassword, resetToken: '' },
  });
};

/**
 * It will insert reset time and token
 *
 * @param {*} email
 * @param {*} data
 * @returns
 */
exports.insertResetTimeAndToken = async (email, data) => {
  await User.findOneAndUpdate(
    { email: email },
    { $set: { resetExpiresIn: data.expiryTime, resetToken: data.token } }
  );
  return await User.findOne({ email: email }).select('-password');
};

/**
 * Decode JWT Token
 *
 * @param {*} token
 * @returns
 */
exports.decodeJwtToken = async token => {
  return await jwt.verify(token.split(' ')[1], process.env.JWT_SECRET);
};

/**
 * Get auth account id
 *
 * @param {*} requestHeader
 * @returns
 */
exports.getAuthAccountId = async requestHeader => {
  if (!requestHeader) {
    const error = new Error(constantUtils.SEND_AUTHENTICATION_TOKEN);
    error.code = 400;
    throw error;
  }
  const tokenData = await this.decodeJwtToken(requestHeader);
  return tokenData.account ?? tokenData.id;
};

/**
 * get user data by filter
 *
 * @param {*} filter
 * @returns
 */
exports.getUserDataByFilter = async filter => {
  return await User.findOne(filter, {
    _id: 1,
    callingName: 1,
    firstName: 1,
    lastName: 1,
    email: 1,
    role: 1,
  });
};
