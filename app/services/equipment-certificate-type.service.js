const EquipmentCertificateType = require('../models/equipment-certificate-type.model');

/**
 * Create EquipmentCertificateType
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipmentCertificateType = async requestData => {
  return await EquipmentCertificateType.create(requestData);
};

/**
 * Filter EquipmentCertificateType
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getEquipmentCertificateType = async (filter, perPage, page, sort) => {
  return await EquipmentCertificateType.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort({ createdAt: sort ?? -1 })
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'deletedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]);
};

/**
 * Get EquipmentCertificateType by Id
 *
 * @param {*} id
 * @returns
 */
exports.getEquipmentCertificateTypeById = async id => {
  return await EquipmentCertificateType.findOne({
    _id: id,
    deletedAt: null,
  }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Update EquipmentCertificateType
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateEquipmentCertificateType = async (id, requestData) => {
  return await EquipmentCertificateType.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Delete EquipmentCertificateType
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteEquipmentCertificateType = async (id, deletedAt) => {
  return await EquipmentCertificateType.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

/**
 * Count cerificateType By filter
 *
 * @param {*} filter
 * @returns
 */
exports.getCerificateTypeCountByFilter = async filter => {
  return await EquipmentCertificateType.countDocuments(filter);
};

/**
 * Filter Single EquipmentCertificateType
 *
 * @param {*} filter
 * @returns
 */
exports.filterSingleEquipmentCertificateType = async filter => {
  return await EquipmentCertificateType.findOne(filter);
};
