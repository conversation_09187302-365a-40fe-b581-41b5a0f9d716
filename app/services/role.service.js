const Role = require('../models/role.model');
const {
  accountPipelineFields,
  userCreatedByPipelineFields,
  userUpdatedByPipelineFields,
  userDeletedByFieldPipelineFields,
} = require('../utils/common.utils');

/**
 * Create New Role
 *
 * @param {*} role
 * @returns
 */
exports.create = async role => {
  return await Role.create(role);
};

/**
 * Get Role By Name
 *
 * @param {*} title
 * @returns
 */
exports.getRoleByName = async (title, account) => {
  return Role.findOne({
    $and: [{ title: title, account: account, deletedAt: null }],
  });
};

/**
 * Get All Roles
 *
 * @param {*} account
 * @returns
 */
exports.getAllRoles = async (account, page, perPage) => {
  return Role.find({ account: account, deletedAt: null, isActive: true })
    .populate([
      ...accountPipelineFields,
      ...userCreatedByPipelineFields,
      ...userUpdatedByPipelineFields,
      ...userDeletedByFieldPipelineFields,
    ])
    .sort({ createdAt: -1 })
    .limit(perPage)
    .skip(page * perPage);
};

/**
 * Filter Roles
 *
 * @param {*} req
 * @returns
 */
exports.searchRole = async (req, page, perPage) => {
  const { active, accessType } = req.query;
  const account = req.userData.account;

  let filter = {
    account: account,
    deletedAt: null,
  };

  if (active && active.toLowerCase() !== 'all') {
    filter.isActive = active;
  }

  if (accessType && accessType.toLowerCase() !== 'all') {
    filter.accessType = accessType;
  }

  return Role.find(filter)
    .populate([
      ...accountPipelineFields,
      ...userCreatedByPipelineFields,
      ...userUpdatedByPipelineFields,
      ...userDeletedByFieldPipelineFields,
    ])
    .sort({ createdAt: -1 })
    .limit(perPage)
    .skip(page * perPage);
};

/**
 * Update Role
 *
 * @param {*} id
 * @param {*} data
 * @returns
 */
exports.update = async (id, data) => {
  return Role.findByIdAndUpdate(id, { $set: data }, { new: true }).populate([
    ...accountPipelineFields,
    ...userCreatedByPipelineFields,
    ...userUpdatedByPipelineFields,
    ...userDeletedByFieldPipelineFields,
  ]);
};

/**
 * Get Role By Id
 *
 * @param {*} id
 * @returns
 */
exports.getRoleById = async (id, account) => {
  return Role.findOne({
    $and: [{ _id: id }, { account: account }, { deletedAt: null }],
  }).populate([
    ...accountPipelineFields,
    ...userCreatedByPipelineFields,
    ...userUpdatedByPipelineFields,
    ...userDeletedByFieldPipelineFields,
  ]);
};

/**
 * Delete Role By Id
 *
 * @param {*} id
 * @param {*} data
 * @returns
 */
exports.delete = async (id, data) => {
  return Role.findByIdAndUpdate(id, { $set: data });
};

/**
 * Get Role By Account
 *
 * @param {*} account
 * @returns
 */
exports.getRoleIdOfAccount = async account => {
  return Role.find({ account });
};

/**
 * Create New Default Role
 *
 * @param {*} role
 * @returns
 */
exports.createRole = async role => {
  return await Role.insertMany(role);
};

/**
 * Get All Roles
 *
 * @param {*} filter
 * @returns
 */
exports.getRoles = async filter => {
  return await Role.find(filter);
};
