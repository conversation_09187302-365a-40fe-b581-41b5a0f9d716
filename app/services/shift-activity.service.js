/* models */
const ShiftActivity = require('../models/shift-activity.model');
const { toObjectId } = require('../utils/common.utils');

/**
 * Create User
 *
 * @param {*} shift
 * @returns
 */
exports.createShiftActivity = async requestData => ShiftActivity.create(requestData);

/**
 * Get Single Record By Filter
 *
 * @param {*} filter
 * @param {*} deleted
 * @returns
 */
exports.getSingleRecord = async (filter, deleted = false) => {
  filter.isDeleted = deleted;
  return ShiftActivity.findOne(filter, {
    createdBy: 0,
    updatedBy: 0,
    deletedBy: 0,
    createdAt: 0,
    updatedAt: 0,
    deletedAt: 0,
    isDeleted: 0,
    __v: 0,
  }).populate([
    {
      path: 'activity',
      select: { name: 1, _id: 1 },
    },
    {
      path: 'location',
      select: { title: 1, _id: 1 },
    },
  ]);
};

/**
 * Update Data
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateShiftActivity = async (id, requestData) => {
  return ShiftActivity.findByIdAndUpdate(id, { $set: requestData }, { new: true }).populate([
    {
      path: 'activity',
      select: { name: 1, _id: 1 },
    },
    {
      path: 'location',
      select: { title: 1, _id: 1 },
    },
  ]);
};

/**
 * Get All Data By Filter
 *
 * @param {*} filter
 * @param {*} deleted
 * @returns
 */
exports.getShiftActivityDataByFilter = async (filter, deleted = false) => {
  filter.isDeleted = deleted;
  return ShiftActivity.find(filter, {
    createdBy: 0,
    updatedBy: 0,
    deletedBy: 0,
    createdAt: 0,
    updatedAt: 0,
    deletedAt: 0,
    isDeleted: 0,
    __v: 0,
  }).populate([
    {
      path: 'activity',
      select: { name: 1, _id: 1 },
    },
    {
      path: 'location',
      select: { title: 1, _id: 1, deletedAt: 1 },
    },
  ]);
};

/**
 * Get All Shift Activites
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @param {*} deleted
 * @returns
 */
exports.getAllRecord = async (filter, sort, deleted = false) => {
  filter.isDeleted = deleted;
  return ShiftActivity.aggregate([
    {
      $match: filter,
    },
    {
      $sort: { _id: sort },
    },
    {
      $lookup: {
        from: 'activities',
        localField: 'activity',
        foreignField: '_id',
        pipeline: [{ $project: { name: 1, _id: 1 } }],
        as: 'activity',
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, _id: 1 } }],
        as: 'location',
      },
    },
    {
      $unwind: { path: '$activity', preserveNullAndEmptyArrays: true },
    },
    {
      $unwind: { path: '$location', preserveNullAndEmptyArrays: true },
    },
    {
      $addFields: {
        activity: { $ifNull: ['$activity', {}] },
        location: { $ifNull: ['$location', {}] },
      },
    },
    {
      $unset: [
        'createdBy',
        'updatedBy',
        'deletedBy',
        'createdAt',
        'updatedAt',
        'deletedAt',
        'isDeleted',
        '__v',
      ],
    },
  ]);
};

exports.isEndTimeValid = async (start, end) => {
  const startDate = new Date(start);
  const endDate = new Date(end);
  return endDate.getTime() > startDate.getTime();
};

/**
 * Delete All Project Shift Activity
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectShiftActivity = async (shiftId, deletedAt) => {
  return await ShiftActivity.updateMany(
    { shift: shiftId },
    {
      $set: deletedAt,
    }
  );
};

/**
 * get shift activity details
 *
 * @param {*} id
 * @returns
 */
exports.getShiftActivityDetails = async id => {
  let aggregationFilter = [
    {
      $match: { shift: id, deletedAt: null },
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        pipeline: [{ $project: { title: 1, _id: 1 } }],
        as: 'location',
      },
    },
    {
      $unwind: { path: '$location', preserveNullAndEmptyArrays: true },
    },
    {
      $lookup: {
        from: 'activities',
        localField: 'activity',
        foreignField: '_id',
        pipeline: [
          {
            $lookup: {
              from: 'scopes',
              localField: 'scopeId',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    name: 1, // Project only the "name" field from scopes
                  },
                },
              ],
              as: 'scopes',
            },
          },
          {
            $project: {
              name: 1,
              scopes: 1,
            },
          },
        ],
        as: 'activities',
      },
    },
    {
      $sort: { endTime: 1 },
    },
  ];

  return await ShiftActivity.aggregate(aggregationFilter);
};

/**
 * Get Activity Summary
 *
 * @param {*} project
 * @returns
 */
exports.getActivitySummary = async project => {
  return await ShiftActivity.aggregate([
    { $match: { deletedAt: null } },
    {
      $lookup: {
        from: 'activities',
        localField: 'activity',
        foreignField: '_id',
        as: 'activity',
      },
    },
    { $unwind: '$activity' },
    {
      $lookup: {
        from: 'scopes',
        localField: 'activity.scopeId',
        foreignField: '_id',
        as: 'activity.scopeId',
      },
    },
    { $unwind: { path: '$activity.scopeId', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'shifts',
        localField: 'shift',
        foreignField: '_id',
        as: 'shift',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
        ],
      },
    },
    {
      $match: {
        'shift.project': toObjectId(project),
      },
    },
    { $unwind: { path: '$shift', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'teammembers',
        localField: 'shift._id',
        foreignField: 'shift',
        as: 'teammembers',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        teamMembers: { $size: '$teammembers' },
      },
    },
    {
      $project: {
        teammembers: 0, // Remove the detailed teammembers array
      },
    },
  ]);
};

/**
 * Activity Calculation
 *
 * @param {*} response
 */
exports.activityCalculation = async (response, dprDate) => {
  const result = {};
  let totalPrevious = 0,
    totalToday = 0;

  // Track previous end time by shift ID to handle multiple shifts
  const previousEndTimeByShiftId = {};

  const dprDateStr = new Date(dprDate).toISOString().split('T')[0];

  // Sort response array
  const sortedResponse = [...response].sort((itemA, itemB) => {
    return new Date(itemA.endTime) - new Date(itemB.endTime);
  });

  sortedResponse.forEach(item => {
    const scopeName =
      item.activity?.scopeId?.deletedAt !== null
        ? `${item.activity?.scopeId?.name ?? 'N/A'} (Deleted)`
        : item.activity?.scopeId?.name ?? 'N/A';
    const activityName = item.activity.name;
    const endTime = new Date(item.endTime);
    const startTime = new Date(item.shift.startDate);
    const shiftId = item.shift._id.toString(); // Use shift ID as unique identifier

    let durationMinutes;

    // Check if this is the first activity in this shift
    if (!previousEndTimeByShiftId[shiftId]) {
      // First activity of this shift - calculate from shift start
      durationMinutes = (endTime - startTime) / global.constant.MILLISECOND_TO_MINUTES;
    } else {
      // Subsequent activities in this shift - calculate from previous activity end time
      durationMinutes =
        (endTime - previousEndTimeByShiftId[shiftId]) / global.constant.MILLISECOND_TO_MINUTES;
    }

    // Ensure duration is not negative
    durationMinutes = Math.max(0, durationMinutes);

    // Store this end time for this shift
    previousEndTimeByShiftId[shiftId] = endTime;

    const shiftStartDateStr = item.shift.startDate.includes('T')
      ? item.shift.startDate.split('T')[0]
      : item.shift.startDate.split(' ')[0];
    let previous = 0,
      today = 0;

    if (shiftStartDateStr < dprDateStr) {
      previous = durationMinutes;
    } else if (shiftStartDateStr === dprDateStr) {
      today = durationMinutes;
    }

    const cumulative = previous + today;
    totalPrevious += previous * item.teamMembers;
    totalToday += today * item.teamMembers;

    if (!result[scopeName]) {
      result[scopeName] = {};
    }

    if (!result[scopeName][activityName]) {
      result[scopeName][activityName] = { previous: 0, today: 0, cumulative: 0 };
    }

    result[scopeName][activityName].previous += previous * item.teamMembers;
    result[scopeName][activityName].today += today * item.teamMembers;
    result[scopeName][activityName].cumulative += cumulative * item.teamMembers;
  });

  const formatDuration = minutes => {
    const hours = Math.floor(minutes / global.constant.CONVERT_TO_HOURS);
    const mins = minutes % global.constant.CONVERT_TO_HOURS;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  const formattedResult = Object.keys(result).map(scope => ({
    [scope]: Object.keys(result[scope]).map(activity => ({
      name: activity,
      previous: formatDuration(result[scope][activity].previous),
      today: formatDuration(result[scope][activity].today),
      cumulative: formatDuration(result[scope][activity].cumulative),
    })),
  }));

  formattedResult.push({
    totalReportedTime: {
      totalPrevious: formatDuration(totalPrevious),
      totalToday: formatDuration(totalToday),
      totalCumulative: formatDuration(totalPrevious + totalToday),
    },
  });

  return formattedResult;
};

/**
 * Get Daily Activity Logs
 *
 * @param {*} filter
 * @returns
 */
exports.dailyActivityLogs = async filter => {
  return ShiftActivity.find(filter);
};

/**
 * Get Daily Activity Caculation
 *
 * @param {*} filter
 * @returns
 */
exports.dailyActivityCaculation = async filter => {
  return ShiftActivity.aggregate([
    { $match: filter },
    {
      $lookup: {
        from: 'activities',
        localField: 'activity',
        foreignField: '_id',
        as: 'activity',
      },
    },
    { $unwind: '$activity' },
    {
      $lookup: {
        from: 'shifts',
        localField: 'shift',
        foreignField: '_id',
        as: 'shift',
      },
    },
    { $unwind: { path: '$shift', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'teams',
        localField: 'shift.team',
        foreignField: '_id',
        as: 'team',
      },
    },
    { $unwind: { path: '$team', preserveNullAndEmptyArrays: true } },
    { $match: { 'team.sortOrder': { $exists: true } } },
    { $sort: { 'team.sortOrder': 1 } },

    {
      $lookup: {
        from: 'teammembers',
        localField: 'shift._id',
        foreignField: 'shift',
        pipeline: [{ $match: { deletedAt: null } }],
        as: 'teammembers',
      },
    },
    {
      $lookup: {
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        as: 'location',
      },
    },
    { $unwind: { path: '$location', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'assets',
        localField: 'asset',
        foreignField: '_id',
        as: 'asset',
      },
    },
    { $unwind: { path: '$asset', preserveNullAndEmptyArrays: true } },
    { $sort: { 'shift._id': 1, endTime: 1 } },
    {
      $group: {
        _id: '$shift._id',
        team: { $first: '$team.teamsWfmName' },
        teamSize: { $first: { $size: '$teammembers' } },
        shiftStartTime: { $first: '$shift.startDate' },
        teamSortOrder: { $first: '$team.sortOrder' },
        shiftActivities: {
          $push: {
            activityName: '$activity.name',
            endTime: '$endTime',
            location: '$location.title',
            asset: '$asset.name',
            remarks: '$comments',
          },
        },
      },
    },
    { $sort: { teamSortOrder: 1 } },
    {
      $project: {
        team: 1,
        teamSize: 1,
        shiftStartTime: 1,
        shiftActivitiesWithManHours: {
          $map: {
            input: '$shiftActivities',
            as: 'activity',
            in: {
              $mergeObjects: [
                '$$activity',
                {
                  startTime: {
                    $cond: {
                      if: {
                        $eq: [{ $indexOfArray: ['$shiftActivities', '$$activity'] }, 0],
                      },
                      then: '$shiftStartTime',
                      else: {
                        $arrayElemAt: [
                          '$shiftActivities.endTime',
                          {
                            $subtract: [{ $indexOfArray: ['$shiftActivities', '$$activity'] }, 1],
                          },
                        ],
                      },
                    },
                  },
                  teamHours: {
                    $divide: [
                      {
                        $subtract: [
                          { $toDate: '$$activity.endTime' },
                          {
                            $toDate: {
                              $cond: {
                                if: {
                                  $eq: [{ $indexOfArray: ['$shiftActivities', '$$activity'] }, 0],
                                },
                                then: '$shiftStartTime',
                                else: {
                                  $arrayElemAt: [
                                    '$shiftActivities.endTime',
                                    {
                                      $subtract: [
                                        { $indexOfArray: ['$shiftActivities', '$$activity'] },
                                        1,
                                      ],
                                    },
                                  ],
                                },
                              },
                            },
                          },
                        ],
                      },
                      global.constant.MILLISECOND_TO_MINUTES,
                    ],
                  },
                },
              ],
            },
          },
        },
      },
    },
    {
      $project: {
        team: 1,
        teamSize: 1,
        shiftActivities: {
          $map: {
            input: '$shiftActivitiesWithManHours',
            as: 'activity',
            in: {
              $mergeObjects: [
                '$$activity',
                {
                  teamHours: {
                    $concat: [
                      // Hours
                      {
                        $toString: {
                          $floor: {
                            $divide: ['$$activity.teamHours', global.constant.CONVERT_TO_HOURS],
                          },
                        },
                      },
                      ':',
                      // Minutes (padded to 2 digits)
                      {
                        $substrCP: [
                          {
                            $cond: {
                              if: {
                                $lt: [
                                  {
                                    $round: [
                                      {
                                        $mod: [
                                          '$$activity.teamHours',
                                          global.constant.CONVERT_TO_HOURS,
                                        ],
                                      },
                                      0,
                                    ],
                                  },
                                  10,
                                ],
                              },
                              then: {
                                $concat: [
                                  '0',
                                  {
                                    $toString: {
                                      $round: [
                                        {
                                          $mod: [
                                            '$$activity.teamHours',
                                            global.constant.CONVERT_TO_HOURS,
                                          ],
                                        },
                                        0,
                                      ],
                                    },
                                  },
                                ],
                              },
                              else: {
                                $toString: {
                                  $round: [
                                    {
                                      $mod: [
                                        '$$activity.teamHours',
                                        global.constant.CONVERT_TO_HOURS,
                                      ],
                                    },
                                    0,
                                  ],
                                },
                              },
                            },
                          },
                          0,
                          2,
                        ],
                      },
                    ],
                  },
                  manHours: {
                    $let: {
                      vars: {
                        totalManHours: {
                          $multiply: [
                            {
                              $divide: ['$$activity.teamHours', global.constant.CONVERT_TO_HOURS],
                            },
                            '$teamSize',
                          ],
                        },
                      },
                      in: {
                        $concat: [
                          // Hours
                          {
                            $toString: {
                              $floor: '$$totalManHours',
                            },
                          },
                          ':',
                          // Minutes (padded to 2 digits)
                          {
                            $substrCP: [
                              {
                                $cond: {
                                  if: {
                                    $lt: [
                                      {
                                        $round: [
                                          {
                                            $multiply: [
                                              { $mod: ['$$totalManHours', 1] },
                                              global.constant.CONVERT_TO_HOURS,
                                            ],
                                          },
                                          0,
                                        ],
                                      },
                                      10,
                                    ],
                                  },
                                  then: {
                                    $concat: [
                                      '0',
                                      {
                                        $toString: {
                                          $round: [
                                            {
                                              $multiply: [
                                                { $mod: ['$$totalManHours', 1] },
                                                global.constant.CONVERT_TO_HOURS,
                                              ],
                                            },
                                            0,
                                          ],
                                        },
                                      },
                                    ],
                                  },
                                  else: {
                                    $toString: {
                                      $round: [
                                        {
                                          $multiply: [
                                            { $mod: ['$$totalManHours', 1] },
                                            global.constant.CONVERT_TO_HOURS,
                                          ],
                                        },
                                        0,
                                      ],
                                    },
                                  },
                                },
                              },
                              0,
                              2,
                            ],
                          },
                        ],
                      },
                    },
                  },
                },
              ],
            },
          },
        },
        totalTeamHours: {
          $concat: [
            // Total Hours
            {
              $toString: {
                $floor: {
                  $divide: [
                    { $sum: '$shiftActivitiesWithManHours.teamHours' },
                    global.constant.CONVERT_TO_HOURS,
                  ],
                },
              },
            },
            ':',
            // Total Minutes (padded to 2 digits)
            {
              $substrCP: [
                {
                  $cond: {
                    if: {
                      $lt: [
                        {
                          $round: [
                            {
                              $mod: [
                                { $sum: '$shiftActivitiesWithManHours.teamHours' },
                                global.constant.CONVERT_TO_HOURS,
                              ],
                            },
                            0,
                          ],
                        },
                        10,
                      ],
                    },
                    then: {
                      $concat: [
                        '0',
                        {
                          $toString: {
                            $round: [
                              {
                                $mod: [
                                  { $sum: '$shiftActivitiesWithManHours.teamHours' },
                                  global.constant.CONVERT_TO_HOURS,
                                ],
                              },
                              0,
                            ],
                          },
                        },
                      ],
                    },
                    else: {
                      $toString: {
                        $round: [
                          {
                            $mod: [
                              { $sum: '$shiftActivitiesWithManHours.teamHours' },
                              global.constant.CONVERT_TO_HOURS,
                            ],
                          },
                          0,
                        ],
                      },
                    },
                  },
                },
                0,
                2,
              ],
            },
          ],
        },
        totalManHours: {
          $let: {
            vars: {
              totalManHours: {
                $multiply: [
                  {
                    $divide: [
                      { $sum: '$shiftActivitiesWithManHours.teamHours' },
                      global.constant.CONVERT_TO_HOURS,
                    ],
                  },
                  '$teamSize',
                ],
              },
            },
            in: {
              $concat: [
                // Total Hours
                {
                  $toString: {
                    $floor: '$$totalManHours',
                  },
                },
                ':',
                // Total Minutes (padded to 2 digits)
                {
                  $substrCP: [
                    {
                      $cond: {
                        if: {
                          $lt: [
                            {
                              $round: [
                                {
                                  $multiply: [
                                    { $mod: ['$$totalManHours', 1] },
                                    global.constant.CONVERT_TO_HOURS,
                                  ],
                                },
                                0,
                              ],
                            },
                            10,
                          ],
                        },
                        then: {
                          $concat: [
                            '0',
                            {
                              $toString: {
                                $round: [
                                  {
                                    $multiply: [
                                      { $mod: ['$$totalManHours', 1] },
                                      global.constant.CONVERT_TO_HOURS,
                                    ],
                                  },
                                  0,
                                ],
                              },
                            },
                          ],
                        },
                        else: {
                          $toString: {
                            $round: [
                              {
                                $multiply: [
                                  { $mod: ['$$totalManHours', 1] },
                                  global.constant.CONVERT_TO_HOURS,
                                ],
                              },
                              0,
                            ],
                          },
                        },
                      },
                    },
                    0,
                    2,
                  ],
                },
              ],
            },
          },
        },
      },
    },
  ]);
};
