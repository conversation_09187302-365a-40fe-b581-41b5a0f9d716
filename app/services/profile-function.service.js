const ProfileFunction = require('../models/profile-function.model');

/**
 * Create ProfileFunction
 *
 * @param {*} profileFunction
 * @returns
 */
exports.createProfileFunction = async profileFunctionData => {
  return await ProfileFunction.create(profileFunctionData);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} profileFunction
 * @returns
 */

exports.updateProfileFunction = async (id, profileFunctionData) => {
  return ProfileFunction.findByIdAndUpdate(id, { $set: profileFunctionData }, { new: true });
};

/**
 *
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteProfileFunction = async (id, deletedAt) => {
  return ProfileFunction.findByIdAndUpdate({ _id: id }, { $set: deletedAt });
};

/**
 * Get All ProfileFunction
 * @returns
 */
exports.getAllProfileFunction = async (filter, page = '', perPage = '', sort = -1) => {
  let pipeline = [];

  pipeline.push({ $match: filter });

  sort && pipeline.push({ $sort: { sortOrder: sort } });

  const skip =
    page && page != '' && perPage && perPage != '' ? parseInt(page) * parseInt(perPage) : 0;
  const limit = perPage && perPage != '' ? parseInt(perPage) : undefined;

  if (skip) pipeline.push({ $skip: skip });
  if (limit) pipeline.push({ $limit: limit });

  pipeline.push(
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        as: 'account',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        as: 'createdBy',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'updatedBy',
        foreignField: '_id',
        as: 'updatedBy',
      },
    }
  );

  const unwindStages = fields =>
    fields.map(field => ({
      $unwind: {
        path: `$${field}`,
        preserveNullAndEmptyArrays: true,
      },
    }));

  pipeline.push(...unwindStages(['account', 'createdBy', 'updatedBy']));

  pipeline.push({
    $project: {
      _id: 1,
      name: 1,
      isActive: 1,
      sortOrder: 1,
      account: { _id: 1, name: 1 },
      createdBy: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      updatedBy: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  });

  return ProfileFunction.aggregate(pipeline);
};

/**
 * Get ProfileFunction By Name
 *
 * @param {*} profileFunctionName
 * @returns
 */
exports.getProfileFunctionByName = async filter => await ProfileFunction.findOne(filter);

/**
 *
 * Get ProfileFunction By Id
 * @param {*} id
 * @returns
 */
exports.getProfileFunctionById = async id => await ProfileFunction.findById(id);
