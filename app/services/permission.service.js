const Permission = require('../models/permission.model');

/**
 * Create Permission
 *
 * @param {*} Permission
 * @returns
 */
exports.createPermission = async requestData => {
  return await Permission.create(requestData);
};

/**
 * Get All Pemissions
 *
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getAllPermissions = async (page, perPage) => {
  return Permission.find()
    .limit(perPage)
    .skip(page * perPage)
    .populate('licence', 'name');
};

/**
 * Get Permission By Name
 *
 * @param {*} permissionName
 * @returns
 */
exports.getPermissionByName = async permissionName => {
  return await Permission.find({ name: permissionName });
};

/**
 * Get Permission By Licence id
 *
 * @param {*} licence
 * @returns
 */
exports.getPermissionsByLicenceId = async licence => {
  return await Permission.find({ licence });
};

/**
 * Get Permission By Filter
 *
 * @param {*} filter
 * @returns
 */
exports.getPermissionByFilter = async filter => {
  return await Permission.findOne(filter);
};

/**
 * Get All Pemissions
 *
 * @returns
 */
exports.getPermissions = async () => {
  return Permission.find();
};
