const EquipmentOrderHistory = require('../models/equipment-order-history.model');

/**
 * Create EquipmentOrderHistory
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipmentOrderHistory = async (requestData, session) => {
  return await EquipmentOrderHistory.create([requestData], session);
};

/**
 * Get EquipmentOrderHistory by id
 *
 * @param {*} id
 * @returns
 */
exports.getEquipmentOrderHistory = async id => {
  return await EquipmentOrderHistory.findById(id);
};

/**
 * Get EquipmentOrderHistory by filter
 *
 * @param {*} filter
 * @returns
 */
exports.getEquipmentOrderHistoryByFilter = async filter => {
  return await EquipmentOrderHistory.find(filter)
    .populate([
      {
        path: 'pmOrder',
        select: { orderNumber: 1, _id: 1, status: 1 },
        strictPopulate: false,
      },
      {
        path: 'account',
        select: { name: 1, _id: 0 },
        strictPopulate: false,
      },
      {
        path: 'equipment',
        select: {
          name: 1,
          _id: 1,
          equipmentNumber: 1,
          serialNumber: 1,
          equipmentImage: 1,
        },
        strictPopulate: false,
      },
      {
        path: 'equipmentType',
        select: { type: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'pmOrderManageEquipment',
        select: { status: 1, _id: 1 },
        strictPopulate: false,
      },
    ])
    .select({
      returnOrder: 0,
      createdBy: 0,
      updatedAt: 0,
      __v: 0,
      deletedBy: 0,
      deletedAt: 0,
      updatedBy: 0,
    });
};

/**
 * Update EquipmentOrderHistory
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateEquipmentOrderHistory = async (id, requestData, session) => {
  return await EquipmentOrderHistory.findByIdAndUpdate(
    id,
    { $set: requestData },
    { new: true, session }
  );
};

exports.getEquipmentOrderHistoryOneByFilter = async filter => {
  return await EquipmentOrderHistory.findOne(filter);
};

exports.updateEquipmentHistory = async (filter, requestData, session) => {
  return await EquipmentOrderHistory.findOneAndUpdate(
    filter,
    { $set: requestData },
    { new: true, session }
  );
};

/**
 * Get EquipmentOrderHistory by filter
 * @param {*} filter
 * @returns
 */
exports.getEquipmentHistory = async filter => {
  return await EquipmentOrderHistory.findOne(filter);
};

exports.updateEquipmentHistoryByFilter = async (filter, data, session) => {
  return await EquipmentOrderHistory.updateMany(filter, { $set: data }, { new: true, session });
};

/**
 * Remove returnOrder data in arrayObject
 *
 * @param {*} id
 * @param {*} returnOrder
 * @param {*} session
 * @returns
 */
exports.updateEquipmentForReturnOrder = async (id, returnOrder, session) => {
  return await EquipmentOrderHistory.updateOne(
    { _id: id },
    { $pull: { returnOrder: { returnOrder } } },
    { new: true, session }
  );
};

exports.updateLinkedEquipments = async (filter, data) => {
  return await EquipmentOrderHistory.findOneAndUpdate(filter, { $set: data });
};
