const CeNorms = require('../models/ce-norms.model');

/**
 * Create Ce Norms
 *
 * @param {*} requestData
 * @returns
 */
exports.createCeNorms = async requestData => {
  return await CeNorms.create(requestData);
};

/**
 * Get Ce Norms by Id
 *
 * @param {*} id
 * @returns
 */
exports.getCeNormsById = async id => {
  return await CeNorms.findOne({ _id: id }).populate([
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ]);
};

/**
 * Update Ce Norms
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateCeNorms = async (id, requestData) => {
  return await CeNorms.findByIdAndUpdate(id, { $set: requestData }, { new: true });
};

/**
 * Filter Ce Norms
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.getCeNorms = async (filter, page, perPage, sort) => {
  return await CeNorms.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .sort({ createdAt: sort ?? -1 })
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        strictPopulate: false,
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
      },
    ]);
};
