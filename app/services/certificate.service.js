const Certificate = require('../models/certificate.model');

//Utils
const commonfunctionUtils = require('../utils/common-function.utils');

/**
 * Create Certificate
 *
 * @param {*} certificateData
 * @returns
 */
exports.createCertificate = async certificateData => {
  return await Certificate.create(certificateData);
};

/**
 * Update Certificate By Id
 *
 * @param {*} id
 * @param {*} certificateData
 * @returns
 */
exports.updateCertificate = async (id, certificateData) => {
  return Certificate.findByIdAndUpdate(id, { $set: certificateData }, { new: true });
};

/**
 * Get All Certificates
 *
 * @param {*} filter
 * @returns
 */
exports.getAllCertificate = async filter => {
  return Certificate.find(filter, { _id: 0, createdAt: 0, updatedAt: 0, __v: 0 })
    .select('_id')
    .populate(commonfunctionUtils.cirtificate);
};

/**
 * Get Certificate By Name
 *
 * @param {*} filter
 * @returns
 */
exports.getCertificateByName = async filterData => Certificate.findOne(filterData);

/**
 * Get Certificate
 *
 * @param {*} account
 * @param {*} project
 * @returns
 */

exports.getCertificate = async (account, project, functionId) => {
  return Certificate.findOne({
    $and: [
      { project: project },
      { account: account },
      { function: functionId },
      { deletedAt: null },
    ],
  });
};

/**
 * Delete Project Certificates
 *
 * @param {*} certificateId
 * @returns
 */
exports.deleteProjectCertificate = async certificateId => {
  return await Certificate.updateMany(
    { certificates: { $in: [certificateId] } },
    { $pull: { certificates: { $in: [certificateId] } } },
    { multi: true }
  );
};

/**
 * Get Certificate
 *
 * @param {*} function
 * @param {*} project
 * @returns
 */
exports.getFunctionCertificatesByProjectId = async (filter, field, order, page, perPage, name) => {
  let aggregate = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
      },
    },
    { $unwind: { path: '$project', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        as: 'account',
      },
    },
    { $unwind: { path: '$account', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'functions',
        localField: 'function',
        foreignField: '_id',
        as: 'function',
      },
    },
    { $unwind: { path: '$function', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'certificate-types',
        localField: 'certificates',
        foreignField: '_id',
        as: 'certificates',
      },
    },
  ];
  if (name) {
    aggregate.push({
      $match: {
        $expr: {
          $or: [
            {
              $regexMatch: {
                input: '$function.functionName',
                regex: name,
                options: 'i',
              },
            },
            {
              $anyElementTrue: {
                $map: {
                  input: '$certificates',
                  as: 'certificate',
                  in: {
                    $regexMatch: {
                      input: '$$certificate.name',
                      regex: name,
                      options: 'i',
                    },
                  },
                },
              },
            },
          ],
        },
      },
    });
  }
  if (field) {
    aggregate.push({
      $sort: {
        'function.functionName': order,
      },
    });
  } else {
    aggregate.push({
      $sort: {
        createdAt: order,
      },
    });
  }
  if (page !== null && perPage !== null) {
    aggregate.push({ $skip: page * perPage }, { $limit: perPage });
  }

  aggregate.push({
    $project: {
      'project._id': 1,
      'project.title': 1,
      'account._id': 1,
      'account.name': 1,
      'function._id': 1,
      'function.functionName': 1,
      'certificates._id': 1,
      'certificates.name': 1,
      deletedAt: 1,
      deletedBy: 1,
      isDeletable: 1,
      isDefault: 1,
    },
  });
  return Certificate.aggregate(aggregate);
};
