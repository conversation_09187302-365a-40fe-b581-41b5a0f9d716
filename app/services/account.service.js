const Account = require('../models/account.model');
const { toObjectId } = require('../utils/common.utils');

/**
 * Get all admin users of the account.
 *
 * @returns
 */
exports.getAdminUsers = async (page, perPage) => {
  return Account.find()
    .populate([
      {
        path: 'user accountOwner',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        strictPopulate: false,
      },
    ])
    .limit(perPage)
    .skip(page * perPage)
    .sort({ createdAt: -1 });
};

/**
 * Create new Account
 *
 * @param {*} account
 * @returns
 */
exports.createAccount = async account => {
  return await Account.create(account);
};

/**
 * Find account by name
 *
 * @param {*} name
 * @returns
 */
exports.findAccountByName = async name => {
  return Account.find({ name: name });
};

/**
 * Get Account By Id
 *
 * @param {*} id
 * @returns
 */
exports.findAccountById = async id => {
  return await Account.findById(toObjectId(id));
};

/**
 * Update Account
 *
 * @param {*} id
 * @param {*} accountData
 */
exports.updateAccount = async (id, accountData) => {
  await Account.findByIdAndUpdate(id, { $set: accountData }, { new: true });
};

/**
 * Get all accounts
 *
 * @returns
 */
exports.getAccounts = async () => {
  return Account.find();
};
