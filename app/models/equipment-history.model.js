const mongoose = require('mongoose');

const equipmentHistory = new mongoose.Schema(
  {
    equipment: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment',
    },
    quantity: {
      type: Number,
      default: 0,
    },
    type: {
      type: String,
      enum: ['in', 'out'],
      default: 'in',
    },
    pmOrderId: {
      type: mongoose.Types.ObjectId,
      ref: 'pm-order-request',
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('equipment-history', equipmentHistory);
