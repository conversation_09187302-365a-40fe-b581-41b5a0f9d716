const { Schema, model } = require('mongoose');

const Logbook = new Schema(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: 'user',
      required: true,
    },
    project: { type: Schema.Types.ObjectId, ref: 'project' },
    description: {
      type: String,
      default: '',
    },
    createdBy: { type: Schema.Types.ObjectId, ref: 'user', required: true },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'user', default: null },
    deletedBy: { type: Schema.Types.ObjectId, ref: 'user', default: null },
    deletedAt: { type: Date, default: null },
  },
  { timestamps: true }
);

module.exports = model('logbook', Logbook);
