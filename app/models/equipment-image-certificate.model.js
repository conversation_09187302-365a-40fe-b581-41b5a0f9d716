const mongoose = require('mongoose');

const EquipmentImageCertificate = new mongoose.Schema(
  {
    equipment: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment',
      required: true,
    },
    equipmentImage: [
      {
        type: String,
      },
    ],
    certificate: {
      type: String,
    },
    certificateType: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-certificate-type',
      default: null,
    },
    certificateValidateDate: {
      type: Date,
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('equipment-image-certificate', EquipmentImageCertificate);
