const mongoose = require('mongoose');

const UserProjectReport = mongoose.Schema(
  {
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    report: {
      type: mongoose.Types.ObjectId,
      ref: 'report',
    },
    location: {
      type: mongoose.Types.ObjectId,
      ref: 'location',
    },
    asset: [
      {
        asset: {
          type: mongoose.Types.ObjectId,
          ref: 'asset',
        },
      },
    ],
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('user-project-report', UserProjectReport);
