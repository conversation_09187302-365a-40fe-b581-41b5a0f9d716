const mongoose = require('mongoose');

const reportDocument = mongoose.Schema(
  {
    name: {
      type: String,
    },
    document: {
      type: String,
    },
    userProjectReport: {
      type: mongoose.Types.ObjectId,
      ref: 'user-project-report',
    },
    isMerge: {
      type: Boolean,
      default: false,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('report-document', reportDocument);
