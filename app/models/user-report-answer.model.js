const mongoose = require('mongoose');

const UserReportAnswer = mongoose.Schema(
  {
    reportQuestion: {
      type: mongoose.Types.ObjectId,
      ref: 'report-question',
    },
    reportQuestionAnswer: {
      type: mongoose.Types.ObjectId,
      ref: 'report-question-answer',
    },
    userReport: {
      type: mongoose.Types.ObjectId,
      ref: 'user-report',
    },
    report: {
      type: mongoose.Types.ObjectId,
      ref: 'report',
    },
    answers: [
      {
        answerTitleId: {
          type: mongoose.Types.ObjectId,
          default: null,
        },
        answer: {
          type: String,
          default: '',
        },
        isActive: {
          type: Boolean,
          default: true,
        },
        isPrintable: {
          type: Boolean,
          default: true,
        },
      },
    ],
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

// Add indexes for better performance
UserReportAnswer.index({ report: 1 });
UserReportAnswer.index({ userReport: 1 });
UserReportAnswer.index({ reportQuestion: 1 });
UserReportAnswer.index({ reportQuestionAnswer: 1 });
UserReportAnswer.index({ account: 1 });
UserReportAnswer.index({ deletedAt: 1 });

module.exports = mongoose.model('user-report-answer', UserReportAnswer);
