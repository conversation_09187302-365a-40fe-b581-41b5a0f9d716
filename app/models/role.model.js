const mongoose = require('mongoose');

const Role = new mongoose.Schema(
  {
    title: {
      type: String,
    },
    description: {
      type: String,
      default: '',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isAssignAllProjects: {
      type: Boolean,
      default: false,
    },
    accessType: {
      type: String,
      enum: ['web', 'mobile', 'both'],
      default: 'mobile',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('role', Role);
