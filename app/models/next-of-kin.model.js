const mongoose = require('mongoose');

const NextOfKin = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'user',
    },
    kinName: {
      type: String,
      default: '',
    },
    relationship: {
      type: String,
      default: '',
    },
    kinStreet: {
      type: String,
      default: '',
    },
    kinCity: {
      type: String,
      default: '',
    },
    kinState: {
      type: String,
      default: '',
    },
    kinCountry: {
      type: String,
      default: '',
    },
    kinZip: {
      type: String,
      default: '',
    },
    kinArea: {
      type: String,
      default: '',
    },
    kinContactNumber: {
      type: Object,
      default: { number: '', code: '', in: '' },
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: new Date(),
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('nextofkin', NextOfKin);
