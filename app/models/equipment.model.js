const mongoose = require('mongoose');

const Equipment = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    equipmentNumber: {
      type: String,
    },
    serialNumber: {
      type: String,
      default: null,
    },
    productNumber: {
      type: String,
    },
    value: {
      type: Number,
    },
    weight: {
      type: Number,
    },
    equipmentType: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-type',
    },
    qrCode: [
      {
        code: { type: String },
        isActive: { type: Boolean },
        isSystemGenerated: { type: Boolean, default: true },
        createdDate: { type: Date },
        createdBy: { type: mongoose.Types.ObjectId, ref: 'user' },
      },
    ],
    equipmentImage: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
      },
    ],
    certificateType: [
      {
        certificateTypeId: { type: mongoose.Types.ObjectId, default: null },
        title: { type: String, default: '' },
        name: { type: String, default: '' },
        size: { type: String, default: '' },
        url: { type: String, default: '' },
        startDate: { type: Date, default: null },
        endDate: { type: Date, default: null },
      },
    ],
    warehouse: {
      type: mongoose.Types.ObjectId,
      ref: 'warehouse',
      required: true,
    },
    quantity: {
      type: Number,
    },
    equipmentLocationInWarehouse: {
      type: String,
    },
    equipmentCurrentLocation: {
      type: String,
    },
    equipmentRow: {
      type: String,
    },
    equipmentShelf: {
      type: String,
    },
    equipmentLocationFromDate: {
      type: Date,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    condition: {
      type: String,
      enum: ['ok', 'maintenance', 'write-off'],
      default: 'ok',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

// Add indexes for better query performance
Equipment.index({ account: 1, deletedAt: 1 }); // Main filter for account-based queries
Equipment.index({ equipmentType: 1 }); // For equipment type lookups
Equipment.index({ warehouse: 1 }); // For warehouse filtering
Equipment.index({ name: 1 }); // For name-based searches
Equipment.index({ serialNumber: 1 }); // For serial number searches
Equipment.index({ equipmentNumber: 1 }); // For equipment number searches
Equipment.index({ 'qrCode.code': 1, 'qrCode.isActive': 1 }); // For QR code searches
Equipment.index({ condition: 1 }); // For condition filtering
Equipment.index({ isActive: 1 }); // For active status filtering
Equipment.index({ createdAt: 1 }); // For sorting by creation date
Equipment.index({ updatedAt: 1 }); // For sorting by update date

// Compound indexes for common query patterns
Equipment.index({ account: 1, deletedAt: 1, equipmentType: 1 }); // Account + type filtering
Equipment.index({ account: 1, deletedAt: 1, warehouse: 1 }); // Account + warehouse filtering
Equipment.index({ account: 1, deletedAt: 1, condition: 1 }); // Account + condition filtering

module.exports = mongoose.model('equipment', Equipment);
