const mongoose = require('mongoose');

const ContractualDetail = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    identityProof: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        status: {
          type: String,
          default: 'pending',
        },
        comment: {
          type: String,
          default: '',
        },
        fromDate: { type: Date },
        toDate: { type: Date },
        isPrimary: { type: Boolean, default: true },
        isSecondary: { type: Boolean, default: false },
      },
    ],
    birthDate: {
      type: Date,
      default: '',
    },
    birthPlace: {
      type: String,
      default: '',
    },
    bankName: {
      type: String,
      default: '',
    },
    bankAccount: {
      type: String,
      default: '',
    },
    bicSwift: {
      type: String,
      default: '',
    },
    status: {
      type: String,
      default: 'pending',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updateBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    passport: {
      type: String,
      default: '',
    },
    secondaryPassport: {
      type: String,
      default: '',
    },
    nationalIdentificationNumber: {
      type: String,
      default: '',
    },
    employmentType: {
      type: String,
      enum: ['internal-employee', 'self-employed', 'external-employee'],
      default: 'internal-employee',
    },
    companyName: {
      type: String,
      default: '',
    },
    companyRegistrationNumber: {
      type: String,
      default: '',
    },
    companyVATNumber: {
      type: String,
      default: '',
    },
    companyAddress: {
      type: String,
      default: '',
    },
    accountHolderName: {
      type: String,
      default: '',
    },
    liabilityInsuranceId: { type: String },
    liabilityInsurance: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        fromDate: { type: Date },
        toDate: { type: Date },
      },
    ],
    healthInsuranceId: { type: String },
    isInsuranceArrangedByReynard: { type: Boolean, default: true },
    healthInsurance: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        fromDate: { type: Date },
        toDate: { type: Date },
      },
    ],
  },
  { timestamps: true }
);

ContractualDetail.virtual('users', {
  ref: 'user',
  localField: '_id',
  foreignField: 'userId',
});

module.exports = mongoose.model('contractual-detail', ContractualDetail);
