const mongoose = require('mongoose');

const Report = mongoose.Schema(
  {
    title: {
      type: String,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    type: {
      type: String,
      enum: ['location', 'asset_per_location', 'multiple_assets'],
    },
    status: {
      type: String,
      enum: ['open', 'completed', 'closed', 'submitted', 'checked', 'in-discussion'],
      default: 'open',
    },
    isProgressable: {
      type: Boolean,
      default: true,
    },
    isPublish: {
      type: Boolean,
      default: true,
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('report', Report);
