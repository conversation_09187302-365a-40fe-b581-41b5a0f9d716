const mongoose = require('mongoose');

const InventoryHistory = new mongoose.Schema(
  {
    equipment: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment',
    },
    status: {
      type: String,
      enum: [
        '',
        'in-stock',
        'linked',
        'pre-transit',
        'in-transit',
        'on-site',
        'return',
        'quarantine',
        'write off',
        'repaired',
        'partially-returned',
      ],
      default: '',
    },
    type: {
      type: String,
      enum: [
        '',
        'purchase',
        'cancel',
        'order',
        'order-received',
        'return',
        'return-received',
        'return-rejected',
        'maintenance',
        'repaired',
        'write-off',
      ],
      default: '',
    },
    orderNumber: {
      type: String,
      default: null,
    },
    quantity: {
      type: Number,
      default: 0,
    },
    inOut: {
      type: String,
      enum: ['', 'in', 'out'],
      default: '',
    },
    tracker: {
      type: String,
      default: null,
    },
    pmOrder: {
      type: mongoose.Types.ObjectId,
      ref: 'pm-order',
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

// Add indexes for better query performance
InventoryHistory.index({ equipment: 1, createdAt: -1 }); // For equipment-based queries with latest first
InventoryHistory.index({ equipment: 1, type: 1, createdAt: -1 }); // For equipment + type queries
InventoryHistory.index({ pmOrder: 1 }); // For PM order lookups
InventoryHistory.index({ account: 1, deletedAt: 1 }); // For account-based filtering
InventoryHistory.index({ status: 1 }); // For status filtering
InventoryHistory.index({ type: 1 }); // For type filtering
InventoryHistory.index({ createdAt: -1 }); // For date-based sorting

module.exports = mongoose.model('inventory-history', InventoryHistory);
