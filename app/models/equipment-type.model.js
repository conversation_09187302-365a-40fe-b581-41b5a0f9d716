const mongoose = require('mongoose');

const EquipmentType = new mongoose.Schema(
  {
    type: {
      type: String,
    },
    equipmentCategory: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-category',
    },
    equipmentUnit: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-unit',
    },
    currencyUnit: {
      type: mongoose.Types.ObjectId,
      ref: 'currency-unit',
    },
    price: {
      type: Number,
    },
    quantityType: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-quantity-type',
    },
    hsCode: {
      type: mongoose.Types.ObjectId,
      ref: 'hs-code',
    },
    certificateTypes: [
      {
        type: mongoose.Types.ObjectId,
      },
    ],
    showOnDpr: {
      type: Boolean,
      default: false,
    },
    ceNorms: {
      type: mongoose.Types.ObjectId,
      default: null,
      ref: 'ce-norms',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isTemporary: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

// Add indexes for better query performance
EquipmentType.index({ account: 1, deletedAt: 1 }); // For account-based queries
EquipmentType.index({ equipmentCategory: 1 }); // For category lookups
EquipmentType.index({ quantityType: 1 }); // For quantity type lookups
EquipmentType.index({ type: 1 }); // For type name searches
EquipmentType.index({ isActive: 1 }); // For active status filtering

module.exports = mongoose.model('equipment-type', EquipmentType);
