const mongoose = require('mongoose');

const Dpr = new mongoose.Schema(
  {
    version: {
      type: String,
    },
    prevVersion: {
      type: String,
    },
    dprNo: {
      type: String,
      required: true,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      default: null,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    dprData: [
      {
        type: Object,
      },
    ],
    dprDate: {
      type: Date,
      default: null,
    },
    status: {
      type: String,
      enum: ['open', 'submitted', 'in_discussion', 'closed'],
      default: 'open',
    },
    lastReload: {
      type: Boolean,
      default: false,
    },
    lastReloadTime: {
      type: String,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('dpr', Dpr);
