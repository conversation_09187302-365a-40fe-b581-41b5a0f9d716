const mongoose = require('mongoose');

const EquipmentOrderHistory = new mongoose.Schema(
  {
    pmOrder: {
      type: mongoose.Types.ObjectId,
      ref: 'pm-order',
    },
    pmOrderManageEquipment: {
      type: mongoose.Types.ObjectId,
      ref: 'pm-order-manage-equipment',
    },
    equipmentType: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-type',
    },
    equipment: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment',
    },
    returnOrder: [
      {
        returnOrder: { type: mongoose.Types.ObjectId, default: null },
        quantity: { type: Number, default: 0 },
      },
    ],
    type: {
      type: String,
      enum: ['', 'buy', 'retnal'],
      default: '',
    },
    status: {
      type: String,
      enum: [
        '',
        'pre-linked',
        'linked',
        'in-transit',
        'check-in',
        'pre-check-in',
        'pre-check-out',
        'check-out',
        'in-stock',
        'missing',
      ],
      default: '',
    },
    subStatus: {
      type: String,
      enum: ['', 'ok', 'transfer', 'received', 'quarantine', 'write-off'],
      default: '',
    },
    returnStatus: {
      type: String,
      enum: ['', 'pre-return', 'return', 'in-stock'],
      default: '',
    },
    checkinReasonStatus: {
      type: String,
      enum: ['', 'repair-required', 'certification-required', 'damaged', 'missing', 'other'],
      default: '',
    },
    checkinReason: {
      type: String,
      default: null,
    },
    wmDispatchReasonStatus: {
      type: String,
      enum: ['', 'repair-required', 'certification-required', 'damaged', 'missing', 'other'],
      default: '',
    },
    wmDispatchReason: {
      type: String,
      default: null,
    },
    wmDispatchQuantity: {
      type: Number,
      default: null,
    },
    pmReceivedQuantity: {
      type: Number,
      default: null,
    },
    pmDispatchQuantity: {
      type: Number,
      default: null,
    },
    wmReceivedQuantity: {
      type: Number,
      default: null,
    },
    comment: {
      type: String,
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('equipment-order-history', EquipmentOrderHistory);
