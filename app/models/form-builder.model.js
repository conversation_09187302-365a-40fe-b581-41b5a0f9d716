const mongoose = require('mongoose');

const formBuilder = new mongoose.Schema(
  {
    fieldName: {
      type: String,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    fieldType: {
      type: String,
    },
    fieldSortOrder: {
      type: Number,
    },
    optionValue: {
      type: Object,
      default: {},
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    cardType: {
      type: String,
      required: false,
      default: null,
    },
    range: {
      type: Object,
      default: {},
    },
    isRequired: {
      type: Boolean,
      required: false,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('form-builder', formBuilder);
