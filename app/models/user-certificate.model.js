const mongoose = require('mongoose');

const UserCertificate = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    cv: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        status: {
          type: String,
          default: 'pending',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    medical: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        fromDate: { type: Date },
        toDate: { type: Date },
        status: {
          type: String,
          default: 'pending',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    chesterStep: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        fromDate: { type: Date },
        toDate: { type: Date },
        status: {
          type: String,
          default: 'pending',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    gwoManualHandling: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        fromDate: { type: Date },
        toDate: { type: Date },
        status: {
          type: String,
          default: 'pending',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    gwoFristAid: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        fromDate: { type: Date },
        toDate: { type: Date },
        status: {
          type: String,
          default: 'pending',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    gwoFireAwareness: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        fromDate: { type: Date },
        toDate: { type: Date },
        status: {
          type: String,
          default: 'pending',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    gwoWorkingAtHeight: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        fromDate: { type: Date },
        toDate: { type: Date },
        status: {
          type: String,
          default: 'pending',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    gwoSeaSurvival: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
        fromDate: { type: Date },
        toDate: { type: Date },
        status: {
          type: String,
          default: 'pending',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updateBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
  },
  { timestamps: true }
);

UserCertificate.virtual('users', {
  ref: 'user',
  localField: '_id',
  foreignField: 'userId',
});

module.exports = mongoose.model('user-certificate', UserCertificate);
