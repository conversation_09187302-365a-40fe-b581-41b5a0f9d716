const mongoose = require('mongoose');

const ShoppingCart = new mongoose.Schema(
  {
    title: {
      type: String,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      default: null,
    },
    fromDate: {
      type: Date,
      default: '',
    },
    toDate: {
      type: Date,
      default: '',
    },
    status: {
      type: String,
      enum: ['pending', 'requested', 'rejected'],
      default: 'pending',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('shopping-cart', ShoppingCart);
