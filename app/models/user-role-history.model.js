const mongoose = require('mongoose');

const UserRoleHistory = new mongoose.Schema({
  user: {
    type: mongoose.Types.ObjectId,
    ref: 'user',
  },
  role: {
    type: mongoose.Types.ObjectId,
    ref: 'role',
  },
  status: {
    type: String,
    enum: ['current', 'previous'],
  },
  account: {
    type: mongoose.Types.ObjectId,
    ref: 'account',
  },
  createdBy: {
    type: mongoose.Types.ObjectId,
    ref: 'user',
    default: null,
  },
  createdAt: {
    type: Date,
    default: null,
  },
  updatedBy: {
    type: mongoose.Types.ObjectId,
    ref: 'user',
    default: null,
  },
  updatedAt: {
    type: Date,
    default: null,
  },
  deletedBy: {
    type: mongoose.Types.ObjectId,
    ref: 'user',
    default: null,
  },
  deletedAt: {
    type: Date,
    default: null,
  },
});

module.exports = mongoose.model('user-role-history', UserRoleHistory);
