const mongoose = require('mongoose');

const AccountLicence = new mongoose.Schema(
  {
    licence: {
      type: mongoose.Types.ObjectId,
      ref: 'licence',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    permission: {
      type: mongoose.Types.ObjectId,
      ref: 'permission',
    },
    isRequested: {
      type: Boolean,
      default: false,
    },
    isApproved: {
      type: Boolean,
      default: false,
    },
    isRejected: {
      type: Boolean,
      default: false,
    },
    reason: {
      type: String,
      default: null,
    },
    comment: {
      type: String,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('accountlicence', AccountLicence);
