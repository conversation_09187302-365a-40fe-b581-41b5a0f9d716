const mongoose = require('mongoose');

const Job = new mongoose.Schema(
  {
    jobId: {
      type: String,
    },
    subId: [
      {
        id: {
          type: String,
        },
        status: {
          type: String,
          enum: ['pending', 'success', 'failed'],
          default: 'pending',
        },
        comment: {
          type: String,
        },
      },
    ],
    status: {
      type: String,
      enum: ['pending', 'success', 'failed'],
      default: 'pending',
    },
    type: {
      type: String,
    },
    user: {
      type: String,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('job', Job);
