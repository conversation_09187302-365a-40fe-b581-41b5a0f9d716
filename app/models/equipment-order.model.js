const mongoose = require('mongoose');

const EquipmentOrder = new mongoose.Schema(
  {
    equipmentType: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-type',
      default: null,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      default: null,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
      default: null,
    },
    user: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    engineerRequestedQuantity: {
      type: Number,
      default: 0,
    },
    pmApprovedQuantity: {
      type: Number,
      default: 0,
    },
    engineerComment: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        time: {
          type: Date,
          default: null,
        },
        status: {
          type: String,
          default: '',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    approver: {
      type: String,
      default: '',
    },
    totalAmount: {
      type: Number,
      default: 0,
    },
    status: {
      type: String,
      enum: ['pending', 'queue', 'requested', 'rejected'],
      default: 'pending',
    },
    fromDate: {
      type: Date,
      default: '',
    },
    toDate: {
      type: Date,
      default: '',
    },
    remark: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        time: {
          type: Date,
          default: null,
        },
        status: {
          type: String,
          default: '',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    instruction: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        time: {
          type: Date,
          default: null,
        },
        status: {
          type: String,
          default: '',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    pmOrderId: {
      type: mongoose.Types.ObjectId,
      ref: 'pm-order-request',
      default: null,
    },
    pmOrderManageEquipment: {
      type: mongoose.Types.ObjectId,
      ref: 'pm-order-manage-equipment',
      default: null,
    },
    isTemporary: {
      type: Boolean,
      default: false,
    },
    temporaryProductName: {
      type: String,
      default: '',
    },
    shoppingCart: {
      type: mongoose.Types.ObjectId,
      ref: 'shopping-cart',
      default: null,
    },
    createdAt: {
      type: Date,
      default: null,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedAt: {
      type: Date,
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('equipment-order', EquipmentOrder);
