const mongoose = require('mongoose');

const WarehouseOwner = new mongoose.Schema(
  {
    warehouse: {
      type: mongoose.Types.ObjectId,
      ref: 'warehouse',
    },
    user: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdAt: {
      type: Date,
      default: null,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedAt: {
      type: Date,
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('warehouse-owner', WarehouseOwner);
