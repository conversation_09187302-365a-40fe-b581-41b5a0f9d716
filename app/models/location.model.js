const mongoose = require('mongoose');

const Location = new mongoose.Schema(
  {
    title: {
      type: String,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    isDeletable: {
      type: Boolean,
      default: true,
    },
    defaultIdentifier: {
      type: String,
      default: global.constant.NORMAL_DATA_IDENTIFIER, // DEFAULT_DATA_IDENTIFIER: for default project, NORMAL_DATA_IDENTIFIER: for normal project
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    reports: [
      {
        type: mongoose.Types.ObjectId,
        ref: 'report',
      },
    ],
    longitude: {
      type: Number,
      default: null,
    },
    latitude: {
      type: Number,
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('location', Location);
