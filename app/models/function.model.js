const mongoose = require('mongoose');

const FunctionModel = new mongoose.Schema(
  {
    functionName: {
      type: String,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
    certificates: [{ type: mongoose.Types.ObjectId }],
    isDeletable: {
      type: Boolean,
      default: true,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    isDefault: {
      type: <PERSON>olean,
      default: false,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('function', FunctionModel);
