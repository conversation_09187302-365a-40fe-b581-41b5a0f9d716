const mongoose = require('mongoose');

const Asset = new mongoose.Schema(
  {
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    fromLocation: {
      type: mongoose.Types.ObjectId,
      ref: 'location',
    },
    toLocation: {
      type: mongoose.Types.ObjectId,
      ref: 'location',
    },
    manufacturer: {
      type: String,
      default: '',
    },
    typeMm2: {
      type: String,
      default: '',
    },
    string: {
      type: mongoose.Types.ObjectId,
      ref: 'projectstring',
    },
    cableName: {
      type: String,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    reports: [
      {
        type: mongoose.Types.ObjectId,
        ref: 'report',
      },
    ],
    isDeletable: {
      type: Boolean,
      default: true,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('asset', Asset);
