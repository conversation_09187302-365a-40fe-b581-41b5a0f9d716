const mongoose = require('mongoose');

const User = new mongoose.Schema(
  {
    firstName: {
      type: String,
    },
    lastName: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      unique: true,
      required: true,
    },
    password: {
      type: String,
    },
    role: {
      type: mongoose.Types.ObjectId,
      ref: 'role',
      default: null,
    },
    contactNumber: {
      type: Object,
      default: { number: '', code: '', in: '' },
    },
    emergencyContactNumber: {
      type: Object,
      default: { number: '', code: '', in: '' },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    resourceNumber: {
      type: Number,
      default: 0,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      default: null,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    resetExpiresIn: {
      type: Date,
      default: null,
    },
    resetToken: {
      type: String,
      default: null,
    },
    secondaryEmail: {
      type: String,
      default: null,
    },
    nationality: {
      type: String,
      default: null,
    },
    motherLanguage: {
      type: String,
      default: null,
    },
    placeOfBirth: {
      type: String,
      default: null,
    },
    birthdate: {
      type: String,
      default: null,
    },
    maritalStatus: {
      type: String,
      default: null,
    },
    ssn: {
      type: String,
      default: null,
    },
    bloodGroup: {
      type: String,
      default: null,
    },
    allergies: {
      type: String,
      default: null,
    },
    companyName: {
      type: String,
      default: null,
    },
    companyLogo: {
      type: String,
      default: null,
    },
    profileImage: {
      type: String,
      default: null,
    },
    callingName: {
      type: String,
      default: null,
    },
    address: {
      type: String,
      default: null,
    },
    gender: {
      type: String,
      default: null,
    },
    street: {
      type: String,
      default: null,
    },
    zipCode: {
      type: String,
      default: null,
    },
    city: {
      type: String,
      default: null,
    },
    state: {
      type: String,
      default: null,
    },
    area: {
      type: String,
      default: null,
    },
    country: {
      type: String,
      default: null,
    },
    prefAirportDeprt: {
      type: String,
      default: null,
    },
    travelTimeToAirport: {
      type: String,
      default: null,
    },
    secondaryPrefAirportDeprt: {
      type: String,
      default: null,
    },
    travelTimeToSecondAirport: {
      type: String,
      default: null,
    },
    certificate: [
      {
        type: String,
        default: null,
      },
    ],
    contractualDetailId: {
      type: mongoose.Types.ObjectId,
      ref: 'contractual-detail',
      default: null,
    },
    certificateId: {
      type: mongoose.Types.ObjectId,
      ref: 'user-certificate',
      default: null,
    },
    businessPurpose: {
      type: Boolean,
      default: false,
    },
    emailCommunication: {
      type: Boolean,
      default: false,
    },
    drivingLicence: [
      {
        licenseNumber: {
          type: String,
          default: null,
        },
        issueDate: {
          type: Date,
          default: null,
        },
        expiryDate: {
          type: Date,
          default: null,
        },
      },
    ],
    clothesSize: {
      type: String,
      default: '',
    },
    shoeSize: {
      type: String,
      default: '',
    },
    windaId: {
      type: String,
      default: null,
    },
    curriculumVitae: [
      {
        name: {
          type: String,
          default: null,
        },
        url: {
          type: String,
          default: null,
        },
      },
    ],
    seamansBook: [
      {
        name: {
          type: String,
          default: null,
        },
        url: {
          type: String,
          default: null,
        },
        fromDate: {
          type: Date,
          default: null,
        },
        toDate: {
          type: Date,
          default: null,
        },
      },
    ],
    medical: [
      {
        questionId: {
          type: mongoose.Types.ObjectId,
          ref: 'question',
        },
        title: {
          type: String,
          default: null,
        },
        answer: {
          type: Boolean,
          default: false,
        },
        description: {
          type: String,
          default: null,
        },
      },
    ],
    gdpr: [
      {
        title: {
          type: String,
          default: null,
        },
        answer: {
          type: Boolean,
          default: false,
        },
      },
    ],
    profileFunction: {
      type: mongoose.Types.ObjectId,
      ref: 'profile-function',
      default: null,
    },
    rating: [
      {
        rating: {
          type: Number,
          default: 0,
        },
        comment: {
          type: String,
          default: null,
        },
        ratedBy: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
          default: null,
        },
        ratedAt: {
          type: Date,
          default: null,
        },
        isDeleted: {
          type: Boolean,
          default: false,
        },
      },
    ],
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updateBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    gdprAcknowledged: {
      type: Boolean,
      default: false,
    },
    gdprAcknowledgedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

User.virtual('accounts', {
  ref: 'account',
  localField: '_id',
  foreignField: 'accountOwner',
});

User.virtual('assign-projects', {
  ref: 'assign-project',
  localField: '_id',
  foreignField: 'user',
});

// Add indexes
User.index({ email: 1 }); // already unique, but explicit is good
User.index({ account: 1 });
User.index({ role: 1 });
User.index({ isActive: 1 });
User.index({ isDeleted: 1 });
User.index({ createdBy: 1 });
User.index({ updateBy: 1 });
User.index({ lastName: 1, firstName: 1 });

module.exports = mongoose.model('user', User);
