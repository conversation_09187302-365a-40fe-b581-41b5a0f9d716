const mongoose = require('mongoose');
const CertificateType = new mongoose.Schema(
  {
    name: {
      type: String,
      required: false,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    function: {
      type: mongoose.Types.ObjectId,
      ref: 'function',
    },
    validityDate: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true }
);
module.exports = mongoose.model('certificate-type', CertificateType);
