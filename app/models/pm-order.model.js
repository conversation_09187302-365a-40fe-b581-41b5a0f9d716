const mongoose = require('mongoose');

const PmOrder = new mongoose.Schema(
  {
    orderNumber: {
      type: String,
      default: null,
    },
    status: {
      type: String,
      enum: [
        'open',
        'requested',
        'approved',
        'rejected',
        'partially-pre-transit',
        'pre-transit',
        'in-transit',
        'partially-in-transit',
        'check-in',
        'check-out',
        'pre-check-out',
        'partially-check-in',
        'partially-check-out',
        'partially-in-stock',
        'in-stock',
        'missing',
      ],
      default: 'open',
    },
    fromDate: {
      type: Date,
      default: null,
    },
    toDate: {
      type: Date,
      default: null,
    },
    comments: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        time: {
          type: Date,
          default: null,
        },
        status: {
          type: String,
          default: '',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    remark: [
      {
        user: {
          type: mongoose.Types.ObjectId,
          ref: 'user',
        },
        time: {
          type: Date,
          default: null,
        },
        status: {
          type: String,
          default: '',
        },
        comment: {
          type: String,
          default: '',
        },
      },
    ],
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    shoppingCart: {
      type: mongoose.Types.ObjectId,
      ref: 'shopping-cart',
      default: null,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('pm-order', PmOrder);
