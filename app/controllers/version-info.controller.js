require('dotenv').config();

// services
const versionInfoService = require('../services/version-info.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

/**
 * Create VersionInfo
 *
 * @param {*} req
 * @param {*} res
 */
exports.createVersionInfo = async (req, res) => {
  try {
    let reqData = req.body;
    const responseData = await versionInfoService.createVersionInfo(reqData);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_VERSION_INFO, responseData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Check VersionInfo
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.checkVersionInfo = async (req, res) => {
  try {
    const { platform, versionCode, versionName } = req.body;
    const exist = await versionInfoService.getVersionInfoByFilter({
      platform: { $regex: platform, $options: 'i' },
    });

    if (!exist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.VERSION_INFO_NOT_EXIST));
    }

    let message = constantUtils.ALREADY_LATEST_VERSION;
    let response = {};

    if (versionCode < exist.versionCode) {
      const updateButtons = {
        title: 'Update',
        color: '029E3B',
        action: 'update',
      };

      const skipButtons = {
        title: 'Skip',
        color: '029E3B',
        action: 'skip',
      };

      message = constantUtils.LOWER_VERSION;
      response.updateBtn = updateButtons;
      response.skipBtn = skipButtons;
    } else if (versionCode > exist.versionCode) {
      const updateInfo = {
        versionCode,
        versionName,
        updateDate: new Date(),
      };
      await versionInfoService.updateVersionInfo(exist._id, updateInfo);
    }

    res.status(200).json(responseUtils.successResponse(message, response));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

exports.getAllVersionInfo = async (req, res) => {
  try {
    const versionInfoData = await versionInfoService.getAllVersionInfoByFilter();
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_VERSION_INFO, versionInfoData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

exports.updateVersionInfo = async (req, res) => {
  try {
    let { id } = req.params;
    let reqData = req.body;
    const exist = await versionInfoService.getVersionInfoByFilter({
      _id: id,
    });

    if (!exist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.VERSION_INFO_NOT_EXIST));
    }

    reqData.updateDate = new Date();
    const versionInfoData = await versionInfoService.updateVersionInfo(exist._id, reqData);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_VERSION_INFO, versionInfoData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

exports.deleteVersionInfo = async (req, res) => {
  try {
    let { id } = req.params;
    let reqData = req.body;
    const exist = await versionInfoService.getVersionInfoByFilter({
      _id: id,
    });

    if (!exist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.VERSION_INFO_NOT_EXIST));
    }

    reqData.updateDate = new Date();
    const versionInfoData = await versionInfoService.deleteVersionInfo(exist._id);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETED_VERSION_INFO, versionInfoData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};
