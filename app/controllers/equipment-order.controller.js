// Services
const equipmentOrderService = require('../services/equipment-order.service');
const memberService = require('../services/member.service');
const pmOrderManageEquipmentService = require('../services/pm-order-manage-equipment.service');
const pmOrderService = require('../services/pm-order.service');
const temporaryEquipmentService = require('../services/temporary-equipment.service');
const equipmentTypeService = require('../services/equipment-type.service');
const wmOrderService = require('../services/wm-order.service');
const shoppingCartService = require('../services/shopping-cart.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const { toObjectId } = require('../utils/common.utils');
const { validateSearch, updateSyncApiManage } = require('../utils/common-function.utils');
const commonUtils = require('../utils/common.utils');
const { getDateFilter } = require('../utils/common-function.utils');
const HTTP_STATUS = require('../utils/status-codes');
const { orderStatusArray } = require('../utils/json-format.utils');
/**
 * Create Equipment Order Request
 *
 * @param {*} req
 * @param {*} res
 */
exports.createEquipmentOrder = async (req, res) => {
  try {
    const requestData = req.body;
    const invalidItems = [];
    const validItems = [];

    for (const [index, element] of requestData.entries()) {
      const filter = {
        account: req.userData.account,
        project: toObjectId(element.project),
        isApprover: true,
      };

      const approver = await memberService.getAllMember(filter);

      if (approver.length === 0) {
        invalidItems.push({ index, data: element, error: 'Approver not found' });
      } else {
        validItems.push(element);
      }
    }

    for (const element of validItems) {
      element.approver = null;
      element.pmOrderId = null;
      element.account = req.userData.account;
      element.createdBy = req.userData._id;
      element.user = req.userData._id;

      let existFilter = {
        account: req.userData.account,
        project: toObjectId(element.project),
        equipmentType: toObjectId(element.equipmentType),
        user: req.userData._id,
        status: 'pending',
      };

      let exist = await equipmentOrderService.getEquipmentOrder(existFilter);

      if (exist) {
        element.engineerRequestedQuantity =
          exist.engineerRequestedQuantity + element.engineerRequestedQuantity;

        if (element.engineerComment && element.engineerComment.length > 0) {
          const newEngineerComment = [...exist.engineerComment, element.engineerComment[0]];
          element.engineerComment = newEngineerComment;
        } else {
          element.engineerComment = [...exist.engineerComment];
        }

        await equipmentOrderService.updateEquipmentOrder(exist._id, element);
      } else {
        await equipmentOrderService.createEquipmentOrder(element);
      }
    }

    const hasFailedItems = invalidItems.length > 0;

    if (hasFailedItems) {
      return res
        .status(406)
        .json(responseUtils.errorResponse(constantUtils.FAILED_ITEMS, invalidItems));
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT_ORDER_REQUEST));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Equipment order By Id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getEquipmentOrderById = async (req, res) => {
  try {
    let equipmentOrderId = req.params.id;

    const filter = {
      _id: toObjectId(equipmentOrderId),
      account: req.userData.account,
      deletedAt: null,
    };

    let response = await equipmentOrderService.getEquipmentOrder(filter);

    if (!response) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_ORDER_REQUEST_NOT_FOUND));
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_ORDER_REQUEST, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * List Equipment Order
 *
 * @param {*} req
 * @param {*} res
 */
exports.listEquipmentOrder = async (req, res) => {
  try {
    const status = 'status' in req.query ? req.query.status : 'pending';
    let search = await validateSearch(req.query.search);
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let project = req.query.project ? commonUtils.toObjectId(req.query.project) : null;

    if (!search && search !== '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    let filter = {
      account: req.userData.account,
      deletedAt: null,
    };

    if (
      req?.assignedProjectList &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      filter.project = { $in: req.assignedProjectList };
    }

    // Add project filter if project status is provided
    if ('projectStatus' in req.query) {
      filter = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filter,
        project
      );
    }

    if (
      req?.assignedProjectList === undefined &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      return res
        .status(200)
        .json(responseUtils.errorResponse(constantUtils.GET_EQUIPMENT_ORDER_REQUEST, []));
    }

    const response = await equipmentOrderService.listEquipmentOrder(
      filter,
      status,
      search,
      page,
      perPage,
      sort
    );

    if (!response) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_ORDER_REQUEST_NOT_FOUND));
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_ORDER_REQUEST, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * List Equipment Order
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.listShoppingCartProjectEquipmentOrder = async (req, res) => {
  try {
    const status = 'status' in req.query ? req.query.status : 'pending';
    let search = await validateSearch(req.query.search);
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    const project = req.query.project ? commonUtils.toObjectId(req.query.project) : null;

    if (!search && search !== '') {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    let filter = {
      account: req.userData.account,
      deletedAt: null,
    };

    if (
      req?.assignedProjectList &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      filter.project = { $in: req.assignedProjectList };
    }

    // Add project filter if project status is provided
    if ('projectStatus' in req.query) {
      filter = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filter,
        project
      );
    }

    if (
      req?.assignedProjectList === undefined &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      return res
        .status(HTTP_STATUS.OK)
        .json(responseUtils.errorResponse(constantUtils.GET_EQUIPMENT_ORDER_REQUEST, []));
    }

    const response = await equipmentOrderService.listShoppingCartProjectEquipmentOrder(
      filter,
      status,
      search,
      page,
      perPage,
      sort
    );

    if (!response) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_ORDER_REQUEST_NOT_FOUND));
    }

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_ORDER_REQUEST, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Change Order Status
 *
 * @param {*} req
 * @param {*} res
 */
exports.changeOrderStatus = async (req, res) => {
  try {
    let data = {
      status: req.body.status,
      ...(req.body.remark && { remark: req.body.remark }),
    };

    let filter = {
      account: req.userData.account,
      ...(req.query.project && { project: toObjectId(req.query.project) }),
      ...(req.query.equipmentType && {
        equipmentType: toObjectId(req.query.equipmentType),
      }),
      ...(req.query.equipmentRequestId && {
        _id: toObjectId(req.query.equipmentRequestId),
      }),
      ...(req.query.existingStatus && { status: req.query.existingStatus }),
      deletedAt: null,
    };

    let responseData = await equipmentOrderService.getEquipmentOrders(filter);
    let response = await equipmentOrderService.changeOrderStatus(filter, data);

    if (req.body.status && req.body.status === 'rejected') {
      await this.handleRejection(req, responseData);
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CHANGE_EQUIPMENT_ORDER_REQUEST, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.handleRejection = async (req, responseData) => {
  for (const element of responseData) {
    let pmOrderManageEquipmentId =
      element?.pmOrderManageEquipment !== null ? toObjectId(element.pmOrderManageEquipment) : null;
    const pmOrderManageData = await pmOrderManageEquipmentService.rejectPMOrderManageEquipment(
      pmOrderManageEquipmentId
    );

    if (pmOrderManageData) {
      let data = await pmOrderManageEquipmentService.getPMOrderManageEquipment({
        account: req.userData.account,
        pmOrder: toObjectId(element.pmOrderId),
        deletedAt: null,
      });

      if (data.length === 0) {
        await pmOrderService.rejectPMOrder(toObjectId(element.pmOrderId));
      }
    }
  }
};

/**
 * Get Equipment Types order history
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentTypesOrderHistory = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let project = req.query.project ? req.query.project : '';
    let day = req.query.day ? req.query.day : 'all';
    let sort = req.query?.sort === 'asc' ? { createdAt: 1 } : { createdAt: -1 };
    let search = await validateSearch(req.query.search);

    if (!search && search !== '') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    if (req.query?.sortName) {
      sort =
        req.query?.sortName === 'asc'
          ? { 'equipmentTypeDetails.type': 1 }
          : { 'equipmentTypeDetails.type': -1 };
    }

    const filterDate = await getDateFilter(day);

    let filter = {
      account: req.userData.account,
      user: req.userData._id,
      ...(project ? { project: toObjectId(project) } : {}),
      ...(filterDate ? { createdAt: { $gte: filterDate } } : {}),
      deletedAt: null,
    };

    const equipmentOrder = await equipmentOrderService.getEquipmentOrderHistory(
      filter,
      search,
      sort,
      page,
      perPage
    );

    res
      .status(200)
      .json(
        responseUtils.successResponse(
          constantUtils.GET_EQUIPMENT_TYPE_ORDER_HISTORY,
          equipmentOrder
        )
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * List Equipment Order
 *
 * @param {*} req
 * @param {*} res
 */
exports.getProjectOrderList = async (req, res) => {
  try {
    const status = 'status' in req.query ? req.query.status : 'pending';
    let search = await validateSearch(req.query.search);
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let project = req.query.project ? toObjectId(req.query.project) : null;

    if (!search && search !== '') {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    let filter = {
      ...(project !== null && { project }),
      account: req.userData.account,
      deletedAt: null,
    };

    if (
      project === null &&
      req?.assignedProjectList &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      filter.project = { $in: req.assignedProjectList };
    }

    // Add project filter if project status is provided
    if ('projectStatus' in req.query) {
      filter = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filter,
        ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
          req.userData.role.title
        )
          ? { $in: req.assignedProjectList }
          : project
      );
    }

    if (
      req?.assignedProjectList === undefined &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_ORDER_REQUEST_NOT_FOUND, []));
    }
    const response = await equipmentOrderService.listEquipmentRequestedOrders(
      filter,
      status,
      search,
      page,
      perPage,
      sort
    );

    if (!response) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_ORDER_REQUEST_NOT_FOUND));
    }

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_ORDER_REQUEST, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get shopping cart wise project equipment type
 *
 * @param {*} req
 * @param {*} res
 */
exports.getShoppingCartWiseProjectEquipmentType = async (req, res) => {
  try {
    const status = 'status' in req.query ? req.query.status : 'pending';
    let search = await validateSearch(req.query.search);
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let project = req.query.project ? toObjectId(req.query.project) : null;

    if (!search && search !== '') {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_SEARCH));
    }

    let filter = {
      ...(project !== null && { project }),
      status,
      account: req.userData.account,
      deletedAt: null,
    };

    if (
      req?.assignedProjectList &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      filter.project = { $in: req.assignedProjectList };
    }

    // Add project filter if project status is provided
    if ('projectStatus' in req.query) {
      filter = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filter,
        project
      );
    }

    if (
      req?.assignedProjectList === undefined &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_ORDER_REQUEST_NOT_FOUND, []));
    }
    const response = await equipmentOrderService.shoppingCartWiseProjectEquipments(
      filter,
      search,
      page,
      perPage,
      sort
    );

    if (!response) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_ORDER_REQUEST_NOT_FOUND));
    }

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_ORDER_REQUEST, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * List Equipment Order By Project
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentOrderByProject = async (req, res) => {
  try {
    const status = 'status' in req.query ? req.query.status : 'pending';
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let filter = {
      account: req.userData.account,
      project: toObjectId(req.params.id),
      deletedAt: null,
    };

    const [response] = await equipmentOrderService.getEquipmentOrderByProject(
      filter,
      status,
      page,
      perPage,
      sort
    );

    if (!response) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_ORDER_REQUEST_NOT_FOUND));
    }
    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_ORDER_REQUEST, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * List Equipment Order By Shopping cart
 *
 * @param {*} req
 * @param {*} res
 */
exports.getEquipmentOrderByShoppingCart = async (req, res) => {
  try {
    const status = 'queue';
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let response;

    let filter = {
      account: req.userData.account,
      shoppingCart: toObjectId(req.params.id),
      deletedAt: null,
      status: 'queue',
    };

    let shoppingCartHasItems = await equipmentOrderService.getEquipmentOrder(filter);

    if (!shoppingCartHasItems) {
      response = await shoppingCartService.getShoppingCartByFilter({
        _id: req.params.id,
      });

      response = {
        _id: response.project._id,
        projectName: response.project.title,
        shoppingCartId: response._id,
        shoppingCartTitle: response.title,
        shoppingCartFromDate: response.fromDate,
        shoppingCartToDate: response.toDate,
        totalDays: await commonUtils.calculateTotalDays(response.fromDate, response.toDate),
        pmOrderId: null,
        equipmentTypes: [],
        totalEquipmentType: 0,
        totalQuantity: 0,
        createdAt: response.createdAt,
      };
    } else {
      [response] = await equipmentOrderService.getEquipmentOrderByShoppingCart(
        filter,
        status,
        page,
        perPage,
        sort
      );
    }

    if (!response) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_ORDER_REQUEST_NOT_FOUND));
    }
    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT_ORDER_REQUEST, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/* ********************* Version 2 Controllers ********************* */
/**
 * Create Equipment Order Request - V2
 *
 * @param {*} req
 * @param {*} res
 */
exports.createEquipmentOrderV2 = async (req, res) => {
  try {
    const { equipmentOrders, tempOrders } = req.body;
    const invalidItems = [];
    const validItems = [];
    let orderRequestId;

    for (const [index, element] of equipmentOrders.entries()) {
      const filter = {
        account: req.userData.account,
        project: toObjectId(element.project),
        isApprover: true,
      };

      const approver = await memberService.getAllMember(filter);

      if (approver.length === 0) {
        invalidItems.push({ index, data: element, error: constantUtils.NO_APPROVER });
      } else {
        validItems.push(element);
      }
    }

    for (const element of validItems) {
      element.approver = null;
      element.pmOrderId = null;
      element.account = req.userData.account;
      element.createdBy = req.userData._id;
      element.user = req.userData._id;

      let existFilter = {
        account: req.userData.account,
        project: toObjectId(element.project),
        equipmentType: toObjectId(element.equipmentType),
        user: req.userData._id,
        status: 'pending',
      };

      let exist = await equipmentOrderService.getEquipmentOrder(existFilter);

      if (exist) {
        element.engineerRequestedQuantity =
          exist.engineerRequestedQuantity + element.engineerRequestedQuantity;

        if (element.engineerComment && element.engineerComment.length > 0) {
          const newEngineerComment = [...exist.engineerComment, element.engineerComment[0]];
          element.engineerComment = newEngineerComment;
        } else {
          element.engineerComment = [...exist.engineerComment];
        }

        await equipmentOrderService.updateEquipmentOrder(exist._id, element);
        orderRequestId = exist._id.toString();
      } else {
        const newOrder = await equipmentOrderService.createEquipmentOrder(element);
        orderRequestId = newOrder._id.toString();
      }
    }

    const hasFailedItems = invalidItems.length > 0;

    if (hasFailedItems) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.FAILED_ITEMS, invalidItems));
    }

    // Create Temp Equipment Order
    const tempEquipmentOrder = await this.createTempEquipmentOrder({
      tempOrders,
      account: req.userData.account,
      user: req.userData._id,
    });

    if (!tempEquipmentOrder.status) {
      return res
        .status(HTTP_STATUS.INTERNAL_SERVER_ERROR)
        .json(responseUtils.errorResponse(tempEquipmentOrder.error));
    }
    await this.commonUpdateSyncApiManage(req.userData.account);
    res.status(HTTP_STATUS.OK).json(
      responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT_ORDER_REQUEST, {
        requestId: orderRequestId,
      })
    );
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Create Temp Equipment Order
 *
 * @param {*} requestData
 * @returns
 */
exports.createTempEquipmentOrder = async requestData => {
  try {
    const { tempOrders, account, user } = requestData;

    // Get Temp Equipment Setup Data
    const getTempEquipmentSetupData = await temporaryEquipmentService.checkAndGetTempEquipmentSetup(
      account
    );

    if (getTempEquipmentSetupData) {
      // Create Temp Equipment Order
      for (let element of tempOrders) {
        // Create Temp Equipment Type
        const tempEquipmentType = await temporaryEquipmentService.createTempEquipmentType(
          {
            productName: element.productName,
            account,
            user,
          },
          getTempEquipmentSetupData
        );

        // Create Temp Equipment Order
        if (tempEquipmentType?._id) {
          let existFilter = {
            equipmentType: tempEquipmentType._id,
            isTemporary: true,
            temporaryProductName: element.productName,
            project: toObjectId(element.project),
            user,
            account,
            status: 'pending',
            engineerRequestedQuantity: element.engineerRequestedQuantity,
            engineerComment: element.engineerComment,
            createdBy: user,
            createdAt: new Date(),
          };

          await equipmentOrderService.createEquipmentOrder(existFilter);
        }
      }
    }

    return {
      status: true,
    };
  } catch (error) {
    return {
      status: false,
      error: error.message,
    };
  }
};

/**
 * Change Order Status - V2
 *
 * @param {*} req
 * @param {*} res
 */
exports.changeOrderStatusV2 = async (req, res) => {
  try {
    let data = {
      status: req.body.status,
      ...(req.body.remark && { remark: req.body.remark }),
    };

    let filter = {
      account: req.userData.account,
      ...(req.query.project && { project: toObjectId(req.query.project) }),
      ...(req.query.equipmentType && {
        equipmentType: toObjectId(req.query.equipmentType),
      }),
      ...(req.query.equipmentRequestId && {
        _id: toObjectId(req.query.equipmentRequestId),
      }),
      ...(req.query.existingStatus && { status: req.query.existingStatus }),
      deletedAt: null,
    };

    let responseData = await equipmentOrderService.getEquipmentOrders(filter);
    let response = await equipmentOrderService.changeOrderStatus(filter, data);

    if (req.body.status && req.body.status === 'rejected') {
      await this.handleRejectionV2(req, responseData);
    }

    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.CHANGE_EQUIPMENT_ORDER_REQUEST, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * On rejected status it will delete the temporary equipment type - V2
 *
 * @param {*} req
 * @param {*} responseData
 */
exports.handleRejectionV2 = async (req, responseData) => {
  for (const element of responseData) {
    // remove temporary equipment type
    if (element?.isTemporary) {
      await equipmentTypeService.deleteEquipmentType(element.equipmentType, {
        deletedAt: new Date(),
        deletedBy: req.userData._id,
      });
    }
  }
};

/**
 * List Equipment Order
 *
 * @param {*} req
 * @param {*} res
 */
exports.getPmEquipmentCounter = async (req, res) => {
  try {
    const pmEquipmentCount = [
      { type: 'projectInvetory', count: false },
      { type: 'equipmentRequest', count: false },
      { type: 'request', count: 0 },
      { type: 'shoppingCart', count: 0 },
      { type: 'checkIn', count: 0 },
      { type: 'returnCart', count: false },
    ];

    let filterData = {
      account: req.userData.account,
      deletedAt: null,
    };
    const project = req.query.project ? commonUtils.toObjectId(req.query.project) : null;

    if ('projectStatus' in req.query) {
      filterData = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filterData,
        project
      );
    }

    delete filterData?.projectStatus;

    if (
      req?.assignedProjectList &&
      !orderStatusArray.includes(req.query.status) &&
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      )
    ) {
      filterData.project = { $in: req.assignedProjectList };
    }

    const pendingFilter = { ...filterData, status: 'pending' };
    const requestResponse = await equipmentOrderService.listEquipmentOrderCount(pendingFilter);

    // Update `request` count
    pmEquipmentCount.find(item => item.type === 'request').count = requestResponse.length;

    const queueFilter = { ...filterData, status: 'queue' };
    const shoppingCartresponse = await equipmentOrderService.listShoppingCartOrderCount(
      queueFilter
    );
    const filteredResponse = shoppingCartresponse.filter(item => item._id !== null);

    pmEquipmentCount.find(item => item.type === 'shoppingCart').count = filteredResponse.length;

    const checkInOrders = await wmOrderService.getPMOrderListCount(filterData, {
      $in: ['in-transit', 'partially-in-transit'],
    });

    pmEquipmentCount.find(item => item.type === 'checkIn').count = checkInOrders.length;

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.PM_EQUIPMENT_COUNTER, pmEquipmentCount));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

exports.createEquipmentOrderFromShoppingCartV2 = async (req, res) => {
  try {
    const { title, project, fromDate, toDate, equipmentOrders } = req.body;
    let { shoppingCart } = req.body;

    if (!shoppingCart) {
      const reqData = {
        title,
        project,
        fromDate,
        toDate,
        account: req.userData.account,
        createdBy: req.userData._id,
      };

      [shoppingCart] = await shoppingCartService.createShoppingCart(reqData);
      shoppingCart = shoppingCart._id;
    }

    const invalidItems = [];
    const validItems = [];
    let orderRequestId, newOrder, tempEquipmentQuantity;

    for (const [index, element] of equipmentOrders.entries()) {
      const filter = {
        account: req.userData.account,
        project: toObjectId(element.project),
        isApprover: true,
      };

      const approver = await memberService.getAllMember(filter);

      if (approver.length === 0) {
        invalidItems.push({ index, data: element, error: constantUtils.NO_APPROVER });
      } else {
        validItems.push(element);
      }
    }

    for (const element of validItems) {
      element.approver = null;
      element.pmOrderId = null;
      element.account = req.userData.account;
      element.createdBy = req.userData._id;
      element.user = req.userData._id;

      let existFilter = {
        equipmentType: toObjectId(element.equipmentType),
        account: req.userData.account,
        project: toObjectId(element.project),
        user: req.userData._id,
        status: 'queue',
        shoppingCart: shoppingCart,
      };

      let exist = await equipmentOrderService.getEquipmentOrder(existFilter);

      if (exist) {
        tempEquipmentQuantity = element.engineerRequestedQuantity;
        element.engineerRequestedQuantity =
          exist.engineerRequestedQuantity + element.engineerRequestedQuantity;

        if (element.engineerComment && element.engineerComment.length > 0) {
          const newEngineerComment = [...exist.engineerComment, element.engineerComment[0]];
          element.engineerComment = newEngineerComment;
        } else {
          element.engineerComment = [...exist.engineerComment];
        }

        await equipmentOrderService.updateEquipmentOrder(exist._id, element);
        await equipmentOrderService.updateEquipmentOrderIncrementPmOrderQuantity(exist._id, {
          $inc: { pmApprovedQuantity: tempEquipmentQuantity },
        });
        orderRequestId = exist._id.toString();
      } else {
        newOrder = await equipmentOrderService.createEquipmentOrder({
          ...element,
          status: 'queue',
          shoppingCart: shoppingCart,
          pmApprovedQuantity: element.engineerRequestedQuantity,
        });
        orderRequestId = newOrder._id.toString();
      }

      // Create PM Order
      let pmOrderData = await pmOrderService.searchPMOrder({ shoppingCart: shoppingCart });

      if (!pmOrderData) {
        pmOrderData = await pmOrderService.createPMOrder({
          account: req.userData.account,
          project: toObjectId(element.project),
          createdBy: req.userData._id,
          shoppingCart: shoppingCart._id,
        });
      }

      let isPMOrderEquipmentExist = await pmOrderManageEquipmentService.getPMOrderManageEquipment({
        account: req.userData.account,
        pmOrder: pmOrderData._id,
        equipmentType: toObjectId(element.equipmentType),
        createdBy: req.userData._id,
      });

      if (isPMOrderEquipmentExist.length == 0) {
        const isPMOrderEquipmentExist =
          await pmOrderManageEquipmentService.createPMOrderManageEquipment({
            account: req.userData.account,
            pmOrder: pmOrderData._id,
            equipmentType: toObjectId(element.equipmentType),
            pmRequestedQuantity: element.engineerRequestedQuantity,
            createdBy: req.userData._id,
          });

        if (String(newOrder.equipmentType) == String(isPMOrderEquipmentExist.equipmentType)) {
          await equipmentOrderService.updateEquipmentOrder(newOrder._id, {
            pmOrderId: pmOrderData._id,
            pmOrderManageEquipment: isPMOrderEquipmentExist._id,
          });
        }
      } else {
        await pmOrderManageEquipmentService.updatePMOrderManageEquipmentQuantity(
          isPMOrderEquipmentExist[0]._id,
          {
            $inc: { pmRequestedQuantity: tempEquipmentQuantity },
          }
        );

        if (String(exist.equipmentType._id) == String(isPMOrderEquipmentExist[0].equipmentType)) {
          await equipmentOrderService.updateEquipmentOrder(exist._id, {
            pmOrderId: pmOrderData._id,
          });
        }
      }
    }

    const hasFailedItems = invalidItems.length > 0;

    if (hasFailedItems) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.FAILED_ITEMS, invalidItems));
    }

    res.status(HTTP_STATUS.OK).json(
      responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT_ORDER_REQUEST, {
        requestId: orderRequestId,
      })
    );
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await updateSyncApiManage({
    syncApis: ['equipmentConfig'],
    account,
  });
};
