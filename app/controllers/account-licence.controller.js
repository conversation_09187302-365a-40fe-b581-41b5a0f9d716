require('dotenv').config();

// services
const accountLicenceService = require('../services/account-licence.service');
const authService = require('../services/auth.service');
const roleService = require('../services/role.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const RoleAgreement = require('../models/role-agreement.model');

/**
 * Create AccountLicence
 *
 * @param {*} req
 * @param {*} res
 */
exports.requestAccountLicence = async (req, res) => {
  try {
    let { account } = req.body;

    const accountId = await authService.getAuthAccountId(req.headers.authorization);

    req.body.account = account != null ? account : accountId;

    const requestAccountLicence = await accountLicenceService.requestAccountLicence(req.body);

    if (requestAccountLicence) {
      // create or update role agreement on licence request
      setTimeout(async () => {
        await accountLicenceService.modifyRoleAgreements(req.body, req.userData);
      }, 1000);
      // response
      res.status(200).json(responseUtils.successResponse(constantUtils.CREATE_ACCOUNT_LICENCE));
    }
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

exports.getAccountLicenceByAccount = async (req, res) => {
  try {
    const accountId = req.userData.account;
    if (!accountId) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.NO_ACCOUNT_LICENCE));
    }
    const accountLicenceData = await accountLicenceService.getAccountLicenceByAccount(accountId);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_ALL_LIST, accountLicenceData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

exports.getPendingRequests = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 10;
    const filter = {
      isRequested: true,
      isApproved: false,
      isRejected: false,
    };
    const accountLicenceData = await accountLicenceService.getAllAccountLicence(
      page,
      perPage,
      filter
    );
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_ALL_LIST, accountLicenceData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Approve or Reject Account Licence
 *
 * @param {*} req
 * @param {*} res
 */
exports.actionForApproveOrReject = async (req, res) => {
  try {
    const id = req.params.id;
    const requestData = req.body;

    if (typeof requestData.action === 'undefined' || requestData.action === '') {
      const error = new Error('Please give the action - accept or reject');
      error.status = 422;
      throw error;
    }

    switch (requestData.action) {
      case 'approve':
        requestData.isRequested = false;
        requestData.isApproved = true;
        requestData.isRejected = false;
        break;
      case 'reject':
        requestData.isRequested = false;
        requestData.isApproved = false;
        requestData.isRejected = true;
        break;
    }
    delete requestData.action;
    const accountLicenceData = await accountLicenceService.updateAccountLicence(id, requestData);
    if (requestData.isApproved) {
      const roles = await roleService.getRoleIdOfAccount(accountLicenceData.account);
      roles.forEach(async role => {
        const isAdminRole = role.title === global.constant.ADMIN_ROLE;
        let roleAgreementData = {
          role: role._id,
          accountLicence: id,
          agreement: {
            create: isAdminRole,
            read: isAdminRole,
            update: isAdminRole,
            delete: isAdminRole,
          },
          account: accountLicenceData.account,
          createdBy: req.userData.id,
          updatedBy: req.userData.id,
        };
        let roleAgreement = await RoleAgreement.findOne({
          role: role._id,
          accountLicence: id,
          account: accountLicenceData.account,
        });

        if (roleAgreement === null) {
          await RoleAgreement.create(roleAgreementData);
        } else {
          await RoleAgreement.findByIdAndUpdate(
            roleAgreement._id,
            { $set: roleAgreementData },
            { new: true }
          );
        }
      });
    }
    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.UPDATE_ACCOUNT_LICENCE, accountLicenceData)
      );
  } catch (err) {
    const status = err.status ?? 500;
    res.status(status).json(responseUtils.errorResponse(err.message));
  }
};
