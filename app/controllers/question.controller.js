// services
const questionService = require('../services/question.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');

/**
 * Create Question
 *
 * @param {*} req
 * @param {*} res
 */
exports.createQuestion = async (req, res) => {
  try {
    let reqData = req.body;

    const exist = await questionService.getQuestionByTitle(reqData.titile);

    if (exist) {
      res.status(400).json(responseUtils.errorResponse(constantUtils.QUESTION_EXIST));
    }

    reqData.account = req.userData.account;
    reqData.createdBy = req.userData._id;

    const questionData = await questionService.createQuestion(reqData);

    if (questionData) {
      res
        .status(200)
        .json(responseUtils.successResponse(constantUtils.CREATE_QUESTION, questionData));
    }
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Get Question List
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllQuestions = async (req, res) => {
  try {
    let filter = req.query;
    let sort = req.query.sort && req.query.sort === 'desc' ? -1 : 1;

    let removeKeys = ['sort'];
    filter = await commonUtils.filterParamsModify(filter, removeKeys);

    filter.account = req.userData.account;
    filter.deletedAt = null;

    const questionData = await questionService.getAllQuestions(filter, sort);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_ALL_QUESTIONS, questionData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Update Question
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateQuestion = async (req, res) => {
  try {
    let reqData = req.body;
    let id = req.params.id;

    let exist = await questionService.getQuestionById(id);

    if (!exist) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.QUESTION_NOT_EXIST));
    }

    reqData.updatedBy = req.userData._id;

    const questionData = await questionService.updateQuestion(id, reqData);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_QUESTION, questionData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Delete questoin
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteQuestion = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await questionService.getQuestionById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.QUESTION_NOT_EXIST));
    }

    let response = !exist.isPublished
      ? await questionService.hardDeleteQuestion(id)
      : await questionService.softDeleteQuestion(id, req.deletedAt);

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_QUESTION, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
