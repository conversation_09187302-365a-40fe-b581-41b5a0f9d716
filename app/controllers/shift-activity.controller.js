// Responses
const { successResponse, errorResponse } = require('../utils/response.utils');
// Services
const shiftActvityServices = require('../services/shift-activity.service');
const shiftServices = require('../services/shift.service');
const memberServices = require('../services/member.service');
const teamMemberServices = require('../services/team-member.service');
const dprService = require('../services/dpr.service');
const shiftService = require('../services/shift.service');
// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonFunction = require('../utils/common-function.utils');
const { toObjectId } = require('../utils/common.utils');
const HTTP_STATUS = require('../utils/status-codes');
/**
 * Create Shift Activity
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createShiftActivity = async (req, res) => {
  try {
    let reqData = req.body;
    const shiftData = await shiftServices.getShiftById(reqData.shift);
    if (Object.keys(reqData).indexOf('shift') === -1 && reqData.shift === '') {
      return res.status(422).json(responseUtils.errorResponse(constantUtils.SHIFT_ID_NOT_EXIST));
    }
    let validateEndDate = await this.validateActivityEndDate(reqData.shift, reqData.endTime);
    if (!validateEndDate.status) {
      return res
        .status(validateEndDate.code)
        .json(responseUtils.errorResponse(validateEndDate.message));
    }
    await commonFunction.shiftActivityOtherProject(shiftData, reqData, req);
    reqData.createdBy = req.userData._id;
    reqData.createdAt = new Date().toISOString();
    const responseData = await shiftActvityServices.createShiftActivity(reqData);
    const shiftActivities = await shiftActvityServices.getShiftActivityDataByFilter({
      shift: shiftData._id,
      deletedAt: null,
    });
    const activityUpdateResult = await commonFunction.calculateActivityDuration(
      shiftActivities,
      shiftData
    );
    if (!activityUpdateResult) {
      return res.status(200).json(successResponse(constantUtils.CALCULATE_SHIFT_DURATION_FAIL));
    }
    if (responseData) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res.status(201).json(successResponse(constantUtils.CREATE_SHIFT_ACTIVITY, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};
/**
 * Update Data
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateShiftActivity = async (req, res) => {
  try {
    let reqData = req.body;
    let id = req.params.id;
    let getData = await shiftActvityServices.getSingleRecord({ _id: req.params.id });
    const shiftData = await shiftServices.getShiftById(getData.shift);
    if (!getData) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SHIFT_ACTVITY_NOT_EXIST));
    }
    let validateEndDate = await this.validateActivityEndDate(getData.shift, reqData.endTime);
    if (!validateEndDate.status) {
      return res
        .status(validateEndDate.code)
        .json(responseUtils.errorResponse(validateEndDate.message));
    }
    await commonFunction.shiftActivityOtherProject(shiftData, reqData, req);
    reqData.updatedBy = req.userData._id;
    reqData.updatedAt = new Date().toISOString();
    const updatedData = await shiftActvityServices.updateShiftActivity(id, reqData);
    let shiftActivities = await shiftActvityServices.getShiftActivityDataByFilter({
      shift: shiftData._id,
      deletedAt: null,
    });
    const activityUpdateResult = await commonFunction.calculateActivityDuration(
      shiftActivities,
      shiftData
    );
    if (!activityUpdateResult) {
      return res.status(200).json(successResponse(constantUtils.CALCULATE_SHIFT_DURATION_FAIL));
    }
    if (updatedData) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res.status(200).json(successResponse(constantUtils.UPDATE_SHIFT_ACTIVITY, updatedData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};
/**
 * Shift Activity - Soft Delete
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteShiftActivity = async (req, res) => {
  try {
    let id = req.params.id;
    let getData = await shiftActvityServices.getSingleRecord({ _id: id });
    if (!getData) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SHIFT_ACTVITY_NOT_EXIST));
    }
    let deleteParams = {
      isDeleted: true,
      deletedAt: new Date().toISOString(),
      deletedBy: req.userData._id,
    };
    const shiftData = await shiftServices.getShiftById(getData.shift);
    await shiftActvityServices.updateShiftActivity(id, deleteParams);
    let shiftActivities = await shiftActvityServices.getShiftActivityDataByFilter({
      shift: shiftData._id,
      deletedAt: null,
    });
    const activityUpdateResult = await commonFunction.calculateActivityDuration(
      shiftActivities,
      shiftData
    );
    if (!activityUpdateResult) {
      return res.status(200).json(successResponse(constantUtils.CALCULATE_SHIFT_DURATION_FAIL));
    }
    if (getData) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res.status(200).json(successResponse(constantUtils.DELETE_SHIFT_ACTIVITY));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};
/**
 * Validate the end date for shift activity
 */
exports.validateActivityEndDate = async (id, endDateTime) => {
  const getShift = await shiftServices.getShiftById(id);
  let responseData = { status: true, code: 200, message: constantUtils.VALID_END_TIME };
  if (!getShift) {
    responseData = { ...responseData, status: false, code: 400, message: constantUtils.NO_SHIFT };
  }
  if (!(await shiftActvityServices.isEndTimeValid(getShift.startDate, endDateTime))) {
    responseData = {
      ...responseData,
      status: false,
      code: 422,
      message: constantUtils.INVALID_END_TIME,
    };
  }
  return responseData;
};
/**
 * Get Activity Summary
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getActivitySummary = async (req, res) => {
  try {
    const { dprId } = req.params;
    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: req.userData.account,
      deletedAt: null,
    });
    if (!dpr) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constantUtils.NO_DPR));
    }
    const dateOnly = new Date(dpr.dprDate).toISOString().split('T')[0];
    let response = await shiftActvityServices.getActivitySummary(dpr.project, dateOnly);
    let result = await shiftActvityServices.activityCalculation(response, dpr.dprDate);
    return res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.SHIFT_ACTVITY_SUMMARY, result));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};
/**
 * Get Personnel List Summary
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getPersonnelListSummary = async (req, res) => {
  try {
    const { dprId } = req.params;
    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: req.userData.account,
      deletedAt: null,
    });
    if (!dpr) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constantUtils.NO_DPR));
    }
    let response = await memberServices.getAllMember({
      account: req.userData.account,
      project: dpr.project,
      deletedAt: null,
    });
    const members = response.map(item => toObjectId(item._id));
    const teamMembers = await teamMemberServices.personnelData(members);
    // Filter members to only include those with shifts on the DPR date
    const shiftWiseMembers = [];
    teamMembers.forEach(item => {
      if (item.shift?.startDate.includes('T')) {
        if (item.shift?.startDate.split('T')[0] == dpr.dprDate.toISOString().split('T')[0]) {
          shiftWiseMembers.push(item);
        }
      } else {
        if (item.shift?.startDate.split(' ')[0] == dpr.dprDate.toISOString().split('T')[0]) {
          shiftWiseMembers.push(item);
        }
      }
    });
    response = await this.generatePersonnelList(shiftWiseMembers);

    const userMap = {};

    for (const { userId, team, ...rest } of response) {
      if (!userMap[userId]) {
        userMap[userId] = {
          userId,
          ...rest,
          teams: [team],
        };
      } else if (!userMap[userId].teams.includes(team)) {
        userMap[userId].teams.push(team);
      }
    }

    response = Object.values(userMap).map(({ teams, ...user }) => ({
      ...user,
      team: teams.join(', '),
    }));

    return res.status(HTTP_STATUS.OK).json(successResponse(constantUtils.PERSONNEL_LIST, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};
/**
 * Generate Personnel List
 *
 * @param {*} teamMembers
 * @returns
 */
exports.generatePersonnelList = async teamMembers => {
  try {
    return teamMembers.map(item => {
      return {
        teamMemberId: item._id,
        memberId: item.member._id,
        userId: item.member?.user?._id,
        name: `${item.member?.user?.callingName || item.member?.user?.firstName} ${
          item.member?.user?.lastName || ''
        }`.trim(),
        jobTitle: item.function?.functionName || '',
        functionId: item.function,
        team: item.shift?.team?.teamsWfmName || '',
        travelDay: !item.isWorking,
        workingDay: item.isWorking,
        status: item.status,
        isWorking: item.isWorking,
      };
    });
  } catch (error) {
    throw new Error(`${HTTP_STATUS.INTERNAL_SERVER_ERROR} -> ${error.message}`);
  }
};
/**
 * Daily Activity Logs
 *
 * @param {*} req
 * @param {*} res
 */
exports.dailyActivityLogs = async (req, res) => {
  try {
    const { dprId } = req.params;
    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: req.userData.account,
      deletedAt: null,
    });
    if (!dpr) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constantUtils.NO_DPR));
    }
    const dateOnly = new Date(dpr.dprDate).toISOString().split('T')[0];
    const filter1 = {
      account: req.userData.account,
      project: toObjectId(dpr.project),
      startDate: {
        $gte: `${dateOnly}T00:00:00`,
        $lt: `${dateOnly}T23:59:59`,
      },
      deletedAt: null,
    };
    const filter2 = {
      account: req.userData.account,
      project: toObjectId(dpr.project),
      startDate: {
        $gte: `${dateOnly} 00:00`,
        $lt: `${dateOnly} 23:59`,
      },
      deletedAt: null,
    };
    let shifts1 = await shiftService.getShiftByFilter(filter1);
    let shifts2 = await shiftService.getShiftByFilter(filter2);
    const finalShiftData = shifts1.concat(shifts2);
    let shiftIds = finalShiftData.map(item => item._id);
    let response = await shiftActvityServices.dailyActivityCaculation({
      shift: { $in: shiftIds },
      deletedAt: null,
    });
    return res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.DAILY_ACTIVITY_LOG, response));
  } catch (error) {
    throw new Error(`${HTTP_STATUS.INTERNAL_SERVER_ERROR} -> ${error.message}`);
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['shiftActivities'],
    account,
  });
};
