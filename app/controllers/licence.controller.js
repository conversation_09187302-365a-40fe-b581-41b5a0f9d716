require('dotenv').config();

// services
const licenceService = require('../services/licence.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

/**
 * Create Licence
 *
 * @param {*} req
 * @param {*} res
 */
exports.createLicence = async (req, res) => {
  try {
    const { name } = req.body;
    const exist = await licenceService.getLicenceByName(name);

    if (exist.length > 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.LICENCE_EXIST));
    }
    const licenceData = await licenceService.createLicence(req.body);

    res.status(200).json(responseUtils.successResponse(constantUtils.CREATE_LICENCE, licenceData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Create Licence
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllLicence = async (req, res) => {
  try {
    const licenceData = await licenceService.getAllLicence();

    if (licenceData.length === 0) {
      return res.status(200).json(responseUtils.successResponse(constantUtils.NO_LICENCE, null));
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.ALL_LICENCE_LIST, licenceData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};
