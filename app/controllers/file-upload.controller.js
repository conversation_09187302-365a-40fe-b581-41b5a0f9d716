require('dotenv').config();

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

//Services
const fileUploadService = require('../services/file-upload.service');

/**
 * File upload
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.fileUpload = async (req, res) => {
  try {
    if (typeof req.body.type === 'undefined' || req.body.type === '') {
      return res.status(422).json(responseUtils.errorResponse(constantUtils.TYPE_FIELD_REQUIRED));
    }

    const fileUploadResponse = await fileUploadService.fileUpload(req);
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.FILE_UPLOAD_SUCCESS, fileUploadResponse));
  } catch (error) {
    const status = error.status ?? 500;
    return res.status(status).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Download sample file
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.downloadSampleFile = async (req, res) => {
  try {
    const { fileName } = req.params;
    return await fileUploadService.downloadFile(fileName, res);
  } catch (error) {
    return res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.multipleFileUpload = async (req, res) => {
  try {
    if (typeof req.body.type === 'undefined' || req.body.type === '') {
      return res.status(422).json(responseUtils.errorResponse(constantUtils.TYPE_FIELD_REQUIRED));
    }

    const fileUploadResponse = await fileUploadService.multipleFileUpload(req);
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.FILE_UPLOAD_SUCCESS, fileUploadResponse));
  } catch (error) {
    const status = error.status ?? 500;
    return res.status(status).json(responseUtils.errorResponse(error.message));
  }
};
