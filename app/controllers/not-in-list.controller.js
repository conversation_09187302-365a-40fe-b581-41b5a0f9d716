// Services
const notInListService = require('../services/not-in-list.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Delete Not In List
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteNotInListMembers = async (req, res) => {
  try {
    if (!commonUtils.isValidId(req.params.id)) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    const exist = await notInListService.getNotInListMember(req.params.id);

    if (!exist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NOT_EXIST_NOT_IN_LIST));
    }

    await notInListService.deleteNotInListMembers(req.params.id, req.deletedAt);

    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.DELETED_NOT_IN_LIST));
  } catch (error) {
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error));
  }
};
