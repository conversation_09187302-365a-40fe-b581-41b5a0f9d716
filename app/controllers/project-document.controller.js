// Services
const projectDocumentService = require('../services/project-document.service');
const projectServices = require('../services/project.service');
const dprService = require('../services/dpr.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const { successResponse, errorResponse } = require('../utils/response.utils');
const HTTP_STATUS = require('../utils/status-codes');
const { toObjectId } = require('../utils/common.utils');
const commonFunction = require('../utils/common-function.utils');

/**
 * Create Project Document
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createProjectDocument = async (req, res) => {
  try {
    let reqData = req.body;
    reqData.account = req.userData.account;
    reqData.createdBy = req.userData._id;
    reqData = await projectDocumentService.formatUploadedDocument(reqData);

    // Initialize reviews array for safety notification documents
    if (reqData.type === 'safety_notification') {
      reqData.docReviews = await projectDocumentService.createReviewsForProjectMembers(
        reqData.project,
        reqData.account
      );
    }

    const createResponse = await projectDocumentService.createProjectDocument(reqData);
    if (createResponse) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res
      .status(200)
      .json(successResponse(constantUtils.CREATE_PROJECT_DOCUMENT, createResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Project Documents
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getProjectDocuments = async (req, res) => {
  try {
    let { project, status } = req.query;
    let page = req.query.page ?? null;
    let perPage = req.query.perPage ?? null;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let active = null;
    if (req.query?.active === 'true') {
      active = true;
    } else if (req.query?.active === 'false') {
      active = false;
    }
    let filter = {
      ...(project && { project: project }),
      account: req.userData.account,
      deletedAt: null,
    };

    if (status) {
      const projectList = await projectServices.getAllProjects({
        status,
        account: req.userData.account,
        deletedAt: null,
      });

      if (projectList && projectList.length > 0) {
        filter = {
          ...filter,
          project: {
            $in: projectList.map(project => project._id),
          },
        };
      }
    }

    const responseData = await projectDocumentService.getProjectDocuments(
      filter,
      page,
      perPage,
      sort,
      active
    );
    return res
      .status(200)
      .json(successResponse(constantUtils.GET_PROJECT_DOCUMENT_LIST, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Non-Acknowledged Documents
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getNonAcknowledgedDocuments = async (req, res) => {
  try {
    const documents = await projectDocumentService.getNonAcknowledgedDocuments({
      account: req.userData.account,
      userId: req.userData._id,
    });

    return res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.GET_NON_ACKNOWLEDGED_DOCUMENTS, documents));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Acknowledge Document
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.acknowledgeDocument = async (req, res) => {
  try {
    const { id } = req.params;
    const checkExist = await this.checkProjectDocumentExist(id, req.userData.account);
    if (!checkExist.status) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(checkExist.message));
    }

    const document = await projectDocumentService.acknowledgeDocument(id, req.userData._id);

    if (!document) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(errorResponse(constantUtils.FAILED_TO_ACKNOWLEDGE_DOCUMENT));
    }

    return res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.ACKNOWLEDGE_DOCUMENT, document));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Update Project Document
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateProjectDocument = async (req, res) => {
  try {
    let { id } = req.params;
    const checkExist = await this.checkProjectDocumentExist(id, req.userData.account);
    if (!checkExist.status) {
      return res.status(400).json(errorResponse(checkExist.message));
    }
    let reqData = req.body;
    let documentData = reqData.document || null;
    delete reqData.document;
    reqData.updatedBy = req.userData._id;
    let responseData = await projectDocumentService.updateProjectDocument(id, reqData);
    if (responseData && documentData) {
      responseData = await this.renewProjectDocument(id, { document: documentData }, responseData);

      // Reset docReviews after adding new document
      await projectDocumentService.updateProjectDocument(id, {
        docReviews: await projectDocumentService.updateReviewsForProjectMembers(
          checkExist.exist.docReviews
        ),
      });

      // Get updated response data
      responseData = await projectDocumentService.getSingleProjectDocumentByFilter({
        _id: id,
        account: req.userData.account,
        deletedAt: null,
      });
    }

    if (responseData) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res
      .status(200)
      .json(successResponse(constantUtils.UPDATE_PROJECT_DOCUMENT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Renew Project Document
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.renewProjectDocument = async (id, requestData, existData) => {
  try {
    for (let document of existData.document) {
      await projectDocumentService.updateDocumentInProjectDocument(id, document._id, {
        'document.$.isActive': false,
      });
    }
    requestData = await this.prepareRenewDocument(requestData, existData);
    return await projectDocumentService.addProjectDocument(id, requestData);
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Prepare Renew Document
 *
 * @param {*} reqData
 * @param {*} existData
 * @returns
 */
exports.prepareRenewDocument = async (reqData, existData) => {
  try {
    let countDocuments = existData.document.length;
    reqData.document.version = `v${countDocuments + 1}`;
    let requiredFields = ['title', 'type', 'documentNumber'];
    for (let field of requiredFields) {
      reqData[field] = existData[field];
    }
    reqData = await projectDocumentService.formatUploadedDocument(reqData);
    for (let field of requiredFields) {
      delete reqData[field];
    }
    return reqData;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Delete Project Document
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteProjectDocument = async (req, res) => {
  try {
    let { id } = req.params;
    const checkExist = await this.checkProjectDocumentExist(id, req.userData.account);
    if (!checkExist.status) {
      return res.status(400).json(errorResponse(checkExist.message));
    }
    const responseData = await projectDocumentService.deleteProjectDocument(id, {
      deletedBy: req.userData._id,
      deletedAt: new Date(),
    });
    if (responseData) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res
      .status(200)
      .json(successResponse(constantUtils.DELETE_PROJECT_DOCUMENT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Check Project Document Exist
 *
 * @param {*} id
 * @param {*} accountId
 * @returns
 */
exports.checkProjectDocumentExist = async (id, accountId) => {
  const exist = await projectDocumentService.getSingleProjectDocumentByFilter({
    _id: id,
    account: accountId,
    deletedAt: null,
  });
  if (!exist) {
    return {
      status: false,
      message: constantUtils.NO_PROJECT_DOCUMENT,
    };
  }
  return {
    status: true,
    exist,
  };
};

/**
 * Get Project Documents Summary
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getProjectDocumentsSummary = async (req, res) => {
  try {
    const { dprId } = req.params;

    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: req.userData.account,
      deletedAt: null,
    });

    if (!dpr) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constantUtils.NO_DPR));
    }

    const dateOnly = new Date(dpr.dprDate).toISOString().split('T')[0];

    const filter = {
      account: req.userData.account,
      project: toObjectId(dpr.project),
      createdAt: {
        $lte: new Date(`${dateOnly}T23:59:59.999Z`),
      },
      document: {
        $elemMatch: {
          isActive: true,
        },
      },
      deletedAt: null,
    };

    const result = await projectDocumentService.getProjectDocumentsSummary(filter);

    return res
      .status(HTTP_STATUS.OK)
      .json(successResponse(constantUtils.GET_PROJECT_DOCUMENT_SUMMARY, result));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Get Project Documents V2
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getProjectDocumentsV2 = async (req, res) => {
  try {
    let { project, status } = req.query;
    let page = req.query.page ?? null;
    let perPage = req.query.perPage ?? null;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let active = null;
    if (req.query?.active === 'true') {
      active = true;
    } else if (req.query?.active === 'false') {
      active = false;
    }
    let filter = {
      ...(project && { project: project }),
      account: req.userData.account,
      deletedAt: null,
    };

    if (status) {
      const projectList = await projectServices.getAllProjects({
        status,
        account: req.userData.account,
        deletedAt: null,
      });

      if (projectList && projectList.length > 0) {
        filter = {
          ...filter,
          project: {
            $in: projectList.map(project => project._id),
          },
        };
      }
    }

    let responseData = await projectDocumentService.getProjectDocuments(
      filter,
      page,
      perPage,
      sort,
      active
    );

    const grouped = {};

    responseData.forEach(doc => {
      const projectId = doc.project._id;
      const type = doc.type;
      const key = `${projectId}_${type}`;

      if (!grouped[key]) {
        grouped[key] = {
          projectId: projectId,
          projectTitle: doc.project.title,
          type: type,
          documents: [],
        };
      }

      doc.document.forEach(docItem => {
        grouped[key].documents.push({
          documentTitle: doc.title,
          documentNumber: doc.documentNumber,
          name: docItem.name,
          url: docItem.url,
          type: docItem.type,
          version: docItem.version,
          isActive: docItem.isActive,
          date: docItem.date,
          _id: docItem._id,
        });
      });
    });

    responseData = Object.values(grouped).sort((documentOne, documentTwo) => {
      return (
        global.constant.PROJECT_DOCUMENT_TYPES.indexOf(documentOne.type) -
        global.constant.PROJECT_DOCUMENT_TYPES.indexOf(documentTwo.type)
      );
    });

    return res
      .status(200)
      .json(successResponse(constantUtils.GET_PROJECT_DOCUMENT_LIST, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['projectDocuments'],
    account,
  });
};
