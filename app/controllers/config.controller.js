const constants = require('../utils/constants.utils');

// Services
const userService = require('../services/auth.service');
const accountService = require('../services/account.service');
const roleService = require('../services/role.service');
const syncService = require('../services/sync.service');

// Json fromat
const { config, screen, buttons } = require('../utils/json-format.utils');

// Response
const { successResponse, errorResponse } = require('../utils/response.utils');

// Static Fields
const staticFields = require('../utils/static-fields.utils');

// Dynamic Fields
const dynamicFields = require('../utils/dynamic-fields.utils');

// Environment varibles
const imageUrl = `${process.env.IMAGE_URL}/icons`;

exports.createCongifFile = async (req, res) => {
  try {
    staticFields.constructFunction(req);
    let tokenData = await this.getTokenData(req, res);
    const accountData = await accountService.findAccountById(tokenData.account);
    if (tokenData) {
      const user = await userService.getUserById(tokenData.id);
      if (user) {
        // 1st Screen for Safe card
        // Safety card Static fields
        let safetyCardStaticFields = await staticFields.GetSafeCardStaticFile(tokenData);

        // Safety card dynamic fields
        let safetyCardDynamicFields = await dynamicFields.GetAllSafetyCardDynamicFeilds(
          tokenData,
          'safe'
        );

        let safeScreen = { ...screen };
        safeScreen.title = 'Safe';
        safeScreen.color = '029E3B';
        safeScreen.screenId = 'safe_01';
        safeScreen.iconUrl = imageUrl + '/ic_safe.png';
        safeScreen.properties = [...safetyCardStaticFields, ...safetyCardDynamicFields];
        let safeBtn = { ...buttons };
        safeBtn.color = '029E3B';
        safeScreen.buttons = [{ ...buttons, color: '029E3B' }];
        safeScreen.agreement = await dynamicFields.GetRoleAgreement(safeScreen.title, tokenData);

        // 2st Screen for Unafe card
        // Unsafe card Static fields
        let unsafeCardStaticFields = await staticFields.GetUnSafeCardStaticFile(tokenData);

        // Unsafe card dynamic fields
        let unsafeCardDynamicFields = await dynamicFields.GetAllSafetyCardDynamicFeilds(
          tokenData,
          'unsafe'
        );

        let unsafeScreen = { ...screen };
        unsafeScreen.title = 'Unsafe';
        unsafeScreen.color = 'D00508';
        unsafeScreen.iconUrl = imageUrl + '/ic_unsafe.png';
        unsafeScreen.screenId = 'unsafe_02';

        unsafeScreen.properties = [...unsafeCardStaticFields, ...unsafeCardDynamicFields];
        unsafeScreen.buttons = [{ ...buttons, color: 'D00508' }];
        unsafeScreen.agreement = await dynamicFields.GetRoleAgreement(
          unsafeScreen.title,
          tokenData
        );

        // 3st Screen for (NCR)Defect Report
        let ncrCardStaticFields = await staticFields.GetNCRCardStaticFile(tokenData);

        // NCR card dynamic fields
        let ncrCardDynamicFields = await dynamicFields.GetAllSafetyCardDynamicFeilds(
          tokenData,
          'ncr'
        );

        let ncrScreen = { ...screen };
        ncrScreen.title = 'NCR';
        ncrScreen.color = '191A51';
        ncrScreen.screenId = 'ncr_03';
        ncrScreen.iconUrl = imageUrl + '/ic_ncr.png';
        ncrScreen.properties = [...ncrCardStaticFields, ...ncrCardDynamicFields];
        ncrScreen.buttons = [{ ...buttons, color: '191A51' }];
        ncrScreen.agreement = await dynamicFields.GetRoleAgreement(ncrScreen.title, tokenData);

        // 4st Screen for Incident
        let incidentCardStaticFields = await staticFields.GetIncidentCardStaticFile(tokenData);

        // NCR card dynamic fields
        let incidentCardDynamicFields = await dynamicFields.GetAllSafetyCardDynamicFeilds(
          tokenData,
          'incident'
        );

        let incidentScreen = { ...screen };
        incidentScreen.title = 'Incident';
        incidentScreen.color = '191A51';
        incidentScreen.iconUrl = imageUrl + '/ic_incident.png';
        incidentScreen.screenId = 'incident_04';
        incidentScreen.properties = [...incidentCardStaticFields, ...incidentCardDynamicFields];
        incidentScreen.buttons = [{ ...buttons, color: '191A51' }];
        incidentScreen.agreement = await dynamicFields.GetRoleAgreement(
          incidentScreen.title,
          tokenData
        );

        // 5st Screen for Submit Feedback
        let submitFeedbackStaticFields = await staticFields.getFeedbackStaticFile();

        let submitFeedbackScreen = { ...screen };
        submitFeedbackScreen.title = 'Submit Feedback';
        submitFeedbackScreen.color = '191A51';
        submitFeedbackScreen.iconUrl = imageUrl + '/ic_feedback.png';
        submitFeedbackScreen.screenId = 'submit_feedback_05';
        submitFeedbackScreen.properties = submitFeedbackStaticFields;
        submitFeedbackScreen.buttons = [{ ...buttons, color: '191A51' }];
        submitFeedbackScreen.agreement = await dynamicFields.GetRoleAgreement('default', tokenData);

        // 6st Screen for Open Shift
        let shiftStaticFields = await staticFields.getShiftStaticFile(tokenData);

        let openShiftScreen = { ...screen };
        openShiftScreen.title = 'Shifts';
        openShiftScreen.iconUrl = imageUrl + '/ic_openshift.png';
        openShiftScreen.color = '191A51';
        openShiftScreen.screenId = 'open_shift_06';
        openShiftScreen.buttons = [{ ...buttons, color: '191A51', title: 'Start' }];
        openShiftScreen.properties = shiftStaticFields;
        openShiftScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Shift Details',
          tokenData
        );

        // 7st Screen for Report
        let reportScreen = { ...screen };
        reportScreen.title = 'Report';
        reportScreen.iconUrl = imageUrl + '/ic_report.png';
        reportScreen.color = '191A51';
        reportScreen.screenId = 'report_07';
        reportScreen.agreement = await dynamicFields.GetRoleAgreement(
          reportScreen.title,
          tokenData
        );

        // 8th Screen for Shift Activity
        let shiftActivityStaticFields = await staticFields.getShiftActivityStaticFile(tokenData);

        let shiftActivityScreen = { ...screen };
        shiftActivityScreen.title = 'Shift Activity';
        shiftActivityScreen.type = 'inner-form';
        shiftActivityScreen.iconUrl = imageUrl + '/ic_shift-activity.png';
        shiftActivityScreen.color = '191A51';
        shiftActivityScreen.screenId = 'shift_activity_08';
        shiftActivityScreen.buttons = [{ ...buttons, color: '191A51', title: 'Add Activity' }];
        shiftActivityScreen.properties = shiftActivityStaticFields;
        shiftActivityScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Shift Details',
          tokenData
        );

        // 9th Screen for Setting
        let settingScreen = { ...screen };
        settingScreen.title = 'Setting';
        settingScreen.iconUrl = imageUrl + '/ic_setting.png';
        settingScreen.screenId = 'setting_09';
        settingScreen.agreement = await dynamicFields.GetRoleAgreement('default', tokenData);

        // 10th Screen for Equipment/Inventory
        let equipmentStaticFields = await staticFields.GetEquipmentStaticFile(tokenData);

        let equipmentScreen = { ...screen };
        equipmentScreen.title = 'Inventory';
        equipmentScreen.iconUrl = imageUrl + '/ic_setting.png';
        equipmentScreen.color = '191A51';
        equipmentScreen.screenId = 'inventory_10';
        equipmentScreen.buttons = [{ ...buttons, color: '191A51' }];
        equipmentScreen.properties = equipmentStaticFields;
        equipmentScreen.agreement = await dynamicFields.GetRoleAgreement('Inventory', tokenData);

        // 11th Screen for Warehouse

        let warehouseScreen = { ...screen };
        warehouseScreen.title = 'Warehouse';
        warehouseScreen.iconUrl = imageUrl + '/ic_setting.png';
        warehouseScreen.color = '191A51';
        warehouseScreen.screenId = 'warehouse_11';
        warehouseScreen.buttons = [{ ...buttons, color: '191A51' }];
        warehouseScreen.properties = [];
        warehouseScreen.agreement = await dynamicFields.GetRoleAgreement('Warehouse', tokenData);

        // 12th Screen for Warehouse

        let equipmentRequestScreen = { ...screen };
        equipmentRequestScreen.title = 'Request';
        equipmentRequestScreen.iconUrl = imageUrl + '/ic_setting.png';
        equipmentRequestScreen.color = '191A51';
        equipmentRequestScreen.screenId = 'order_request_12';
        equipmentRequestScreen.buttons = [{ ...buttons, color: '191A51' }];
        equipmentRequestScreen.properties = [];
        equipmentRequestScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Request',
          tokenData
        );

        // 13th Screen for Project Inventory
        let equipmentApprovalScreen = { ...screen };
        equipmentApprovalScreen.title = 'Project Inventory';
        equipmentApprovalScreen.iconUrl = imageUrl + '/ic_setting.png';
        equipmentApprovalScreen.color = '191A51';
        equipmentApprovalScreen.screenId = 'order_approval_13';
        equipmentApprovalScreen.buttons = [{ ...buttons, color: '191A51' }];
        equipmentApprovalScreen.properties = [];
        equipmentApprovalScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Order Approval',
          tokenData
        );

        // 14th Screen for Toolbox Talk
        let toolBoxTalkScreen = { ...screen };
        toolBoxTalkScreen.title = 'Toolbox Talk';
        toolBoxTalkScreen.iconUrl = imageUrl + '/ic_toolBoxTalk.png';
        toolBoxTalkScreen.color = '191A51';
        toolBoxTalkScreen.screenId = 'toolBoxTalk_14';
        toolBoxTalkScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Toolbox Talk',
          tokenData
        );

        // 15th Screen for Setting
        let projectDocumentScreen = { ...screen };
        projectDocumentScreen.title = 'Documents';
        projectDocumentScreen.iconUrl = imageUrl + '/project.png';
        toolBoxTalkScreen.color = '191A51';
        projectDocumentScreen.screenId = 'projectDocument_15';
        projectDocumentScreen.agreement = await dynamicFields.GetRoleAgreement(
          'default',
          tokenData
        );

        // 16th Screen for User Management
        let userManagementScreen = { ...screen };
        userManagementScreen.title = 'User Management';
        userManagementScreen.iconUrl = imageUrl + '/ic_userManagement.png';
        userManagementScreen.color = '191A51';
        userManagementScreen.screenId = 'userManagement_16';
        userManagementScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Personnel',
          tokenData
        );

        // 17th Screen for DPR
        let dprScreen = { ...screen };
        dprScreen.title = 'DPR';
        dprScreen.iconUrl = imageUrl + '/ic_dpr.png';
        dprScreen.color = '191A51';
        dprScreen.screenId = 'DPR_17';
        dprScreen.agreement = await dynamicFields.GetRoleAgreement('DPR', tokenData);

        // 18th Screen for Training Matrics
        let trainingMatricsScreen = { ...screen };
        trainingMatricsScreen.title = 'Training Matrix';
        trainingMatricsScreen.iconUrl = imageUrl + '/ic_trainingMatrix.png';
        trainingMatricsScreen.color = '191A51';
        trainingMatricsScreen.screenId = 'trainingMatrix_18';
        trainingMatricsScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Training Matrix',
          tokenData
        );

        // 19th Screen for Personnel(Basic)
        let personnelBasicScreen = { ...screen };
        personnelBasicScreen.title = 'Personnel(Basic)';
        personnelBasicScreen.iconUrl = imageUrl + '/ic_personalBasic.png';
        personnelBasicScreen.color = '191A51';
        personnelBasicScreen.screenId = 'Personal(Basic)_19';
        personnelBasicScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Personnel Basic',
          tokenData
        );

        // 20th Screen for Project Setup
        let projectSetupScreen = { ...screen };
        projectSetupScreen.title = 'Project Setup';
        projectSetupScreen.iconUrl = imageUrl + '/ic_projectSetup.png';
        projectSetupScreen.color = '191A51';
        projectSetupScreen.screenId = 'projectSetup_20';
        projectSetupScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Project Setup',
          tokenData
        );

        // 21st Screen for Report Setup
        let reportSetupScreen = { ...screen };
        reportSetupScreen.title = 'Report Setup';
        reportSetupScreen.iconUrl = imageUrl + '/ic_reportSetup.png';
        reportSetupScreen.color = '191A51';
        reportSetupScreen.screenId = 'reportSetup_21';
        reportSetupScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Report Setup',
          tokenData
        );

        // 22nd Screen for Certificate Approval
        let certificateApprovalScreen = { ...screen };
        certificateApprovalScreen.title = 'Certificate Approval';
        certificateApprovalScreen.iconUrl = imageUrl + '/ic_certificateApproval.png';
        certificateApprovalScreen.color = '191A51';
        certificateApprovalScreen.screenId = 'certificate_approval_22';
        certificateApprovalScreen.agreement = await dynamicFields.GetRoleAgreement(
          'Certificate Approval',
          tokenData
        );

        let ConfigFile = { ...config };
        ConfigFile.username =
          (user.callingName ? user.callingName : user.firstName) + ' ' + user.lastName;
        const adminUser = await userService.getUserByEmail(accountData.email);
        ConfigFile.admin =
          (adminUser?.callingName ? adminUser?.callingName : adminUser?.firstName) +
          ' ' +
          adminUser?.lastName;
        ConfigFile.companyName = accountData?.name;
        ConfigFile.profileImage = user?.profileImage;
        ConfigFile.syncUpTime = accountData
          ? accountData?.syncUpTime
          : parseInt(process.env.DEFAULT_SYNCUP_TIME);
        ConfigFile.id = user._id;
        ConfigFile.hasNonAcknowledgedDocuments = await syncService.checkPendingSafetyNotifications(
          tokenData.id,
          tokenData.account
        ); // Add pending safety notifications status
        ConfigFile.logoUrl = imageUrl + '/reynard_logo.png';
        ConfigFile.account = user.account;
        ConfigFile.tokenRole = tokenData.roleId.title ?? '';
        ConfigFile.role = user.role.title ?? '';
        ConfigFile.accessType = user.role.accessType ?? '';
        ConfigFile.isActive = user.isActive ?? false;
        ConfigFile.isRoleActive = user.role.isActive ?? false;

        // Get the screen licences of account
        let getAccountScreens = await dynamicFields.accountLicences(user.account);

        // Prepare all screens
        let prepareAllScreens = {
          safeScreen,
          unsafeScreen,
          ncrScreen,
          incidentScreen,
          openShiftScreen,
          reportScreen,
          shiftActivityScreen,
          equipmentScreen,
          warehouseScreen,
          equipmentRequestScreen,
          equipmentApprovalScreen,
          toolBoxTalkScreen,
          submitFeedbackScreen,
          settingScreen,
          projectDocumentScreen,
          userManagementScreen,
          dprScreen,
          trainingMatricsScreen,
          personnelBasicScreen,
          projectSetupScreen,
          reportSetupScreen,
          certificateApprovalScreen,
        };

        // Check the licence access in all screens and prepare the screen array
        let accessScreens = [];
        getAccountScreens.forEach(screen => {
          accessScreens = [...accessScreens, prepareAllScreens[screen]];
        });

        // Add valid screens in config file
        ConfigFile.screens = accessScreens;
        const configFileContent = JSON.stringify(ConfigFile);
        res.setHeader('Content-Length', Buffer.byteLength(configFileContent, 'utf8'));
        res.status(200).json(successResponse(constants.CONFIG_FILE, ConfigFile));
      } else {
        return res.status(400).json({
          status: false,
          message: constants.USER_CANNOT_ACCESS_USER_NOT_FOUND,
        });
      }
    }
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.changeTokenData = async (token, account, userId) => {
  try {
    let loginUserToken = await userService.decodeJwtToken(token);
    let user = await userService.getUserById(userId);
    let roleData = await roleService.getRoleById(user.role, account);

    if (roleData) {
      loginUserToken.id = userId;
      loginUserToken.role = roleData.title;
      loginUserToken.account = account;
      loginUserToken.roleId._id = roleData._id;
      loginUserToken.roleId.title = roleData.title;
      loginUserToken.roleId.allProject = roleData.allProject;
      loginUserToken.roleId.isActive = roleData.isActive;
      loginUserToken.roleId.accessType = roleData.accessType;
    }
    return loginUserToken;
  } catch (error) {
    return token;
  }
};

/**
 * Get equipment config file
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createEquipmentConfigFile = async (req, res) => {
  let tokenData = await this.getTokenData(req, res);
  let result = {};
  if (tokenData) {
    const user = await userService.getUserById(tokenData.id);
    if (user) {
      let equipmentStaticFields = await staticFields.GetEquipmentStaticFile(tokenData);
      let GetImageSpecificationAndPriceStaticFile =
        await staticFields.GetImageSpecificationAndPriceStaticFile(tokenData);
      let warehouseInfo = await staticFields.GetWarehouseInfoFields(tokenData);

      result.equipment = equipmentStaticFields;
      result.imageSpecificationAndPrice = GetImageSpecificationAndPriceStaticFile;
      result.warehouseInfo = warehouseInfo;

      // equipment
      let equipmentScreen = { ...screen };
      equipmentScreen.title = 'Register Equipment';
      equipmentScreen.color = '029E3B';
      equipmentScreen.screenId = 'equipment_11';
      equipmentScreen.iconUrl = imageUrl + '/ic_equipment.png';
      equipmentScreen.properties = result;
      let equipmentBtn = { ...buttons };
      equipmentBtn.color = '029E3B';
      equipmentScreen.buttons = [{ ...buttons, title: 'Countinue', color: '029E3B' }];
      equipmentScreen.agreement = await dynamicFields.GetRoleAgreement('Inventory', tokenData);

      let prepareAllScreens = {
        equipmentScreen,
      };
      res.status(200).json(successResponse(constants.CONFIG_FILE, prepareAllScreens));
    } else {
      return res.status(400).json({
        status: false,
        message: constants.USER_CANNOT_ACCESS_USER_NOT_FOUND,
      });
    }
  }
};

exports.getReportConfigFile = async (req, res) => {
  try {
    let tokenData = await this.getTokenData(req, res);
    if (tokenData) {
      const user = await userService.getUserById(tokenData.id);
      if (user) {
        // Static fields
        let reportStaticFields = await staticFields.GetSetupReportStaticFile(tokenData);

        let reportScreen = { ...screen };
        reportScreen.title = 'Setup Report';
        reportScreen.color = '191a55';
        reportScreen.screenId = 'setup_report_14';
        reportScreen.iconUrl = imageUrl + '/ic_report.png';
        reportScreen.properties = [...reportStaticFields];
        let reportBtn = { ...buttons };
        reportBtn.color = '191a55';
        reportScreen.buttons = [{ ...buttons, title: 'Start', color: '191a55' }];
        reportScreen.agreement = await dynamicFields.GetRoleAgreement(
          global.constant.REPORT,
          tokenData
        );

        let prepareAllScreens = {
          reportScreen,
        };
        res.status(200).json(successResponse(constants.CONFIG_FILE, prepareAllScreens));
      }
    } else {
      return res.status(400).json({
        status: false,
        message: constants.USER_CANNOT_ACCESS_USER_NOT_FOUND,
      });
    }
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get toolbox talk config file
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getToolboxTalkConfigFile = async (req, res) => {
  try {
    let tokenData = await this.getTokenData(req, res);
    if (tokenData) {
      const user = await userService.getUserById(tokenData.id);
      if (user) {
        // Static fields
        let toolboxTalkStaticFields = await staticFields.GetSetupToolboxTalkStaticFile(tokenData);

        let toolboxTalkScreen = { ...screen };
        toolboxTalkScreen.title = 'Toolbox Talk';
        toolboxTalkScreen.color = '191a55';
        toolboxTalkScreen.screenId = 'toolbox_talk_14';
        toolboxTalkScreen.iconUrl = imageUrl + '/ic_toolboxTalk.png';
        toolboxTalkScreen.properties = [...toolboxTalkStaticFields];
        let toolboxTalkBtn = { ...buttons };
        toolboxTalkBtn.color = '191a55';
        toolboxTalkScreen.buttons = [{ ...buttons, title: 'Save & Countinue', color: '191a55' }];
        toolboxTalkScreen.agreement = await dynamicFields.GetRoleAgreement(
          toolboxTalkScreen.title,
          tokenData
        );

        let prepareAllScreens = {
          toolboxTalkScreen,
        };
        res.status(200).json(successResponse(constants.CONFIG_FILE, prepareAllScreens));
      }
    } else {
      return res.status(400).json({
        status: false,
        message: constants.USER_CANNOT_ACCESS_USER_NOT_FOUND,
      });
    }
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Token Data
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getTokenData = async (req, res) => {
  const token = req.headers.authorization;
  if (!token) {
    return res.status(400).json({
      status: false,
      message: constants.SEND_AUTHENTICATION_TOKEN,
    });
  }
  let tokenData = {};
  if (req.query.account && req.query.admin) {
    tokenData = await this.changeTokenData(token, req.query.account, req.query.admin);
  } else {
    tokenData = await userService.decodeJwtToken(token);
    const user = await userService.getUserById(tokenData.id);
    const roleData = await roleService.getRoleById(user.role, user.account);
    if (roleData) {
      tokenData.roleId.isAssignAllProjects = roleData.isAssignAllProjects;
    }
  }
  return tokenData;
};
