require('dotenv').config();

// Services
const teamMemberService = require('../services/team-member.service');
const shiftServices = require('../services/shift.service');
const notInListService = require('../services/not-in-list.service');
const functionService = require('../services/function.service');
const memberService = require('../services/member.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const modifyDefaultDataUtils = require('../utils/modify-default-data.utils');
const HTTP_STATUS = require('../utils/status-codes');
const { isValidId } = require('../utils/common.utils');
const commonFunctionsUtils = require('../utils/common-function.utils');

/**
 * Create Team Member
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createTeamMember = async (req, res) => {
  try {
    const reqData = req.body;
    // default project
    const shiftData = await shiftServices.getShiftById(reqData.shift);

    // member
    if (!shiftData) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_SHIFT));
    }

    // Check if same member with same function already exists in this shift
    const isTeamMemberExist = await teamMemberService.getTeamMemberByFilterData({
      member: reqData.member,
      function: reqData.function,
      shift: reqData.shift,
      deletedAt: null,
    });

    if (isTeamMemberExist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.TEAM_MEMBER_SAME_FUNCTION_EXIST));
    }

    if (shiftData.project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      reqData.member = await modifyDefaultDataUtils.createDefaultMember(
        req.body.member,
        shiftData.project._id,
        req.userData.account
      );
    }
    // member

    // function
    if (shiftData.project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      reqData.function = await modifyDefaultDataUtils.createDefaultFunction(
        req.body.function,
        shiftData.project._id,
        req.userData.account
      );
    }
    // function
    const newReq = { ...reqData, createdBy: req.userData._id, updatedBy: req.userData._id };

    // end default project
    const createdTeamMember = await teamMemberService.createTeamMember(newReq);

    if (createdTeamMember) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_TEAM_MEMBER, createdTeamMember));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Team Member
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateTeamMember = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await teamMemberService.getTeamMemberById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_TEAM_MEMBER));
    }

    // default project
    const shiftData = await shiftServices.getShiftById(exist.shift);

    // member
    if (shiftData.project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      if ('member' in req.body) {
        req.body.member = await modifyDefaultDataUtils.createDefaultMember(
          req.body.member,
          shiftData.project._id,
          req.userData.account
        );
      }
    }
    // member

    // function
    if (shiftData.project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      if ('function' in req.body) {
        req.body.function = await modifyDefaultDataUtils.createDefaultFunction(
          req.body.function,
          shiftData.project._id,
          req.userData.account
        );
      }
    }
    // function
    const newReq = { ...req.body, updatedBy: req.userData._id };

    // end default project

    const response = await teamMemberService.updateTeamMember(id, newReq);
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_TEAM_MEMBER, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Team Member
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteTeamMember = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await teamMemberService.getTeamMemberById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_TEAM_MEMBER));
    }

    await teamMemberService.deleteTeamMember(id, req.deletedAt);

    await this.commonUpdateSyncApiManage(req.userData.account);

    return res.status(200).json(responseUtils.successResponse(constantUtils.DELETE_TEAM_MEMBER));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Batch Update Team Members
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.batchUpdateTeamMembers = async (req, res) => {
  try {
    const reqData = req.body;

    const response = await teamMemberService.batchUpdateTeamMembers(reqData.personnelListData);

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.BATCH_UPDATE_TEAM_MEMBERS, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Team Member
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateTeamMemberV2 = async (req, res) => {
  try {
    let id = req.params.id;
    /** ---------------------------------------- Not in list ---------------------------------------- */
    if (req.body.notInListMember) {
      const shift = await shiftServices.getShiftById(req.body.notInListMember.shift);

      if (req.body.function && !isValidId(req.body.function)) {
        let createdFunction = await functionService.createFunction({
          project: shift.project._id,
          functionName: req.body.function,
          account: req.userData.account,
        });

        let createdMember = await memberService.createMember({
          project: shift.project._id,
          function: createdFunction._id,
          user: req.body.member,
        });

        req.body.member = createdMember._id;
        req.body.function = createdFunction._id;
      }

      // Check if same member with same function already exists in this shift
      const isTeamMemberExist = await teamMemberService.getTeamMemberByFilterData({
        member: req.body.member,
        function: req.body.function,
        shift: req.body.notInListMember.shift,
        deletedAt: null,
      });

      if (isTeamMemberExist) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(responseUtils.errorResponse(constantUtils.TEAM_MEMBER_SAME_FUNCTION_EXIST));
      }

      await notInListService.deleteNotInListMembers(req.body.notInListMember.notInList, {
        deletedAt: new Date(),
        deletedBy: req.userData.id,
      });

      await teamMemberService.createTeamMember({
        member: req.body.member,
        function: req.body.function,
        shift: req.body.notInListMember.shift,
        createdBy: req.userData._id,
      });

      return res
        .status(HTTP_STATUS.OK)
        .json(responseUtils.successResponse(constantUtils.UPDATE_TEAM_MEMBER));
    }
    /** ---------------------------------------- Not in list ---------------------------------------- */

    const exist = await teamMemberService.getTeamMemberById(id);

    if (!exist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_TEAM_MEMBER));
    }

    // default project
    const shiftData = await shiftServices.getShiftById(exist.shift);

    // member
    if (shiftData.project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      if ('member' in req.body) {
        req.body.member = await modifyDefaultDataUtils.createDefaultMember(
          req.body.member,
          shiftData.project._id,
          req.userData.account
        );
      }
    }
    // member

    // function
    if (shiftData.project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      if ('function' in req.body) {
        req.body.function = await modifyDefaultDataUtils.createDefaultFunction(
          req.body.function,
          shiftData.project._id,
          req.userData.account
        );
      }
    }
    // function
    const newReq = { ...req.body, updatedBy: req.userData._id };

    if ('member' in req.body || 'function' in req.body) {
      const memberToCheck = req.body.member || exist.member;
      const functionToCheck = req.body.function || exist.function;

      const isTeamMemberExist = await teamMemberService.getTeamMemberByFilterData({
        member: memberToCheck,
        function: functionToCheck,
        shift: exist.shift,
        deletedAt: null,
        _id: { $ne: id },
      });

      if (isTeamMemberExist) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(responseUtils.errorResponse(constantUtils.TEAM_MEMBER_SAME_FUNCTION_EXIST));
      }
    }

    // end default project

    const response = await teamMemberService.updateTeamMember(id, newReq);

    if ('notInList' in req.body) {
      for (const element of req.body.notInList) {
        const id = element._id;
        const rest = { ...element };
        delete rest._id;

        await notInListService.updateNotInList({ _id: id }, rest);
      }
    }

    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.UPDATE_TEAM_MEMBER, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonFunctionsUtils.updateSyncApiManage({
    syncApis: ['shifts'],
    account,
  });
};
