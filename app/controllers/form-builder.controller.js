const responseUtils = require('../utils/response.utils');
const constantUtils = require('../utils/constants.utils');

// Service
const projectServices = require('../services/project.service');
const categoryServices = require('../services/category.service');
const locationServices = require('../services/location.service');
const severityServices = require('../services/severity.service');
const likelihoodServices = require('../services/likelihood.service');
const typeServices = require('../services/type.service');
const formbuilderService = require('../services/form-builder.service');

/**
 * Create Question
 *
 * @param {*} req
 * @param {*} res
 */
exports.create = async (req, res) => {
  try {
    let reqData = req.body;
    reqData.account = req.userData.account;

    const filterData = {
      fieldName: reqData.fieldName,
      account: reqData.account,
      cardType: reqData.cardType,
      isActive: true,
    };

    const exist = await formbuilderService.getFieldByFilter(filterData);

    if (exist.length > 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.FIELD_EXIST));
    }

    await formbuilderService.create(reqData);
    res.status(200).json(responseUtils.successResponse(constantUtils.CREATED_FIELDS));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.list = async (req, res) => {
  try {
    const { cardtype } = req.params;
    let status;
    const filterData = {
      account: req.userData.account,
      ...(status === true && { isActive: true }),
      deletedAt: null,
      isDefault: false,
    };
    const projectList = await projectServices.getAllProjects(filterData);
    if (cardtype == 'safe') {
      filterData.isVisibleForSafeCard = true;
    } else if (cardtype == 'unsafe') {
      filterData.isVisibleForUnsafeCard = true;
    } else if (cardtype == 'incident') {
      filterData.isVisibleForIncidentCard = true;
    }
    const categoryList = await categoryServices.getAllCategory(filterData);
    const locationsList = await locationServices.getAllLocation();
    const severityList = await severityServices.getAllSeverity();
    const likelihoodList = await likelihoodServices.getAllLikelihood();
    const typeList = await typeServices.getAllType(0, 10, filterData);

    let staticField = [];

    let staticFieldJsonformat = {
      _id: '',
      title: '',
      fieldName: '',
      fieldType: '',
      fieldSortOrder: '',
      optionValue: [],
      createdBy: {},
    };
    let titlefield = { ...staticFieldJsonformat };
    titlefield.fieldName = 'Title';
    titlefield.fieldType = 'Text';
    titlefield.fieldSortOrder = 0;
    staticField.push(titlefield);

    let descriptionfield = { ...staticFieldJsonformat };
    descriptionfield.fieldName = 'Description';
    descriptionfield.fieldType = 'textarea';
    descriptionfield.fieldSortOrder = 0;

    staticField.push(descriptionfield);
    let temp = null;
    if (cardtype === 'safe') {
      temp = {
        Project: projectList,
        Location: locationsList,
        category: categoryList,
      };
    } else if (cardtype === 'unsafe') {
      temp = {
        Project: projectList,
        Location: locationsList,
        category: categoryList,
        severity: severityList,
        likelihood: likelihoodList,
      };
    } else if (cardtype === 'ncr') {
      temp = {
        Project: projectList,
        Location: locationsList,
      };
      let itemFields = { ...staticFieldJsonformat };
      itemFields.fieldName = 'Item / Component / Cable';
      itemFields.fieldType = 'Text';
      itemFields.fieldSortOrder = 0;
      staticField.push(itemFields);

      let correctiveActionFields = { ...staticFieldJsonformat };
      correctiveActionFields.fieldName = 'Corrective actions / recommendations';
      correctiveActionFields.fieldType = 'textarea';
      correctiveActionFields.fieldSortOrder = 0;
      staticField.push(correctiveActionFields);

      let preventiveActionFields = { ...staticFieldJsonformat };
      preventiveActionFields.fieldName = 'Preventive actions / recommendations';
      preventiveActionFields.fieldType = 'textarea';
      preventiveActionFields.fieldSortOrder = 0;
      staticField.push(preventiveActionFields);

      let estimatedDelayCostFields = { ...staticFieldJsonformat };
      estimatedDelayCostFields.fieldName = 'Estimated delay / cost';
      estimatedDelayCostFields.fieldType = 'Text';
      estimatedDelayCostFields.fieldSortOrder = 0;
      staticField.push(estimatedDelayCostFields);
    } else {
      temp = {
        Location: locationsList,
        category: categoryList,
        severity: severityList,
        likelihood: likelihoodList,
        type: typeList,
      };

      let subjectFields = { ...staticFieldJsonformat };
      subjectFields.fieldName = 'Subject';
      subjectFields.fieldType = 'Text';
      subjectFields.fieldSortOrder = 0;
      staticField.push(subjectFields);

      let dateTimeFields = { ...staticFieldJsonformat };
      dateTimeFields.fieldName = 'Time of incident';
      dateTimeFields.fieldType = 'time';
      staticField.push(dateTimeFields);
    }

    Object.entries(temp).forEach(([key, value]) => {
      let fields = { ...staticFieldJsonformat };
      fields.fieldName = key;
      fields.fieldType = value.length > 0 ? 'Options' : 'Text';
      fields.optionValue = value.map(item => {
        if (item.title) {
          return { optionText: item.title };
        } else {
          return { optionText: item.categoryName };
        }
      });
      staticField.push(fields);
    });

    let statusUpdatefield = { ...staticFieldJsonformat };
    statusUpdatefield.fieldName = 'Status Update';
    statusUpdatefield.fieldType = 'Text';
    statusUpdatefield.fieldSortOrder = 0;
    staticField.push(statusUpdatefield);

    let stagefield = { ...staticFieldJsonformat };
    stagefield.fieldName = 'Status';
    stagefield.fieldType = 'option';
    let stageOption = ['Open', 'Submitted', 'In Discussion', 'Closed'];
    stagefield.optionValue = stageOption.map(item => {
      return { optionText: item };
    });
    staticField.push(stagefield);

    const data = await formbuilderService.getAllFields(req.userData.account, cardtype);
    let safetyCArdQuestion = {
      dyamicField: data,
      staticField,
    };
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DYNAMIC_FIELDS, safetyCArdQuestion));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Question
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteQuestion = async (req, res) => {
  try {
    const id = req.params.id;
    const exist = await formbuilderService.getQuestionById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_FIELDS));
    }
    const data = await formbuilderService.disableField(id);

    return res.status(200).json(responseUtils.successResponse(constantUtils.DELETED_FIELDS, data));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Question
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateQuestion = async (req, res) => {
  try {
    const id = req.params.id;
    const exist = await formbuilderService.getQuestionById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_FIELDS));
    }
    const data = await formbuilderService.updateField(id, req.body);

    return res.status(200).json(responseUtils.successResponse(constantUtils.UPDATED_FIELDS, data));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
