require('dotenv').config();

// Services
const categoryService = require('../services/category.service');
const authService = require('../services/auth.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

/**
 * Get All Category
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllCategory = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 10;
    const tokenData = await authService.decodeJwtToken(req.headers.authorization);

    if (!tokenData) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SEND_AUTHENTICATION_TOKEN));
    }
    const filterData = {};
    filterData.account = req.userData.account;
    filterData.deletedAt = null;
    const categoryList = await categoryService.getAllCategory(filterData, page, perPage);
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.ALL_CATEGORY_LIST, categoryList));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Create Category
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createCategory = async (req, res) => {
  try {
    const reqData = req.body;

    const exist = await categoryService.getCategoryByName(
      reqData.categoryName,
      req.userData.account
    );

    if (exist.length > 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.CATEGORY_EXIST));
    }

    reqData.account = req.userData.account;

    const createdCategory = await categoryService.createCategory(reqData);
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_CATEGORY, createdCategory));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Category
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateCategory = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await categoryService.getCategoryById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_CATEGORY));
    }
    const response = await categoryService.updateCategory(id, req.body);
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_CATEGORY, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Category
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteCategory = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await categoryService.getCategoryById(id);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_CATEGORY));
    }
    const response = await categoryService.deleteCategory(id, req.deletedAt);

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_CATEGORY, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
