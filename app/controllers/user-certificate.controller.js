// services
const userCertificateService = require('../services/user-certificate.service');
const { successResponse, errorResponse } = require('../utils/response.utils');
const constants = require('../utils/constants.utils');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

/**
 * Update Document Data
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateDocumentData = async (req, res) => {
  try {
    let { id, fileType, fileId } = req.params;
    let reqData = req.body;

    // check the data exist
    const isExist = await userCertificateService.getSingleRecord({ _id: id });

    if (!isExist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.USER_CERTIFICATE_NOT_EXIST));
    }

    // set the parameters for update
    let setData = {};

    Object.keys(reqData).forEach(key => {
      setData[`${fileType}.$.${key}`] = reqData[key];
    });

    // set the parameter for search
    let fieldSearch = {};
    fieldSearch['_id'] = id;
    fieldSearch[`${fileType}._id`] = fileId;

    // update the file data
    const updateData = await userCertificateService.updateFileData(fieldSearch, setData);

    // check if record not updated
    if (updateData.modifiedCount === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.DOCUMENT_NOT_EXIST));
    }

    // set the paramters for seach in document
    let filerFiled = {};
    filerFiled[`${fileType}`] = { $elemMatch: { _id: fileId } };

    // get the updated record
    const responseData = await userCertificateService.getFileData(id, filerFiled);

    return res
      .status(200)
      .json(successResponse(constantUtils.UPDATE_USER_CERTIFICATE, responseData));
  } catch (err) {
    return res
      .status(500)
      .json(errorResponse(constants.SOMETHING_WENT_WRONG, { message: err.message }));
  }
};
