// services
const feedbackService = require('../services/feedback.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');

/**
 * Create Feedback
 *
 * @param {*} req
 * @param {*} res
 */
exports.createFeedback = async (req, res) => {
  try {
    let requestData = req.body;
    requestData.account = req.userData.account;
    requestData.createdBy = req.userData._id;
    if (!req.userData.isMobile) {
      return res
        .status(401)
        .json(responseUtils.errorResponse(constantUtils.ONLY_ADMIN_ACCESS_TO_RESOURCES));
    }
    const feedbackData = await feedbackService.createFeedback(requestData);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_FEEDBACK, feedbackData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Filter Feedback
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllFeedback = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 10;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

    let filterData = req.query;

    let removeKeys = ['page', 'perPage', 'sort'];
    filterData = await commonUtils.filterParamsModify(filterData, removeKeys);

    filterData.account = req.userData.account;
    filterData.deletedBy = null;
    filterData.deletedAt = null;

    const feedbackData = await feedbackService.filterFeedback(filterData, page, perPage, sort);

    // add all records count
    let finalResponse = {
      feedbackData,
      currentPage: Number(page),
    };
    finalResponse = await commonUtils.getCountFromQuery('feedback', filterData, finalResponse);

    res.status(200).json(responseUtils.successResponse(constantUtils.FEEDBACK_LIST, finalResponse));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
