// Services
const equipmentWareHouseService = require('../services/equipment-warehouse.service');
const equipmentService = require('../services/equipment.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

/**
 * Create EquipmentWareHouse
 *
 * @param {*} req
 * @param {*} res
 */
exports.createEquipmentWarehouse = async (req, res) => {
  try {
    const requestData = req.body;
    const response = await equipmentWareHouseService.createEquipmentWarehouse(requestData);

    if (response) {
      let updateEquipment = {
        equipmentWarehouse: response._id,
      };
      await equipmentService.updateEquipment(response.equipment, updateEquipment);
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT_WAREHOUSE, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
