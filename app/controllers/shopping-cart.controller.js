// service
const shoppingCartService = require('../services/shopping-cart.service');
const equipmentOrderService = require('../services/equipment-order.service');
const pmOrderService = require('../services/pm-order.service');
const pmOrderManageEquipmentService = require('../services/pm-order-manage-equipment.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const HTTP_STATUS = require('../utils/status-codes');
const commonUtils = require('../utils/common.utils');

/**
 * Create Shopping Cart
 *
 * @param {*} req
 * @param {*} res
 */
exports.createShoppingCart = async (req, res) => {
  try {
    const { title, project, fromDate, toDate } = req.body;
    const { requestedEquipmentType } = req.body;
    let shoppingCart;

    const reqData = {
      title,
      project,
      fromDate,
      toDate,
      account: req.userData.account,
      createdBy: req.userData._id,
    };

    [shoppingCart] = await shoppingCartService.createShoppingCart(reqData);

    const pmOrderData = await pmOrderService.createPMOrder({
      account: req.userData.account,
      project,
      status: 'open',
      createdBy: req.userData._id,
    });

    if (requestedEquipmentType && requestedEquipmentType.length > 0) {
      for (let item of requestedEquipmentType) {
        if (!commonUtils.isValidId(item.equipmentType)) {
          return res
            .status(HTTP_STATUS.BAD_REQUEST)
            .json(responseUtils.errorResponse(constantUtils.INVALID_ID));
        }

        await equipmentOrderService.findOneAndUpdateEquipmentType(
          {
            _id: item.requestId,
            account: req.userData.account,
            project,
            equipmentType: item.equipmentType,
            status: 'pending',
          },
          {
            shoppingCart: shoppingCart._id,
            updatedBy: req.userData._id,
          }
        );

        let filter = {
          account: req.userData.account,
          shoppingCart: shoppingCart._id,
          equipmentType: item.equipmentType,
          status: { $in: ['pending', 'queue'] },
        };

        const equipmentOrderData = await equipmentOrderService.getEquipmentOrder(filter);

        const pmManageEquipmentData =
          await pmOrderManageEquipmentService.createPMOrderManageEquipment({
            account: req.userData.account,
            status: 'open',
            pmRequestedQuantity: equipmentOrderData.engineerRequestedQuantity,
            pmOrder: pmOrderData._id,
            equipmentType: item.equipmentType,
          });

        await equipmentOrderService.findOneAndUpdateEquipmentType(
          {
            _id: item.requestId,
            account: req.userData.account,
            project,
            equipmentType: item.equipmentType,
          },
          {
            shoppingCart: shoppingCart._id,
            status: 'queue',
            pmApprovedQuantity: equipmentOrderData.engineerRequestedQuantity,
            updatedBy: req.userData._id,
            pmOrderId: pmOrderData._id,
            pmOrderManageEquipment: pmManageEquipmentData._id,
          }
        );
      }
    }

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.SHOPPING_CART_CREATED));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * get Shopping Cart List
 *
 * @param {*} req
 * @param {*} res
 */
exports.getShoppingCartList = async (req, res) => {
  try {
    const project = req.params.id;
    const { status } = req.query;
    const filter = {
      status: status || 'pending',
      project,
      account: req.userData.account,
      deletedAt: null,
    };

    const shoppingCarts = await shoppingCartService.getShoppingCart(filter);
    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.SHOPPING_CART_CREATED, shoppingCarts));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * reject Shopping Cart
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.rejectShoppingCart = async (req, res) => {
  try {
    const { id } = req.params;
    const { equipmentType, remark } = req.body;

    let exist = await shoppingCartService.getShoppingCartByFilter({
      _id: id,
      account: req.userData.account,
      deletedAt: null,
    });

    if (!exist) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.SHOPPING_CART_NOT_FOUND));
    }

    let updateData = {
      status: 'rejected',
      updatedBy: req.userData._id,
      updatedAt: new Date(),
    };

    let message = constantUtils.UPDATE_EQUIPMENT_ORDER_REQUEST;

    if (!equipmentType) {
      await shoppingCartService.updateShoppingCartById(id, updateData);
      message = constantUtils.REJECT_SHOPPING_CART;
    }

    await equipmentOrderService.updateEquipmentOrderByFilter(
      {
        ...(equipmentType && { equipmentType }),
        shoppingCart: id,
        account: req.userData.account,
        deletedAt: null,
      },
      {
        ...updateData,
        ...(remark && { remark }),
      }
    );

    return res.status(HTTP_STATUS.OK).json(responseUtils.successResponse(message));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};
