// Services
const equipmentImageCertificateService = require('../services/equipment-image-certificate.service');
const equipmentService = require('../services/equipment.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');

/**
 * Create EquipmentImageCertificate
 *
 * @param {*} req
 * @param {*} res
 */
exports.createEquipmentImageCertificate = async (req, res) => {
  try {
    const requestData = req.body;
    const response = await equipmentImageCertificateService.createEquipmentImageCertificate(
      requestData
    );

    if (response) {
      let updateEquipment = {
        equipmentImagesCertificates: response._id,
      };
      await equipmentService.updateEquipment(response.equipment, updateEquipment);
    }

    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.CREATE_EQUIPMENT_IMAGE_CERTIFICATE, response)
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
