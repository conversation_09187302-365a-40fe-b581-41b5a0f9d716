require('dotenv').config();
const mongoose = require('mongoose');

// services
const syncApiService = require('../services/sync-api.service');
const syncApiManageService = require('../services/sync-api-manage.service');

// Utils
const commonUtils = require('../utils/common.utils');
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const { transactionOptions } = require('../utils/json-format.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Manage Sync API
 *
 * @param {*} req
 * @param {*} res
 */
exports.manageSyncApi = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOption = { ...transactionOptions };
  try {
    let { configMetaData } = req.body;
    const { account } = req.userData;
    session.startTransaction(transactionOption);
    let responseData = [];
    for (const data of configMetaData) {
      let { syncApiId, apiName, hashKey, deviceId } = data;
      // Check if the API already exists
      let filterSyncApi = syncApiId !== null ? { _id: syncApiId } : { apiName };
      let syncApi = await syncApiService.findOneSyncApi(filterSyncApi);
      if (!syncApi) {
        // Create a new API if it doesn't exist
        syncApi = await syncApiService.createSyncApi({ apiName }, session);
      }

      syncApiId = syncApi._id;
      let hashKeyManage = hashKey;
      hashKey = hashKey || commonUtils.generateOrderNumber(10, 'sync');

      // Check if the API manage already exists
      let syncApiManage = await syncApiManageService.findOneSyncApiManage({
        syncApi: syncApiId,
        account,
        deviceId,
        deletedAt: null,
      });
      if (!syncApiManage) {
        syncApiManage = await syncApiManageService.createSyncApiManage(
          {
            syncApi: syncApiId,
            hashKey,
            account,
            deviceId,
          },
          session
        );
      }
      responseData.push({
        syncApiId,
        apiName,
        deviceId,
        hashKey: syncApiManage.hashKey,
        isSynced: syncApiManage.hashKey === hashKeyManage ? true : false,
      });
    }
    // Commit the transaction
    await session.commitTransaction();
    session.endSession();
    // Send the response
    res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.CHECK_MANAGE_SYNC_API, responseData));
  } catch (err) {
    // Rollback the transaction in case of an error
    await session.abortTransaction();
    session.endSession();
    // Handle the error
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(err.message));
  }
};
