// utils
const { generateAndExportPdf } = require('../utils/export-pdf.utils');
const { convertImageUrlToBase64Image } = require('../utils/common-function.utils');
const { soloReport } = require('../../pdf_templates/report.template');
const { safetyCard } = require('../../pdf_templates/safetyCard.template');
const { orderList } = require('../../pdf_templates/orderList.template');
const { toolbox } = require('../../pdf_templates/toolbox.template');
const { shiftDetails } = require('../../pdf_templates/shiftDetails.template');
const { report } = require('../../pdf_templates/newReport.template');
const { locationProgress } = require('../../pdf_templates/locationProgress.template');
const { header, footer } = require('../../pdf_templates/header-footer.template');

/**
 * export pdf
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.exportPdfTemplates = async (req, res) => {
  try {
    let pdfHtmlTemplate;
    const pdfName = req.query.name;
    const companyLogo = global.constant.APP_LOGO;
    const reynardLogo = global.constant.APP_LOGO;

    const base64ReynardLogo = await convertImageUrlToBase64Image(reynardLogo);
    const base64CompanyLogo = await convertImageUrlToBase64Image(companyLogo);

    const headerTemplate = await header({ base64CompanyLogo });
    const footerTemplate = await footer({ base64ReynardLogo });

    let options = {
      format: 'A4',
      orientation: 'portrait',
      printBackground: true,
      margin: {
        top: '30mm',
        right: '10mm',
        bottom: '20mm',
        left: '10mm',
      },
      displayHeaderFooter: true,
      headerTemplate: headerTemplate,
      footerTemplate: footerTemplate,
    };
    const templateData = {
      companyPrimaryColor: global.constant.Reynard_PRIMARY_COLOR,
    };

    if (pdfName === 'safetyCard') {
      pdfHtmlTemplate = await safetyCard(templateData);
    } else if (pdfName === 'orderList') {
      pdfHtmlTemplate = await orderList(templateData);
    } else if (pdfName === 'toolbox') {
      pdfHtmlTemplate = await toolbox(templateData);
    } else if (pdfName === 'shiftDetails') {
      pdfHtmlTemplate = await shiftDetails(templateData);
    } else if (pdfName === 'report') {
      pdfHtmlTemplate = await report(templateData);
    } else if (pdfName === 'locationProgress') {
      pdfHtmlTemplate = await locationProgress(templateData);
    } else {
      pdfHtmlTemplate = await soloReport();
    }

    // Generate and send PDF using the utility function
    await generateAndExportPdf(pdfHtmlTemplate, res, options);
  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).send(error.toString());
  }
};
