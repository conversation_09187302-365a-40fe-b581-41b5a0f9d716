// Services
const profileFunctionService = require('../services/profile-function.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const { toObjectId } = require('../utils/common.utils');

/**
 * Get All Profile Function
 * @param {*} req
 *
 * @param {*} res
 */
exports.getAllProfileFunction = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : '';
    let perPage = req.query.perPage ? req.query.perPage : '';
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let isActive = req.query.isActive ?? '';
    const newReq = {
      account: req.userData.account,
      deletedAt: null,
      ...(isActive !== '' && { isActive: isActive === 'true' }),
    };
    const profileFunctionList = await profileFunctionService.getAllProfileFunction(
      newReq,
      page,
      perPage,
      sort
    );

    res
      .status(200)
      .json(
        responseUtils.successResponse(
          constantUtils.PROFILE_FUNCTION_RETRIVED_SUCCESSFULLY,
          profileFunctionList
        )
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Create Profile Function
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createProfileFunction = async (req, res) => {
  try {
    const { name, sortOrder } = req.body;
    const newReq = {
      account: req.userData.account,
      name: name,
      sortOrder,
      createdBy: req.userData._id,
    };

    const exist = await profileFunctionService.getProfileFunctionByName({
      account: req.userData.account,
      name: name,
      deletedAt: null,
    });

    if (exist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PROFILE_FUNCTION_EXIST));
    }

    newReq.isActive = req.body.isActive ?? true;

    const profileFunction = await profileFunctionService.createProfileFunction(newReq);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.PROFILE_FUNCTION_CREATED, profileFunction));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Profile Function
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateProfileFunction = async (req, res) => {
  try {
    const { name, sortOrder } = req.body;
    const currentRecordId = req.params.id;

    let reqData = {};
    const newReq = {
      account: req.userData.account,
      name: name,
      sortOrder,
    };

    const exist = await profileFunctionService.getProfileFunctionByName({
      _id: { $ne: toObjectId(currentRecordId) },
      account: toObjectId(req.userData.account),
      deletedAt: null,
      name: name,
    });

    if (exist && exist._id.toString() !== req.params.id) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PROFILE_FUNCTION_EXIST));
    }

    reqData = {
      ...newReq,
      ...req.body,
      updatedBy: req.userData._id,
    };

    const profileFunction = await profileFunctionService.updateProfileFunction(
      req.params.id,
      reqData
    );

    if (!profileFunction) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_PROFILE_FUNCTION));
    }

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.PROFILE_FUNCTION_UPDATED, profileFunction));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Profile Function
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteProfileFunction = async (req, res) => {
  try {
    let id = req.params.id;
    const exist = await profileFunctionService.getProfileFunctionById(id, req.userData.account);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_PROFILE_FUNCTION));
    }
    const data = {
      ...req.deletedAt,
    };
    const response = await profileFunctionService.deleteProfileFunction(id, data);

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_PROFILE_FUNCTION, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
