// Services
const reportDocumentService = require('../services/report-document.service');
// utils
const constantUtils = require('../utils/constants.utils');
const { successResponse, errorResponse } = require('../utils/response.utils');
const { toObjectId } = require('../utils/common.utils');
/**
 * Create Report Documnet
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createReportDocument = async (req, res) => {
  try {
    const reqData = req.body;
    reqData.account = req.userData.account;
    reqData.createdBy = req.userData._id;

    const filter = {
      name: reqData.name,
      userProjectReport: toObjectId(reqData.userProjectReport),
      account: req.userData.account,
      deletedAt: null,
    };

    const isExists = await reportDocumentService.getReportDocument(filter);

    if (isExists) {
      return res.status(400).json(errorResponse(constantUtils.REPORT_DOCUMENT_EXIST));
    }

    const createResponse = await reportDocumentService.createReportDocument(reqData);

    return res
      .status(200)
      .json(successResponse(constantUtils.CREATE_REPORT_DOCUMENT, createResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update Report Documnet
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateReportDocument = async (req, res) => {
  try {
    const id = req.params.id;
    let filter = {
      _id: toObjectId(id),
      account: req.userData.account,
      deletedAt: null,
    };
    const isExists = await reportDocumentService.getReportDocument(filter);
    if (!isExists) {
      return res.status(400).json(errorResponse(constantUtils.REPORT_DOCUMENT_NOT_EXIST));
    }
    const createResponse = await reportDocumentService.updateReportDocument(filter, req.body);
    return res
      .status(200)
      .json(successResponse(constantUtils.UPDATE_REPORT_DOCUMENT, createResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Report Documnet
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getReportDocument = async (req, res) => {
  try {
    const userProjectReportId = req.params.id;
    let filter = {
      userProjectReport: toObjectId(userProjectReportId),
      account: req.userData.account,
      deletedAt: null,
    };
    const reportDocuments = await reportDocumentService.getReportDocumentByReportProject(filter);

    return res
      .status(200)
      .json(successResponse(constantUtils.GET_REPORT_DOCUMENT, reportDocuments));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Delete Report Documnet
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteReportDocument = async (req, res) => {
  try {
    const id = req.params.id;
    let filter = {
      _id: toObjectId(id),
      account: req.userData.account,
      deletedAt: null,
    };
    const isExists = await reportDocumentService.getReportDocument(filter);
    if (!isExists) {
      return res.status(400).json(errorResponse(constantUtils.REPORT_DOCUMENT_NOT_EXIST));
    }
    const deleteResponse = await reportDocumentService.updateReportDocument(filter, req.deletedAt);

    return res
      .status(200)
      .json(successResponse(constantUtils.DELETE_REPORT_DOCUMENT, deleteResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};
