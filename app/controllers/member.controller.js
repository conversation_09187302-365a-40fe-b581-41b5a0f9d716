require('dotenv').config();

// Services
const memberService = require('../services/member.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const { toObjectId } = require('../utils/common.utils');
const commonFunction = require('../utils/common-function.utils');
/**
 * Get All Member
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllMember = async (req, res) => {
  try {
    let reqData = {};
    if (req.query.isActive) {
      const memberList = await memberService.getAllAccountMember(req.userData.account);

      res
        .status(200)
        .json(responseUtils.successResponse(constantUtils.ALL_MEMBER_LIST, memberList));
    } else {
      const newReq = {
        ...reqData,
        account: req.userData.account,
        project: req.query.project,
        function: req.query.function,
      };
      const memberList = await memberService.getAllMember(newReq);
      res
        .status(200)
        .json(responseUtils.successResponse(constantUtils.ALL_MEMBER_LIST, memberList));
    }
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Member
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateMember = async (req, res) => {
  try {
    let id = req.params.id;
    const filerData = {
      _id: toObjectId(id),
      account: req.userData.account,
    };
    const exist = await memberService.getMemberById(filerData);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_MEMBER));
    }

    const response = await memberService.updateMember(id, req.body);

    // update sync api manage data
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_MEMBER, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Member
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteMember = async (req, res) => {
  try {
    let id = req.params.id;
    const filerData = {
      _id: toObjectId(id),
      account: req.userData.account,
    };
    const exist = await memberService.getMemberById(filerData);

    if (!exist) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_MEMBER));
    }
    const response = await memberService.deleteMember(id, req.deletedAt);

    // update sync api manage data
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_MEMBER, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.createSingleMember = async (req, res) => {
  try {
    let reqData = req.body;
    reqData.account = req.userData.account;

    // check data exist
    const getData = await memberService.getAllMember({
      project: reqData.project,
      user: reqData.user,
      deletedAt: null,
    });

    if (getData.length > 0) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.PROJECT_ALREADY_ASSIGN));
    }

    const createdMember = await memberService.createMember(reqData);

    if (createdMember) {
      // update sync api manage data
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_MEMBER, createdMember));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Sync API Manage
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.commonUpdateSyncApiManage = async account => {
  await commonFunction.updateSyncApiManage({
    syncApis: ['members', 'toolboxConfig', 'reportConfig'],
    account,
  });
};
