require('dotenv').config();

// services
const projectStringService = require('../services/project-string.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const { validateNameInput } = require('../utils/common-function.utils');

/**
 * Create ProjectString
 *
 * @param {*} req
 * @param {*} res
 */
exports.createProjectString = async (req, res) => {
  try {
    req.body.account = req.userData.account;

    const { name, project } = req.body;

    const validateName = await validateNameInput(name);

    if (!validateName) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_NAME));
    }

    const filterData = {
      name: { $regex: new RegExp(`^${validateName}$`, 'i') },
      project,
      account: req.userData.account.toString(),
      deletedAt: null,
    };
    const exist = await projectStringService.getProjectStringByProjectIdAndName(filterData);

    if (exist.length > 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.PROJECT_STRING_EXIST));
    }
    const projectStringData = await projectStringService.createProjectString(req.body);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_PROJECT_STRING, projectStringData));
  } catch (err) {
    const errCode = err.code ?? 500;
    res.status(errCode).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Update By Id
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateProjectString = async (req, res) => {
  try {
    const id = req.params.id;
    const exist = await projectStringService.getProjectStringById(id);

    if (exist.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_PROJECT_STRING));
    }

    const validateName = await validateNameInput(req.body.name);

    if (!validateName) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_NAME));
    }
    const filterData = {
      _id: { $ne: id },
      name: { $regex: new RegExp(validateName, 'i') },
      project: req.body.project,
      account: req.userData.account,
      deletedAt: null,
    };
    const stringExist = await projectStringService.getProjectStringByProjectIdAndName(filterData);

    if (stringExist.length > 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.PROJECT_STRING_EXIST));
    }

    req.body.account = req.userData.account;

    const projectStringData = await projectStringService.updateProjectString(id, req.body);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.UPDATE_PROJECT_STRING, projectStringData));
  } catch (err) {
    const errCode = err.code ?? 500;
    res.status(errCode).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Delete ProjectString
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteProjectString = async (req, res) => {
  try {
    const id = req.params.id;
    const exist = await projectStringService.getProjectStringById(id);

    if (exist.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_PROJECT_STRING));
    }
    const projectStringData = await projectStringService.deleteProjectString(id, req.deletedAt);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.DELETE_PROJECT_STRING, projectStringData));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};
