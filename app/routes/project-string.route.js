// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const projectStringController = require('../controllers/project-string.controller');

// Validator
const validator = require('../validators/project-string.validator');

// create project string
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.projectStringValidationRule(),
  validate,
  projectStringController.createProjectString
);

// update project string
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.updateProjectStringValidationRule(),
  validate,
  projectStringController.updateProjectString
);

// delete project string
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  deletedAt,
  validate,
  projectStringController.deleteProjectString
);

module.exports = routes;
