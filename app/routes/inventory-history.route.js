// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const inventoryHistoryController = require('../controllers/inventory-history.controller');

// get all inventory history
routes.get(
  '/:equipment',
  verifyToken,
  authAccount,
  validate,
  inventoryHistoryController.getInventoryHistory
);

module.exports = routes;
