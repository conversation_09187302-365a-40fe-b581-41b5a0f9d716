// library
const express = require('express');
const routes = express.Router();

// controller
const syncController = require('../controllers/sync.controller');

// middleware
const { verifyToken, deletedAt } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// create sync up
routes.post('/up', verifyToken, deletedAt, validate, syncController.syncUp);

module.exports = routes;
