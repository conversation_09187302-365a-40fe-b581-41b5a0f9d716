// library
const express = require('express');
const routes = express.Router();

// controller
const teamMemberController = require('../controllers/team-member.controller');

// validator
const validator = require('../validators/team-member.validator');

// middleware
const { verifyToken, deletedAt } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

routes.post(
  '',
  verifyToken,
  validator.teamMemberValidationRule(),
  validate,
  teamMemberController.createTeamMember
);
routes.patch(
  '/batch-update',
  verifyToken,
  validator.personnelValidationRule(),
  validate,
  teamMemberController.batchUpdateTeamMembers
);
routes.patch(
  '/:id',
  verifyToken,
  validator.updateTeamMemberValidationRule(),
  validate,
  teamMemberController.updateTeamMember
);
routes.delete('/:id', verifyToken, deletedAt, validate, teamMemberController.deleteTeamMember);

module.exports = routes;
