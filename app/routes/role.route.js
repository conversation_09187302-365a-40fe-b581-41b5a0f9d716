const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/role.validator');

// Controller
const roleController = require('../controllers/role.controller');

// Middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// Create
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.roleValidationRule(),
  validate,
  roleController.createRole
);
routes.get('', verifyToken, authAccount, validate, roleController.getAllRoles);
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.updateRoleValidationRule(),
  validate,
  roleController.updateRole
);
routes.delete('/:id', verifyToken, authAccount, deletedAt, validate, roleController.deleteRole);

module.exports = routes;
