// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const accountLicenceController = require('../controllers/account-licence.controller');

// Validator
const validator = require('../validators/account-licence.validator');

// create permission
routes.post(
  '/request',
  verifyToken,
  validator.accountLicenceValidationRule(),
  validate,
  accountLicenceController.requestAccountLicence
);

// Pending request list
routes.get('/pending-requests', verifyToken, validate, accountLicenceController.getPendingRequests);

// Action for accept/reject request
routes.patch(
  '/:id/respond',
  verifyToken,
  validate,
  accountLicenceController.actionForApproveOrReject
);

module.exports = routes;
