// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const equipmentOrderController = require('../controllers/equipment-order.controller');

routes.get(
  '/counter',
  verifyToken,
  authAccount,
  validate,
  equipmentOrderController.getPmEquipmentCounter
);

// Equipment Type order history
routes.get(
  '/history',
  verifyToken,
  authAccount,
  validate,
  equipmentOrderController.getEquipmentTypesOrderHistory
);

// get equipment orders
routes.get('/orders', verifyToken, validate, equipmentOrderController.getProjectOrderList);

// get equipment orders by shopping cart
routes.get(
  '/shopping-cart-project-equipment-type',
  verifyToken,
  validate,
  equipmentOrderController.getShoppingCartWiseProjectEquipmentType
);

// get equipment orders by project
routes.get(
  '/project/:id',
  verifyToken,
  validate,
  equipmentOrderController.getEquipmentOrderByProject
);

routes.get(
  '/shopping-cart/:id',
  verifyToken,
  validate,
  equipmentOrderController.getEquipmentOrderByShoppingCart
);

// create equipment Order Request
routes.post('', verifyToken, validate, equipmentOrderController.createEquipmentOrder);

routes.get('', verifyToken, validate, authAccount, equipmentOrderController.listEquipmentOrder);

routes.get(
  '/list-shopping-cart-project-equipment-order',
  verifyToken,
  validate,
  authAccount,
  equipmentOrderController.listShoppingCartProjectEquipmentOrder
);

routes.get('/:id', verifyToken, validate, equipmentOrderController.getEquipmentOrderById);

routes.patch('/change-status', verifyToken, validate, equipmentOrderController.changeOrderStatus);

module.exports = routes;
