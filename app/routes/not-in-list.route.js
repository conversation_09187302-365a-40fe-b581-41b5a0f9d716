// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const notInListController = require('../controllers/not-in-list.controller');

// delete single not in list member
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  deletedAt,
  validate,
  notInListController.deleteNotInListMembers
);

module.exports = routes;
