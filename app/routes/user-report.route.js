// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/user-report.validator');

// middleware
const { verifyToken, authAccount, deletedAt } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const userReportController = require('../controllers/user-report.controller');

// Create User Report
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.userReportValidationRule(),
  validate,
  userReportController.createUserReport
);

routes.get('', verifyToken, authAccount, validate, userReportController.getUserReports);

// get report by project, manage-report, location, assets
routes.get('/final-reports', verifyToken, authAccount, validate, userReportController.getReports);

// export report pdf
routes.post(
  '/export-report-pdf',
  verifyToken,
  authAccount,
  validate,
  userReportController.exportReportPdf
);

routes.post(
  '/report-details',
  verifyToken,
  authAccount,
  validator.userReportDetailValidationRule(),
  validate,
  userReportController.getUserReportDetails
);

routes.get(
  '/report-details/:userProjectReportId',
  verifyToken,
  authAccount,
  validate,
  userReportController.getUserReportDetails
);

routes.patch(
  '/update-status',
  verifyToken,
  authAccount,
  validator.userReportDetailValidationRule(),
  validate,
  userReportController.updateUserReportStatus
);

routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  deletedAt,
  userReportController.deleteUserReport
);

routes.post(
  '/delete-report',
  verifyToken,
  authAccount,
  validator.userReportDetailValidationRule(),
  validate,
  deletedAt,
  userReportController.deleteReport
);

// View report details for mobile
routes.post(
  '/report-view',
  verifyToken,
  authAccount,
  validator.userReportValidationRule(),
  validate,
  userReportController.getUserReportViewDetails
);

routes.post(
  '/get-user-project-report',
  verifyToken,
  authAccount,
  validator.userReportValidationRule(),
  validate,
  userReportController.getUserProjectReport
);

// create web report
routes.post(
  '/create-web-report',
  verifyToken,
  authAccount,
  validator.webUserReportValidationRule(),
  validate,
  userReportController.createUserWebReport
);

// get all user reports except request user
routes.get(
  '/all-user-reports',
  verifyToken,
  authAccount,
  validate,
  userReportController.getAllUserReportsExceptRequestUser
);

module.exports = routes;
