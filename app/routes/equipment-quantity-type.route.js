// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
  updatedBy,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const {
  createEquipmentQuantityTypeValidationRule,
  updateEquipmentQuantityTypeValidationRule,
} = require('../validators/equipment-quantity-type.validator');

// controller
const equipmentQuantityTypeController = require('../controllers/equipment-quantity-type.controller');

//Create
routes.post(
  '',
  verifyToken,
  authAccount,
  createEquipmentQuantityTypeValidationRule(),
  validate,
  defaultCreatedDetails,
  equipmentQuantityTypeController.createEquipmentQuantityType
);

// Get EquipmentUnit
routes.get(
  '',
  verifyToken,
  authAccount,
  validate,
  equipmentQuantityTypeController.getEquipmentQuantityType
);

// Update EquipmentUnit
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  updateEquipmentQuantityTypeValidationRule(),
  validate,
  updatedBy,
  equipmentQuantityTypeController.updateEquipmentQuantityType
);

// Delete EquipmentUnit
routes.delete(
  '/:id',
  verifyToken,
  deletedAt,
  validate,
  equipmentQuantityTypeController.deleteEquipmentQuantityType
);

module.exports = routes;
