// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const licenceController = require('../controllers/licence.controller');

// Validator
const validator = require('../validators/licence.validator');

// create licence
routes.post(
  '',
  verifyToken,
  validator.licenceValidationRule(),
  validate,
  licenceController.createLicence
);

// get all licence
routes.get('', verifyToken, validate, licenceController.getAllLicence);

module.exports = routes;
