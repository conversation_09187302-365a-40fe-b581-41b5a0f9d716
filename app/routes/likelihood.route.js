// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const likelihoodController = require('../controllers/likelihood.controller');

// get all likelihood
routes.get('', verifyToken, validate, likelihoodController.getAllLikelihood);

module.exports = routes;
