// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount, deletedAt } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// Validator
const validator = require('../validators/profile-function.validator');

// controller
const profileFunctionController = require('../controllers/profile-function.controller');

// create profile function
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.profileFunctionValidationRule(),
  validate,
  profileFunctionController.createProfileFunction
);

// get all profile function
routes.get('', verifyToken, authAccount, validate, profileFunctionController.getAllProfileFunction);

// update profile function
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.updateProfileFunctionValidationRule(),
  validate,
  profileFunctionController.updateProfileFunction
);

// delete profile function
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  deletedAt,
  validate,
  profileFunctionController.deleteProfileFunction
);

module.exports = routes;
