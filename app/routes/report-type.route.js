const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const reportController = require('../controllers/report-type.controller');

routes.get('', verifyToken, authAccount, validate, reportController.filterReports);

module.exports = routes;
