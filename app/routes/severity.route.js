// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const severityController = require('../controllers/severity.controller');

// get all severity
routes.get('', verifyToken, validate, severityController.getAllSeverity);

module.exports = routes;
