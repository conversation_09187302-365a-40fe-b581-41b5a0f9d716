// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/manage-report.validator');

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const manageReportController = require('../controllers/manage-report.controller');

// Create Report
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.createReportValidationRule(),
  validate,
  manageReportController.createReport
);

// Get Report
routes.get('', verifyToken, authAccount, validate, manageReportController.getReports);

// Get Printable Report Titles
routes.get(
  '/get-printable-report-titles/:reportId',
  verifyToken,
  authAccount,
  validate,
  manageReportController.getPrintableReportTitles
);

routes.get(
  '/project-progress-calculation',
  verifyToken,
  authAccount,
  validate,
  manageReportController.getProjectWiseReportCalculation
);

// Get Report By Id
routes.get('/:id', verifyToken, authAccount, validate, manageReportController.getReportById);

// Update Report
routes.patch('/:id', verifyToken, authAccount, validate, manageReportController.updateReport);

// Delete Report
routes.delete('/:id', verifyToken, authAccount, validate, manageReportController.softDeleteReport);

routes.get(
  '/config/new-form',
  verifyToken,
  authAccount,
  validate,
  manageReportController.getReportQuestionConfig
);

// Project tracker
routes.get(
  '/project-tracker/:projectId',
  verifyToken,
  authAccount,
  validate,
  manageReportController.getProjectTracker
);

// Project tracker export excel
routes.get(
  '/project-tracker/export-excel/:projectId',
  verifyToken,
  authAccount,
  validate,
  manageReportController.getProjectTrackerExcel
);

routes.get(
  '/detailed-progress/:dprId',
  verifyToken,
  authAccount,
  manageReportController.reportsDetailedProgress
);

module.exports = routes;
