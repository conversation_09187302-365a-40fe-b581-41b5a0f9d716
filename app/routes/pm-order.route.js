// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// Validator
const validator = require('../validators/pm-order.validator');

// controller
const pmOrderController = require('../controllers/pm-order.controller');

routes.post(
  '/add-to-queue',
  verifyToken,
  authAccount,
  validator.createPMOrderValidationRule(),
  validate,
  pmOrderController.addToQueuePMOrder
);

routes.patch(
  '/request/:pmOrderId',
  verifyToken,
  authAccount,
  validate,
  pmOrderController.pmOrderRequest
);

routes.get('', verifyToken, authAccount, validate, pmOrderController.getPMOrders);
routes.get('/scan-equipment', verifyToken, authAccount, validate, pmOrderController.scanEquipment);
routes.post(
  '/manage-inventory',
  verifyToken,
  authAccount,
  validate,
  pmOrderController.manageInventory
);

routes.patch(
  '/order-details/:pmOrderManageId',
  verifyToken,
  authAccount,
  validate,
  pmOrderController.updatePMOrderManageEquipment
);

routes.get(
  '/order-status-count',
  verifyToken,
  authAccount,
  validate,
  pmOrderController.getOrderStatusCount
);

routes.get(
  '/equipment-order-history/:equipment',
  verifyToken,
  authAccount,
  validate,
  pmOrderController.getEquipmentOrderHistory
);

// Get Linked Equipment List On PM Check-in
routes.get(
  '/linked-equipment/:manageId',
  verifyToken,
  authAccount,
  validate,
  pmOrderController.getLinkedEquipmentList
);

// Scan Linked Equipment In PM Check-in
routes.get(
  '/linked-equipment/:manageId/:qrCode',
  verifyToken,
  authAccount,
  validate,
  pmOrderController.scanLinkedEquipmentInPMCheckIn
);

// Update Linked Equipment In PM Check-in
routes.patch(
  '/update-linked-check-in/:historyId',
  verifyToken,
  authAccount,
  validator.validateUpdateLinkedEquipment(),
  validate,
  pmOrderController.updateLinkedCheckInOrder
);

routes.patch(
  '/shopping-cart/update-quantity/:pmManageOrderId',
  verifyToken,
  authAccount,
  validate,
  pmOrderController.updateRequestQuantityFromShoppingCart
);

// get project wise pm orders
routes.get('/projects', verifyToken, authAccount, validate, pmOrderController.getPMOrdersProjects);

// get pm order details
routes.get('/projects/:id', verifyToken, authAccount, validate, pmOrderController.getPMOrdersList);

module.exports = routes;
