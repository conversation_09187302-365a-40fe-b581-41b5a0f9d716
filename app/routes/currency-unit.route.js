// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
  updatedBy,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const {
  createCurrencyUnitValidationRule,
  updateCurrencyUnitValidationRule,
} = require('../validators/currency-unit.validator');

// controller
const currencyUnitController = require('../controllers/currency-unit.controller');

//Create
routes.post(
  '',
  verifyToken,
  authAccount,
  createCurrencyUnitValidationRule(),
  validate,
  defaultCreatedDetails,
  currencyUnitController.createCurrencyUnit
);

// Get CurrencyUnit
routes.get('', verifyToken, authAccount, validate, currencyUnitController.getCurrencyUnit);

// Update CurrencyUnit
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  updateCurrencyUnitValidationRule(),
  validate,
  updatedBy,
  currencyUnitController.updateCurrencyUnit
);

// Delete CurrencyUnit
routes.delete('/:id', verifyToken, deletedAt, validate, currencyUnitController.deleteCurrencyUnit);

module.exports = routes;
