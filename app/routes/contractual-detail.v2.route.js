const express = require('express');
const routes = express.Router();

const contractualDetailController = require('../controllers/contractual-detail.controller');

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');

// Create
routes.post('', verifyToken, contractualDetailController.create);

routes.put('/:id', verifyToken, contractualDetailController.updateDetail);

module.exports = routes;
