// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
  updatedBy,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const certicateTypeController = require('../controllers/certificate-type.controller');

//Create certificate type
routes.post(
  '',
  verifyToken,
  authAccount,
  validate,
  defaultCreatedDetails,
  certicateTypeController.createCertificateType
);

// Get certificate type
routes.get('', verifyToken, authAccount, validate, certicateTypeController.getCertificateType);

// Get certificate type by Id
routes.get('/:id', verifyToken, validate, certicateTypeController.getCertificateTypeById);

// Update certificate type
routes.patch(
  '/:id',
  verifyToken,
  validate,
  updatedBy,
  certicateTypeController.updateCertificateType
);

// Delete certificate type
routes.delete(
  '/:id',
  verifyToken,
  deletedAt,
  validate,
  certicateTypeController.deleteCertificateType
);

module.exports = routes;
