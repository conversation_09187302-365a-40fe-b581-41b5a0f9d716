// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const configController = require('../controllers/config.controller');

// config file
routes.get('/config', verifyToken, validate, configController.createCongifFile);
routes.get('/equipment', verifyToken, validate, configController.createEquipmentConfigFile);
routes.get('/report', verifyToken, validate, configController.getReportConfigFile);
routes.get('/toolbox-talk', verifyToken, validate, configController.getToolboxTalkConfigFile);

module.exports = routes;
