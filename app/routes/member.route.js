// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const memberController = require('../controllers/member.controller');

//create single memeber
routes.post(
  '/single-member',
  verifyToken,
  authAccount,
  validate,
  memberController.createSingleMember
);

// get all members
routes.get('', verifyToken, authAccount, validate, memberController.getAllMember);

// update member
routes.patch('/:id', verifyToken, authAccount, validate, memberController.updateMember);

// delete member
routes.delete('/:id', verifyToken, authAccount, deletedAt, validate, memberController.deleteMember);

module.exports = routes;
