// library
const express = require('express');
const routes = express.Router();

// controller
const teamMemberController = require('../controllers/team-member.controller');

// validator
const validator = require('../validators/team-member.validator');

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

routes.patch(
  '/:id',
  verifyToken,
  validator.updateTeamMemberValidationRule(),
  validate,
  teamMemberController.updateTeamMemberV2
);

module.exports = routes;
