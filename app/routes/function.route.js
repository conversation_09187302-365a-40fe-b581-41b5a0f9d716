// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const functionController = require('../controllers/function.controller');

// Validator
const validator = require('../validators/function.validator');

// create function
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.functionValidationRule(),
  validate,
  functionController.createFunction
);

// update function
routes.patch(
  '/:id',
  verifyToken,
  validator.functionValidationRule(),
  validate,
  functionController.updateFunction
);

// delete function
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  deletedAt,
  validate,
  functionController.deleteFunction
);

module.exports = routes;
