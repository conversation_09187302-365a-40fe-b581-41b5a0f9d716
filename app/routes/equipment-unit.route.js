// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
  updatedBy,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const { createEquipmentUnitValidationRule } = require('../validators/equipment-unit.validator');

// controller
const equipmentUnitController = require('../controllers/equipment-unit.controller');

//Create
routes.post(
  '',
  verifyToken,
  authAccount,
  createEquipmentUnitValidationRule(),
  validate,
  defaultCreatedDetails,
  equipmentUnitController.createEquipmentUnit
);

// Get EquipmentUnit
routes.get('', verifyToken, authAccount, validate, equipmentUnitController.getEquipmentUnit);

// Update EquipmentUnit
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  updatedBy,
  equipmentUnitController.updateEquipmentUnit
);

// Delete EquipmentUnit
routes.delete(
  '/:id',
  verifyToken,
  deletedAt,
  validate,
  equipmentUnitController.deleteEquipmentUnit
);

module.exports = routes;
