// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const roleAgreementController = require('../controllers/role-agreement.controller');

// Validator
const validator = require('../validators/role-agreement.validator');

/** Assing Role Agreement */
routes.post(
  '',
  verifyToken,
  validator.roleAgreementValidationRule(),
  validate,
  roleAgreementController.assignRoleAgreement
);

/** Get Role Agreement By Role */
routes.get('/:role', verifyToken, validate, roleAgreementController.getRoleAgreementByRole);

module.exports = routes;
