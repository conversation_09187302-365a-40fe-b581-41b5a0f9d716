// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// Validator
const validator = require('../validators/shopping-cart.validator');

// controller
const shoppingCartController = require('../controllers/shopping-cart.controller');

// create shopping cart
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.shoppingCartValidationRule(),
  validate,
  shoppingCartController.createShoppingCart
);

// get shopping cart list
routes.get(
  '/:id',
  verifyToken,
  validator.validateParamIds(),
  validate,
  authAccount,
  shoppingCartController.getShoppingCartList
);

routes.patch(
  '/:id',
  verifyToken,
  validator.validateParamIds(),
  validate,
  authAccount,
  shoppingCartController.rejectShoppingCart
);

module.exports = routes;
