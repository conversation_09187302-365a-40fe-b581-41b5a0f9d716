// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, defaultCreatedDetails } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const {
  createEquipmentImageCertificateValidationRule,
} = require('../validators/equipment-image-certificate.validator');

// controller
const equipmentImageCertificateController = require('../controllers/equipment-image-certificate.controller');

//Create
routes.post(
  '',
  verifyToken,
  createEquipmentImageCertificateValidationRule(),
  validate,
  defaultCreatedDetails,
  equipmentImageCertificateController.createEquipmentImageCertificate
);

module.exports = routes;
