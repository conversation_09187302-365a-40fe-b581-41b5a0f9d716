const express = require('express');
const routes = express.Router();

const { validate } = require('../middlewares/validate.middleware');
const contractualDetailController = require('../controllers/contractual-detail.controller');

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');

// validation
const validator = require('../validators/contractual-detail.validator');

// Create
routes.post(
  '',
  verifyToken,
  validator.filedValidationRule(),
  validate,
  contractualDetailController.create
);

// Update
routes.put(
  '/:id',
  verifyToken,
  validator.updatefiledValidationRule(),
  validate,
  contractualDetailController.updateDetail
);

// Update file data
routes.patch(
  '/:id/files/:fileId',
  verifyToken,
  validate,
  contractualDetailController.updateContractFileData
);

// Remove the file from collection
routes.patch('/:id', verifyToken, validate, contractualDetailController.removeFile);

module.exports = routes;
