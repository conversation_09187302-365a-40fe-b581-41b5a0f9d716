// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const { createUserValidationRule } = require('../validators/user.validator');

// controller
const accountController = require('../controllers/account.controller');

//create
routes.post('', createUserValidationRule(), validate, accountController.createAccount);

//get list
routes.get('', verifyToken, validate, accountController.getAdminUsers);

//get AccountLicence by account
routes.get(
  '/licence',
  verifyToken,
  authAccount,
  validate,
  accountController.getAccountLicenceByAccount
);

// update account
routes.patch('/:id', verifyToken, accountController.updateAccount);

//get AccountLicence by account id
routes.get('/:id/licence', verifyToken, validate, accountController.getAccountLicenceByAccountId);

module.exports = routes;
