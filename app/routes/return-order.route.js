// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// validator
const validator = require('../validators/return-order.validator');

// controller
const returnOrderController = require('../controllers/return-order.controller');

// get project return order
routes.get(
  '/pm-return-order/projects',
  verifyToken,
  authAccount,
  validate,
  returnOrderController.getProjectReturnOrderList
);

// get return order details
routes.get(
  '/pm-return-order/projects/:id',
  verifyToken,
  authAccount,
  validate,
  returnOrderController.getReturnOrderListByProject
);

routes.patch(
  '/reject-equipment',
  verifyToken,
  authAccount,
  validate,
  returnOrderController.wmRejectMissingEquipment
);
routes.get('', verifyToken, authAccount, validate, returnOrderController.getPMOrderEquipment);

routes.get(
  '/equipment-history/:projectId',
  verifyToken,
  authAccount,
  validate,
  returnOrderController.getReturnOrderEquipment
);

routes.post('', verifyToken, authAccount, validate, returnOrderController.returnEquipmentOrder);

routes.patch(
  '/:returnOrder',
  verifyToken,
  authAccount,
  validate,
  returnOrderController.updateReturnOrder
);

routes.post(
  '/pre-checkout',
  verifyToken,
  authAccount,
  validate,
  returnOrderController.preCheckoutEquipment
);

routes.delete(
  '/remove-equipment/:id',
  verifyToken,
  authAccount,
  validator.validateParamIds(),
  validate,
  returnOrderController.removeReturnOrderEquipment
);

routes.get(
  '/:projectId/:qrCode',
  verifyToken,
  authAccount,
  validator.validateParamIds(),
  validate,
  returnOrderController.getEquipmentDataByQR
);

routes.get(
  '/pm-return-order',
  verifyToken,
  authAccount,
  validate,
  returnOrderController.getPMReturnOrder
);

module.exports = routes;
