// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken } = require('../middlewares/auth.middleware');
const { verifyFile, verifyBulkFiles } = require('../middlewares/upload.middleware');

// controller
const fileUploadController = require('../controllers/file-upload.controller');

// file upload
routes.post('/upload', verifyToken, verifyFile, fileUploadController.fileUpload);

routes.post(
  '/multiple-upload',
  verifyToken,
  verifyBulkFiles,
  fileUploadController.multipleFileUpload
);

// Download Excel
routes.get('/download-excel/:fileName', verifyToken, fileUploadController.downloadSampleFile);

module.exports = routes;
