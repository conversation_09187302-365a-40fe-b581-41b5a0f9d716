const express = require('express');
const routes = express.Router();
const validator = require('../validators/safety-card.validator');
const safetyCard = require('../controllers/safety-card.controller');
// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

routes.get('/:dprId/summary', verifyToken, authAccount, validate, safetyCard.safetyCardsSummary);
routes.post(
  '',
  verifyToken,
  authAccount,
  validate,
  validator.safetyCardValidationRule(),
  validate,
  safetyCard.createSafetyCard
);
routes.get('/count', verifyToken, authAccount, validate, safetyCard.qhscCardCount);
routes.get(
  '/risk-of-incident-count',
  verifyToken,
  authAccount,
  validate,
  safetyCard.qhscCardRiskOfIncidentCount
);
routes.get(
  '/type-of-incident-count',
  verifyToken,
  authAccount,
  validate,
  safetyCard.qhscCardTypeOfIncidentCount
);
routes.get('/form/:id/:cardtype', verifyToken, authAccount, validate, safetyCard.getSafetyCardform);
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.updateSafetyCardValidationRule(),
  validate,
  safetyCard.updateSafetyCard
);
routes.get('', verifyToken, authAccount, validate, safetyCard.getAllSafetyCard);
routes.delete('/:id', verifyToken, authAccount, deletedAt, safetyCard.deleteSafetyCard);
routes.get('/export', verifyToken, authAccount, validate, safetyCard.exportSafetyCards);
routes.get(
  '/:safetyCardId/export-pdf',
  verifyToken,
  authAccount,
  validate,
  safetyCard.getSafetyCardPDFDetails
);
routes.get('/:id', safetyCard.getSafetyCardById);

module.exports = routes;
