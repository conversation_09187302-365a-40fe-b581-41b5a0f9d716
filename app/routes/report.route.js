// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/report.validator');

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const reportController = require('../controllers/report.controller');

// Create Report
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.reportValidationRule(),
  validate,
  reportController.createReport
);

// Get All Reports & Filter Reports
routes.get('', verifyToken, authAccount, validate, reportController.filterReportData);

// export report
routes.get('/:id/export', verifyToken, validate, reportController.exportReport);

// Get Report By Id
routes.get('/:id', verifyToken, validate, reportController.getDataByReportId);

// Insert report params in details
routes.patch('/:id/details', verifyToken, validate, reportController.addReportParams);

// Update Report Param Detail
routes.patch('/:id/details/:detailId', verifyToken, validate, reportController.updateReportParams);

// Update Report Param Detail
routes.patch('/:id', verifyToken, validate, reportController.updateReport);

// Remove report detail
routes.delete('/:id/details/:detailId', verifyToken, validate, reportController.removeReportDetail);

// Remove report
routes.delete('/:id', verifyToken, validate, reportController.deleteReport);

module.exports = routes;
