// library
const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const projectController = require('../controllers/project.controller');

// Validator
const validator = require('../validators/project.validator');

// create projects
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.projectValidationRule(),
  validate,
  projectController.createProject
);

// get all projects
routes.get('', verifyToken, authAccount, validate, projectController.getAllProjects);
routes.get(
  '/get-projects-by-approver',
  verifyToken,
  authAccount,
  validate,
  projectController.getProjectsByApprover
);

routes.get(
  '/project-matrix',
  verifyToken,
  authAccount,
  validate,
  projectController.getAllProjectsDetails
);

// update projects
routes.patch('/:id', verifyToken, authAccount, validate, projectController.updateProject);

// delete project
routes.delete(
  '/:id',
  verifyToken,
  deletedAt,
  authAccount,
  validate,
  projectController.deleteProject
);

// get locations by project
routes.get(
  '/:id/locations',
  verifyToken,
  authAccount,
  validate,
  projectController.getLocationsByProjectId
);

// get members by project
routes.get(
  '/:id/members',
  verifyToken,
  authAccount,
  validate,
  projectController.getMembersByProjectId
);

// get functions by project
routes.get(
  '/:id/functions',
  verifyToken,
  authAccount,
  validate,
  projectController.getFunctionsByProjectId
);

// get assets by project
routes.get(
  '/:id/assets',
  verifyToken,
  authAccount,
  validate,
  projectController.getAssetsByProjectId
);

// get project strings by project
routes.get(
  '/:id/project-strings',
  verifyToken,
  authAccount,
  validate,
  projectController.getProjectStringsByProjectId
);

// get scopes by project
routes.get(
  '/:id/scopes',
  verifyToken,
  authAccount,
  validate,
  projectController.getScopeByProjectId
);

// get activities by project
routes.get(
  '/:id/activities',
  verifyToken,
  authAccount,
  validate,
  projectController.getActivityByProjectId
);

// get teams by project
routes.get('/:id/teams', verifyToken, authAccount, validate, projectController.getTeamsByProjectId);

routes.get(
  '/:id/locations-linked-with-assets',
  verifyToken,
  authAccount,
  validate,
  projectController.getLocationAssetDataByProjectId
);

// get training matrix export excel
routes.post(
  '/training-matrix/export-excel',
  verifyToken,
  authAccount,
  validate,
  projectController.getTrainingMatrixExcel
);

module.exports = routes;
