const express = require('express');
const routes = express.Router();

const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
} = require('../middlewares/auth.middleware');

const validator = require('../validators/ce-norms.validator');

const { validate } = require('../middlewares/validate.middleware');

// controller
const ceNormsController = require('../controllers/ce-norms.controller');

// create Ce Norms
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.createCeNormsValidationRule(),
  validate,
  defaultCreatedDetails,
  ceNormsController.createCeNorms
);

// update Ce Norms
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.validateParamIds(),
  validate,
  ceNormsController.updateCeNorms
);

// get Ce Norms
routes.get('', verifyToken, authAccount, validate, ceNormsController.getCeNorms);

// delete Ce Norms
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  validator.validateParamIds(),
  validate,
  deletedAt,
  ceNormsController.deleteCeNorms
);

module.exports = routes;
