// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/team.validator');

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const teamController = require('../controllers/team.controller');

// create team
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.teamValidationRule(),
  validate,
  teamController.createTeam
);

// update team
routes.patch(
  '/:id',
  validator.updateTeamValidationRule(),
  verifyToken,
  validate,
  teamController.updateTeam
);

// delete team
routes.delete('/:id', verifyToken, deletedAt, validate, teamController.deleteTeam);

module.exports = routes;
