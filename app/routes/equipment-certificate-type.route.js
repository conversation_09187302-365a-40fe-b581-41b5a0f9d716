// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
  updatedBy,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const {
  createEquipmentCertificateTypeValidationRule,
} = require('../validators/equipment-certificate-type.validator');

// controller
const equipmentCerticateTypeController = require('../controllers/equipment-certificate-type.controller');

//Create
routes.post(
  '',
  verifyToken,
  authAccount,
  createEquipmentCertificateTypeValidationRule(),
  validate,
  defaultCreatedDetails,
  equipmentCerticateTypeController.createEquipmentCertificateType
);

// Get EquipmentType
routes.get(
  '',
  verifyToken,
  authAccount,
  validate,
  equipmentCerticateTypeController.getEquipmentCertificateType
);

// Update EquipmentType
routes.patch(
  '/:id',
  verifyToken,
  validate,
  updatedBy,
  equipmentCerticateTypeController.updateEquipmentCertificateType
);

// Delete EquipmentType
routes.delete(
  '/:id',
  verifyToken,
  deletedAt,
  validate,
  equipmentCerticateTypeController.deleteEquipmentCertificateType
);

module.exports = routes;
