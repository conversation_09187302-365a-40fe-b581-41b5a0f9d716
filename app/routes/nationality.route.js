const express = require('express');
const routes = express.Router();

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const nationalityController = require('../controllers/nationality.controller');

//create nationality
routes.post('', verifyToken, authAccount, validate, nationalityController.createNationality);

// get all nationalities
routes.get('', verifyToken, authAccount, validate, nationalityController.getNationality);

module.exports = routes;
