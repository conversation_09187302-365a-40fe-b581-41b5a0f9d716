const express = require('express');
const routes = express.Router();

const { validate } = require('../middlewares/validate.middleware');
const userController = require('../controllers/user.controller');

// middleware
const { verifyToken, deletedAt, authAccount } = require('../middlewares/auth.middleware');

// Validator
const validator = require('../validators/user.validator');

routes.post(
  '',
  verifyToken,
  authAccount,
  validator.createUserValidationRule(),
  validate,
  userController.create
);
routes.get('', verifyToken, authAccount, validate, userController.getAllUsers);
routes.get('/profile', verifyToken, validate, userController.getUserProfile);
routes.delete('/:id', verifyToken, deletedAt, validate, userController.deleteUser);
routes.get('/:id', verifyToken, validate, userController.getUserById);
routes.patch(
  '/change-status',
  validator.updateUserStatusValidationRule(),
  verifyToken,
  validate,
  userController.changeStatus
);
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.updateUserValidationRule(),
  validate,
  userController.updateUser
);

// submit user rating
routes.patch(
  '/user-rating/:id',
  verifyToken,
  authAccount,
  validator.submitUserRatingValidationRule(),
  validate,
  userController.submitUserRating
);

routes.delete(
  '/:ratingId/delete-user-rating/:id',
  verifyToken,
  authAccount,
  validate,
  userController.deleteUserRating
);

/** user licence seeder */
module.exports = routes;
