// library
const express = require('express');
const routes = express.Router();

// middleware
const {
  verifyToken,
  authAccount,
  deletedAt,
  defaultCreatedDetails,
  updatedBy,
} = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');
const {
  createEquipmentCategoryValidationRule,
  updateEquipmentCategoryValidationRule,
} = require('../validators/equipment-category.validator');

// controller
const equipmentCategoryController = require('../controllers/equipment-category.controller');

//Create
routes.post(
  '',
  verifyToken,
  authAccount,
  createEquipmentCategoryValidationRule(),
  validate,
  defaultCreatedDetails,
  equipmentCategoryController.createEquipmentCategory
);

// Get EquipmentCategory
routes.get(
  '',
  verifyToken,
  authAccount,
  validate,
  equipmentCategoryController.getEquipmentCategory
);

// Update EquipmentCategory
routes.patch(
  '/:id',
  verifyToken,
  updateEquipmentCategoryValidationRule(),
  validate,
  updatedBy,
  equipmentCategoryController.updateEquipmentCategory
);

// Delete EquipmentCategory
routes.delete(
  '/:id',
  verifyToken,
  deletedAt,
  validate,
  equipmentCategoryController.deleteEquipmentCategory
);

module.exports = routes;
