const ContractualDetails = require('../models/contractual-detail.model');

/**
 * Add isInsuranceArrangedByReynard field in contractual details if it doesn't exist
 *
 * @returns
 */
exports.up = async () => {
  try {
    const result = await ContractualDetails.updateMany(
      { isInsuranceArrangedByReynard: { $exists: false } },
      { $set: { isInsuranceArrangedByReynard: true } }
    );

    console.log(
      `Successfully added isInsuranceArrangedByReynard field to ${result.modifiedCount} records`
    );
    return true;
  } catch (error) {
    console.error('Error in add-insurance-arranged-by-reynard seeder', error);
  }
};

/**
 * Rollback the migration
 *
 * @returns
 */
exports.down = async () => {
  try {
    const result = await ContractualDetails.updateMany(
      {
        isInsuranceArrangedByReynard: { $exists: true },
      },
      { $unset: { isInsuranceArrangedByReynard: 1 } }
    );

    console.log(`Successfully rollback ${result.modifiedCount} records`);
  } catch (error) {
    console.error('Error in add-insurance-arranged-by-reynard down seeder', error);
  }
};
