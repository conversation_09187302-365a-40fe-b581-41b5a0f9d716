/* models */
const User = require('../models/user.model');
const Role = require('../models/role.model');
const RoleAgreement = require('../models/role-agreement.model');
const AccountLicence = require('../models/account-licence.model');

/**
 * Prepare and insert the role-agreement data in collection
 *
 * @returns
 */
exports.up = async () => {
  //check the role-agreement exist
  const dataCount = await RoleAgreement.find().countDocuments();

  if (dataCount > 0) {
    return true;
  }

  // get the superadmin id
  const superAdmin = await User.find({ role: { $in: ['superadmin'] } });
  let defaultUserId = superAdmin[0]._id;

  const adminUser = await User.find({ email: '<EMAIL>' });
  let account = adminUser[0].account;

  const roles = await Role.find({ deletedAt: null });

  // prepare the role-agreement data collection
  for (let element of roles) {
    let accountLicences = await AccountLicence.find({
      account: element.account,
      isApproved: true,
      isRejected: false,
    });

    for (let ele of accountLicences) {
      await RoleAgreement.create({
        role: element._id,
        accountLicence: ele._id,
        account,
        createdBy: defaultUserId,
        updatedBy: defaultUserId,
      });
    }
  }
};
