/** Services */
const { licenceData } = require('../utils/licence-permission.utils');
const licenceService = require('../services/licence.service');
const permissionService = require('../services/permission.service');

/**
 * Seeder to add new role and permission
 */
exports.up = async () => {
  for (const data of licenceData) {
    const existingLicence = await licenceService.getLicencesByFilter({ name: data.licence });
    let createLicence = existingLicence;

    if (!existingLicence) {
      createLicence = await licenceService.createLicence({
        name: data.licence,
        createdAt: new Date(),
        updatedAt: new Date(),
        __v: 0,
      });

      console.log(`Created Licence:-> ${createLicence.name}`);
    }

    // Insert permissions associated with the licence
    for (const permissionName of data.permissions) {
      const existingPermission = await permissionService.getPermissionByFilter({
        name: permissionName,
      });

      if (!existingPermission) {
        let createdPermission = await permissionService.createPermission({
          name: permissionName,
          licence: createLicence._id,
          createdAt: new Date(),
          updatedAt: new Date(),
          __v: 0,
        });

        console.log(`Created Permission:-> ${createdPermission.name}`);
      }
    }
  }
};
