const User = require('../models/user.model');

/**
 * Update travelTimeToAirport and travelTimeToSecondAirport from Number → String
 */
exports.up = async () => {
  try {
    const result = await User.updateMany(
      {
        $expr: {
          $or: [
            { $in: [{ $type: '$travelTimeToAirport' }, ['int', 'double']] },
            { $in: [{ $type: '$travelTimeToSecondAirport' }, ['int', 'double']] },
          ],
        },
      },
      [
        {
          $set: {
            travelTimeToAirport: {
              $cond: [
                { $in: [{ $type: '$travelTimeToAirport' }, ['int', 'double']] },
                { $toString: '$travelTimeToAirport' },
                '$travelTimeToAirport',
              ],
            },
            travelTimeToSecondAirport: {
              $cond: [
                { $in: [{ $type: '$travelTimeToSecondAirport' }, ['int', 'double']] },
                { $toString: '$travelTimeToSecondAirport' },
                '$travelTimeToSecondAirport',
              ],
            },
          },
        },
      ]
    );

    console.log(`Successfully converted travelTime fields in ${result.modifiedCount} users`);
  } catch (err) {
    console.error('Migration failed:', err);
  }
};

/**
 * Rollback: Convert travelTimeToAirport and travelTimeToSecondAirport back to Number
 */
exports.down = async () => {
  try {
    const result = await User.updateMany(
      {
        $expr: {
          $or: [
            { $eq: [{ $type: '$travelTimeToAirport' }, 'string'] },
            { $eq: [{ $type: '$travelTimeToSecondAirport' }, 'string'] },
          ],
        },
      },
      [
        {
          $set: {
            travelTimeToAirport: {
              $cond: [
                { $eq: [{ $type: '$travelTimeToAirport' }, 'string'] },
                { $toDouble: '$travelTimeToAirport' },
                '$travelTimeToAirport',
              ],
            },
            travelTimeToSecondAirport: {
              $cond: [
                { $eq: [{ $type: '$travelTimeToSecondAirport' }, 'string'] },
                { $toDouble: '$travelTimeToSecondAirport' },
                '$travelTimeToSecondAirport',
              ],
            },
          },
        },
      ]
    );

    console.log(`Successfully rolled back travelTime fields in ${result.modifiedCount} users`);
  } catch (err) {
    console.error('Rollback failed:', err);
  }
};
