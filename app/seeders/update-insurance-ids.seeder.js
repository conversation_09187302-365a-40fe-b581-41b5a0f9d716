/* models */
const ContractualDetail = require('../models/contractual-detail.model');
const constantUtils = require('../utils/constants.utils');

/**
 * Update existing contractual details documents:
 * - Add healthInsuranceId and liabilityInsuranceId as empty strings at the root level
 *
 * @returns
 */
exports.up = async () => {
  try {
    console.log(constantUtils.START_PROCESSING);

    // Find all contractual details documents
    const contractualDetails = await ContractualDetail.find({});
    console.log(`Found ${contractualDetails.length} contractual details documents`);

    let updatedCount = 0;

    // Update each document
    for (const doc of contractualDetails) {
      // Convert Mongoose document to plain JavaScript object
      const docObj = doc.toObject();

      const updateData = {};
      let needsUpdate = false;

      // Check if healthInsuranceId doesn't exist or is undefined
      if (!docObj.healthInsuranceId) {
        updateData.healthInsuranceId = '';
        needsUpdate = true;
      }

      // Check if liabilityInsuranceId doesn't exist or is undefined
      if (!docObj.liabilityInsuranceId) {
        updateData.liabilityInsuranceId = '';
        needsUpdate = true;
      }

      // Only update if there are fields to update
      if (needsUpdate) {
        // Update the document
        await ContractualDetail.findByIdAndUpdate(doc._id, { $set: updateData });
        updatedCount++;
      }
    }

    console.log(`Successfully updated ${updatedCount} contractual details documents`);
    console.log(constantUtils.END_PROCESS);
    return true;
  } catch (error) {
    console.error('Error updating insurance fields:', error);
    return false;
  }
};
