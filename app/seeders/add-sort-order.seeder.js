/* models */
const Permission = require('../models/permission.model');

/**
 * Add Sort Order
 */
exports.up = async () => {
  const permissions = await Permission.find().sort({ _id: 1 });

  const bulkOps = permissions.map((permission, index) => ({
    updateOne: {
      filter: { _id: permission._id },
      update: { $set: { sortOrder: index + 1 } },
    },
  }));

  if (bulkOps.length > 0) {
    await Permission.bulkWrite(bulkOps);
    console.log(`Updated ${bulkOps.length} permissions with sortOrder.`);
  } else {
    console.log('No permissions found to update.');
  }
};
