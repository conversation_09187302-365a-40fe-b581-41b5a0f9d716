/* models */
const VersionInfo = require('../models/version-info.model');

// Utils
const commonUtils = require('../utils/common.utils');

/* Command to run script
-> npm run seeder version-info
*/

/**
 * Prepare and insert version-info data in collection
 *
 * @returns
 */
exports.up = async () => {
  //check the collection exist
  const collectionExist = await commonUtils.checkCollectionExists('version-info');
  if (collectionExist) {
    return true;
  }

  // prepare data
  let insertData = [
    {
      platform: 'Android',
      versionCode: 1,
      versionName: '1.0.0',
      packageName: 'com.package.name',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      platform: 'iOS',
      versionCode: 1,
      versionName: '1.0.0',
      packageName: 'com.package.name',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  // insert the pre-define data
  return await VersionInfo.insertMany(insertData);
};
