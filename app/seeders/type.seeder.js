/*models*/
const Type = require('../models/type.model');

/**
 * Define the replacement mapping
 * Keys are the old titles, values are the new titles
 */
const replacementMap = {
  'Occupational Injury': 'Injury / Illness',
  'Occupational Health / Illness': 'Damage / Breakdown / Loss',
  'Property Damage': 'Spill / Pollution / Emission',
  'Property Breakdown / Malfunction': 'Breach of law / Contract',
  'Property Loss / Missing': 'Other non-work events',
};

/**
 * Define the final desired state of types
 */
const desiredTitles = [
  { title: 'Near Miss', color: 'CBD7E5' },
  { title: 'Injury / Illness', color: 'F6CDCE' },
  { title: 'Damage / Breakdown / Loss', color: 'F7BB80' },
  { title: 'Spill / Pollution / Emission', color: 'ADD4D1' },
  { title: 'Breach of law / Contract', color: '9BC795' },
  { title: 'Other non-work events', color: 'F4DF91' },
];

/**
 * Prepare and update the types data in collection
 *
 * @returns
 */
exports.up = async () => {
  console.log('Starting type collection update...');

  // Track changes for reporting
  const changes = {
    updated: 0,
    added: 0,
    deleted: 0,
  };

  // Get current state of the database
  const existingTypes = await Type.find({});
  console.log(`Found ${existingTypes.length} existing types in the db`);

  // Process replacements - update existing records based on replacement map
  for (const existingType of existingTypes) {
    const oldTitle = existingType.title;

    // Check if this title needs to be replaced
    if (replacementMap[oldTitle]) {
      const newTitle = replacementMap[oldTitle];

      // Find the other data for the new title from desiredTitles
      const newTypeInfo = desiredTitles.find(type => type.title === newTitle);

      if (newTypeInfo) {
        await Type.updateOne(
          { _id: existingType._id },
          { $set: { title: newTitle, color: newTypeInfo.color } }
        );

        changes.updated++;
        console.log(
          `Updated record with ID ${existingType._id} from "${oldTitle}" to "${newTitle}"`
        );
      }
    }
  }

  // Add any new types from desiredTitles that don't exist yet
  const updatedTypes = await Type.find({});
  const existingTitles = updatedTypes.map(type => type.title);

  for (const desired of desiredTitles) {
    // Skip if this title already exists
    if (!existingTitles.includes(desired.title)) {
      const newType = await Type.create({
        title: desired.title,
        color: desired.color,
      });

      changes.added++;
      console.log(`Added new type: "${desired.title}" with ID ${newType._id}`);
    }
  }

  // Delete any types that are not in desiredTitles
  for (const existingType of updatedTypes) {
    if (!desiredTitles.find(type => type.title === existingType.title)) {
      await Type.deleteOne({ _id: existingType._id });

      changes.deleted++;
      console.log(`Deleted type: "${existingType.title}" with ID ${existingType._id}`);
    }
  }

  // Report summary
  console.log(
    `Type collection update completed: ${changes.updated} records updated, ${changes.added} new types added, ${changes.deleted} types deleted.`
  );

  return changes;
};
