const AWS = require('aws-sdk');
const sharp = require('sharp');
const { BUCKET_ENDPOINT, DO_SPACES_NAME, SPACES_SECRET, SPACES_KEY } = process.env;
const constantUtils = require('../utils/constants.utils');

const s3 = new AWS.S3({
  endpoint: new AWS.Endpoint(BUCKET_ENDPOINT),
  accessKeyId: SPACES_KEY,
  secretAccessKey: SPACES_SECRET,
});

// Models
const IconUrl = require('../models/icon-url.model');

/* Command to run script
-> npm run seeder migrate-compress-img Report_Answers 1(optional) 100(optional)
    Discription:- 
      migrate-compress-img(required) = seeder file name
      Report_Answers(required) = Bucket folder name
      1 = start index (iconurls table => index field)
      100 = end index (iconurls table => index field)
*/

/**
 * Process to migrate-compress-img
 *
 * @returns
 */
exports.up = async args => {
  try {
    console.log(constantUtils.START_PROCESSING);

    const folderPath = args[3] ?? '';

    const startIndex = parseInt(args[4]) ?? '';
    const endIndex = parseInt(args[5]) ?? '';

    if (folderPath === '') {
      console.log(constantUtils.NO_FOLDER_PATH);
      return false;
    }

    let filter = {
      iconUrl: { $regex: `/${folderPath}/`, $options: 'i' },
    };

    let sortData = { createdAt: 1 };

    if (!Number.isNaN(startIndex) && !Number.isNaN(endIndex)) {
      if (startIndex > endIndex) {
        console.log(constantUtils.START_INDEX_SHOULD_BE_LESS);
        return false;
      }

      filter = { ...filter, index: { $gte: startIndex, $lte: endIndex } };
      sortData = { index: 1 };
    }

    const fetchIconUrl = await IconUrl.find(filter, { _id: 1, iconUrl: 1 }).sort(sortData);

    console.log('Fetch data from DB: ', fetchIconUrl.length);

    let counter = 0;

    for (const result of fetchIconUrl) {
      const url = result.iconUrl;
      const regex = new RegExp(`images/(${folderPath})/(.+)$`);
      const match = regex.exec(url);
      if (match) {
        const bucketPath = `images/${match[1]}`;
        const fileName = match[2];
        await this.processImage(bucketPath, fileName);
        counter++;
      }
    }

    console.log(constantUtils.END_PROCESS);
    console.log(`Successfully processed images: ${counter}`);
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Function to fetch image from DigitalOcean Spaces
 * @param {string} Key - The key (path) of the file in the bucket
 * @returns {Buffer} - The image file in buffer format
 */
exports.fetchImageFromSpace = async Key => {
  const params = {
    Bucket: DO_SPACES_NAME,
    Key,
  };
  const data = await s3.getObject(params).promise();
  return data.Body; // returns the image buffer
};

/**
 * Function to compress the image using sharp
 * @param {Buffer} imageBuffer - The image buffer to compress
 * @returns {Buffer} - The compressed image buffer
 */
exports.compressImage = async imageBuffer => {
  return sharp(imageBuffer)
    .resize(600, 600) // Resize and make transparent background
    .toBuffer();
};

/**
 * Function to upload image to DigitalOcean Spaces
 * @param {string} Key - The key (path) where the file will be saved
 * @param {Buffer} Body - The file buffer to upload
 * @param {string} ContentType - The MIME type of the file
 */
exports.uploadToSpace = async (Key, Body, ContentType) => {
  const params = {
    Bucket: DO_SPACES_NAME,
    Key,
    Body,
    ACL: 'public-read', // Public access
    ContentType,
  };

  await s3.putObject(params).promise();
  console.log(`Image uploaded to ${Key}`);
};

/**
 * Main function to fetch, compress and re-upload the image
 * @param {string} filePath - The path of the image in the bucket
 */
exports.processImage = async (bucketPath, fileName) => {
  try {
    const filePath = `${bucketPath}/${fileName}`;

    console.log(`Fetching image from path: ${filePath}`);

    // 1. Fetch the image from the bucket
    const imageBuffer = await this.fetchImageFromSpace(filePath);

    // 2. Compress the image
    const compressedImageBuffer = await this.compressImage(imageBuffer);

    // 3. Get the content type (MIME type) based on file extension
    const contentType = filePath.endsWith('.png') ? 'image/png' : 'image/jpeg';

    // 4. Upload the compressed image to a new path (e.g., 'compressed/' folder)
    const newKeyPath = `${bucketPath}/compressed/${fileName}`; // Save in a 'compressed/' folder
    await this.uploadToSpace(newKeyPath, compressedImageBuffer, contentType);

    console.log(constantUtils.IMAGE_PROCESS_COMPLETED);
  } catch (error) {
    console.error('Error processing image:', error);
  }
};
