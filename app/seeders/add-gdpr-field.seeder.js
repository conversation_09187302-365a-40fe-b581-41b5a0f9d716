const User = require('../models/user.model');

/**
 * /Add gdpr fileds to user table
 *
 * @returns
 */
exports.up = async () => {
  try {
    const result = await User.updateMany(
      { gdprAcknowledged: { $exists: false } },
      { $set: { gdprAcknowledged: false, gdprAcknowledgedAt: null } }
    );

    console.log(`Successfully added gdpr fields to ${result.modifiedCount} users`);
  } catch (error) {
    console.error(error);
  }
};

/**
 * Rollback the migration
 *
 * @returns
 */
exports.down = async () => {
  try {
    const result = await User.updateMany(
      { gdprAcknowledged: { $exists: true } },
      { $unset: { gdprAcknowledged: 1, gdprAcknowledgedAt: 1 } }
    );

    console.log(`Successfully removed gdpr fields from ${result.modifiedCount} users`);
  } catch (error) {
    console.error(error);
  }
};
