const mongoose = require('mongoose');
require('dotenv').config();
require('../utils/global-constants.utils');
const URL = require('../../db-connection-url.js');
const constantsUtils = require('../utils/constants.utils.js');

// DB Connection Start
exports.defaultDBConnection = async () => {
  mongoose.set('strictQuery', false);
  mongoose.Promise = global.Promise;
  return mongoose
    .connect(URL)
    .then(() => 'DB CONNECTED SUCCESSFULLY...')
    .catch(err => console.log(err));
};

/** Define seeders */
const seeders = async () => {
  try {
    const seeder = process.argv[2];
    const connectDB = await this.defaultDBConnection();
    const allArgs = process.argv;

    if (connectDB) {
      console.log(connectDB);

      /** Execute seeders */
      const result = await require(`./${seeder}.seeder`).up(allArgs);
      if (result) {
        console.log(constantsUtils.SEEDER_SUCCESS);
      }
      mongoose
        .disconnect()
        .then(() => console.log('DB DISCONNECTED...'))
        .catch(err => console.log(err));
    }
  } catch (error) {
    console.log(error);
  }
};

seeders();
