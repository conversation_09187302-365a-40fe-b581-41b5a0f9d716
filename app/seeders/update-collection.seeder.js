/* Command to run script
-> npm run seeder update-collection -- report-question reportQuestion
    Discription:- 
      update-collection(required) = seeder file name
      report-question(required) = model name
      reportQuestion(required) = method/function name
*/

const constantsUtils = require('../utils/constants.utils');

/**
 * Get model
 *
 * @param {*} model
 * @returns
 */
exports.getModel = async model => {
  try {
    return require(`../models/${model}.model`);
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Process to update collection
 *
 * @returns
 */
exports.up = async args => {
  try {
    const ModelName = args[3] ?? '';
    const MethodName = args[4] ?? '';
    const FieldName = args[5] ?? '';
    const FieldDefaultValue = args[6] ?? '';

    if (ModelName === '') {
      console.log(constantsUtils.MODEL_NAME_NOT_PROVIDED);
      return false;
    }

    if (MethodName === '') {
      console.log(constantsUtils.METHOD_NAME_NOT_PROVIDED);
      return false;
    }
    // Models
    const ModelFile = await this.getModel(ModelName);

    console.log(constantsUtils.START_PROCESSING);

    await this[MethodName](ModelFile, FieldName, FieldDefaultValue);

    console.log(constantsUtils.END_PROCESS);
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Update Report Question
 *
 * @param {*} model
 * @returns
 */
exports.reportQuestion = async model => {
  try {
    const filter = {
      $and: [
        { supportedContent: { $exists: true } },
        { supportedContent: { $ne: [] } },
        { supportedContent: { $elemMatch: { sortOrder: { $exists: false } } } },
      ],
      deletedAt: null,
    };

    const getQuestions = await model.find(filter);
    if (getQuestions.length > 0) {
      console.log(getQuestions.length);
      for (let question of getQuestions) {
        let counter = 1;
        let contentLength = question.supportedContent.length;
        for (let key in question.supportedContent) {
          if (counter > contentLength) {
            return true;
          } else {
            await model.findByIdAndUpdate(question._id, {
              $set: { [`supportedContent.${key}.sortOrder`]: counter },
            });
            counter++;
          }
        }
      }
    }
    return true;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Update Report Question Answer
 *
 * @param {*} model
 * @returns
 */
exports.reportQuestionAnswer = async model => {
  try {
    await model.updateMany({ sortOrder: { $exists: false } }, { $set: { sortOrder: 0 } });
    return true;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Delete User Report Answers On Deleted User Report
 *
 * @param {*} model
 * @returns
 */
exports.deleteUserReportAnswersOnDeletedUserReport = async model => {
  try {
    const userReports = await model.find(
      {
        deletedAt: { $ne: null },
      },
      { _id: 1, deletedBy: 1 }
    );

    const userReportAnswerModel = await this.getModel('user-report-answer');

    let userReportAnswerIds = [];
    for (let userReport of userReports) {
      let filter = { userReport: userReport._id, deletedAt: null };

      // Found User Report Answers
      const userReportAnswers = await userReportAnswerModel.find(filter, { _id: 1 });
      userReportAnswerIds = userReportAnswerIds.concat(userReportAnswers.map(x => x._id));

      // Delete User Report Answers
      await userReportAnswerModel.updateMany(filter, {
        $set: { deletedAt: Date.now(), deletedBy: userReport.deletedBy },
      });
    }

    console.log(`Deleted user report answer ids: ${userReportAnswerIds}`);
    console.log(`${userReportAnswerIds.length} user report answers deleted successfully`);
    return true;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Create User Project Report and Update User Report
 *
 * @param {*} model
 * @returns
 */
exports.updateUserReportByUserProjectReport = async model => {
  try {
    const getUserReports = await model.aggregate([
      {
        $match: {
          userProjectReport: {
            $exists: false,
          },
          deletedAt: null,
        },
      },
      {
        $group: {
          _id: {
            project: '$project',
            report: '$report',
            location: '$location',
            asset: {
              $ifNull: ['$asset', ''],
            },
          },
          userReportIds: {
            $push: '$_id',
          },
          createdBy: {
            $first: '$createdBy',
          },
          account: { $first: '$account' },
        },
      },
    ]);

    let counter1 = 0;
    let counter2 = 0;
    for (let userReport of getUserReports) {
      const userProjectReportModel = await this.getModel('user-project-report');
      const fetchUserProjectReport = await userProjectReportModel.findOne(userReport._id);
      let userProjectReportId = fetchUserProjectReport ? fetchUserProjectReport._id : null;
      if (!fetchUserProjectReport) {
        let userProjectReport = {
          ...userReport._id,
          account: userReport.account,
          createdBy: userReport.createdBy,
          createdAt: new Date(),
        };
        const createdUserProjectReport = await userProjectReportModel.create(userProjectReport);

        userProjectReportId = createdUserProjectReport._id;
        counter1++;
      }

      for (let userReportId of userReport.userReportIds) {
        await model.findByIdAndUpdate(userReportId, {
          $set: { userProjectReport: userProjectReportId },
        });
        counter2++;
      }
    }

    console.log(`${counter1} user project reports created successfully`);
    console.log(`${counter2} user reports updated successfully`);
    return true;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Add isPrintable default values in User Report Answer from Report Question Answer
 *
 * @param {*} model
 * @returns
 */
exports.addIsPrintableInUserReportAnswerTable = async model => {
  try {
    const reportQuestionAnswers = await model.find({
      'title.isPrintable': { $exists: true },
      deletedAt: null,
    });
    let counter = 1;
    if (reportQuestionAnswers.length > 0) {
      const userReportAnswerModel = await this.getModel('user-report-answer');
      for (let reportQuestionAnswer of reportQuestionAnswers) {
        let getUserReportAnswers = await userReportAnswerModel.find({
          reportQuestion: reportQuestionAnswer.reportQuestion,
          reportQuestionAnswer: reportQuestionAnswer._id,
          answers: { $ne: [] },
          'answers.isPrintable': { $exists: false },
          deletedAt: null,
        });

        if (getUserReportAnswers.length > 0) {
          for (let userReportAnswer of getUserReportAnswers) {
            for (let key in reportQuestionAnswer.title) {
              await userReportAnswerModel.updateOne(
                {
                  _id: userReportAnswer._id,
                  'answers.answerTitleId': reportQuestionAnswer.title[key]._id,
                },
                {
                  $set: {
                    'answers.$.isPrintable': reportQuestionAnswer.title[key].isPrintable,
                  },
                }
              );
            }
            counter++;
          }
        }
      }
    }

    console.log(`${counter} user report answers updated successfully`);
    return true;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Add field in collection
 * example command:-> npm run seeder update-collection -- certificate-type addFieldInCollection internal false
 * certificate-type -> model name
 * addFieldInCollection -> method name
 * internal -> field name
 * false -> default value
 *
 * @param {*} model
 * @param {*} field
 * @param {*} defaultValue
 * @returns
 */
exports.addFieldInCollection = async (model, field, defaultValue) => {
  try {
    if (!field || !defaultValue) {
      console.log(constantsUtils.NO_FIELD_OR_DEFAULT_PROVIDED);
      return false;
    }
    const getCollection = await model.find({
      [field]: { $exists: false },
      deletedAt: null,
    });

    let counter = 0;
    if (getCollection.length > 0) {
      for (let collection of getCollection) {
        await model.updateOne({ _id: collection._id }, { $set: { [field]: defaultValue } });
        counter++;
      }
    }

    console.log(`${counter} records updated successfully`);
    return true;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Remove field from collection
 * example command:-> npm run seeder update-collection -- certificate-type removeFieldFromCollection internal
 * certificate-type -> model name
 * removeFieldFromCollection -> method name
 * internal -> field name
 *
 * @param {*} model
 * @param {*} field
 * @returns
 */
exports.removeFieldFromCollection = async (model, field) => {
  try {
    if (!field) {
      console.log(constantsUtils.FIELD_NOT_PROVIDED);
      return false;
    }

    const result = await model.updateMany(
      {
        [field]: { $exists: true },
      },
      {
        $unset: { [field]: 1 },
      },
      { strict: false }
    );

    console.log(`${field} has been removed successfully from ${result.modifiedCount} records`);
    return true;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * update user drivingLicence field from boolean to array
 *
 * @param {*} model
 * @returns
 */
exports.updateDrivingLiecneceField = async model => {
  try {
    const result = await model.updateMany(
      { drivingLicence: { $type: 'bool' } },
      {
        $set: {
          drivingLicence: [],
        },
      }
    );
    console.log(`${result.modifiedCount} documents updated.`);
    return true;
  } catch (error) {
    console.error('Error updating drivingLicence field:', error);
    return false;
  }
};

/**
 * update user report and user project report asset field from array of values to array of objects
 *
 * @param {*} model
 * @returns
 */
exports.updateUserReportAssetField = async model => {
  try {
    const result = await model.updateMany(
      { asset: { $exists: true } }, // Filters documents where the 'asset' field exists
      [
        {
          $set: {
            asset: {
              $map: {
                input: '$asset', // The array of asset ObjectIds
                as: 'asset',
                in: {
                  _id: '$$asset', // The original asset ObjectId becomes the new _id
                  asset: '$$asset', // The same ObjectId is stored in the asset field
                },
              },
            },
          },
        },
      ]
    );
    console.log(`${result.modifiedCount} documents updated.`);
    return true;
  } catch (error) {
    console.error('Error updating reportAsset field:', error);
    return false;
  }
};
