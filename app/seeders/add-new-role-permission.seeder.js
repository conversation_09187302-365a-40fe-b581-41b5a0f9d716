/** Services */
const { licenceData } = require('../utils/licence-permission.utils');
const licenceService = require('../services/licence.service');
const permissionService = require('../services/permission.service');

/**
 * Seeder to add new role and permission
 */
exports.up = async () => {
  for (const data of licenceData) {
    let existingLicence = await licenceService.getLicencesByFilter({ name: data.licence });

    if (!existingLicence) {
      existingLicence = await licenceService.createLicence({
        name: data.licence,
        createdAt: new Date(),
        updatedAt: new Date(),
        __v: 0,
      });

      console.log(`Created Licence:-> ${existingLicence.name}`);
    }

    // Insert permissions associated with the licence
    for (const permissionName of data.permissions) {
      const existingPermission = await permissionService.getPermissionByFilter({
        name: permissionName,
      });

      if (!existingPermission) {
        let createdPermission = await permissionService.createPermission({
          name: permissionName,
          licence: existingLicence._id,
          createdAt: new Date(),
          updatedAt: new Date(),
          __v: 0,
        });

        console.log(`Created Permission:-> ${createdPermission.name}`);
      }
    }
  }
};
