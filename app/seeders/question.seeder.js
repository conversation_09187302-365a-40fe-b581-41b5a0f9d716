/* models */
const User = require('../models/user.model');
const Question = require('../models/question.model');

/* Service */
const QuestionService = require('../services/question.service');

/**
 * Prepare and insert the questions data in collection
 *
 * @returns
 */
exports.up = async () => {
  //check the questions exist
  const questionCount = await QuestionService.questionsCount({ deletedAt: null });

  if (questionCount > 0) {
    return true;
  }

  // get the superadmin id
  const superAdmin = await User.find({ role: { $in: ['superadmin'] } });
  let defaultUserId = superAdmin[0]._id;

  const adminUser = await User.find({ email: '<EMAIL>' });
  let account = adminUser[0].account;

  // prepare the questions data collection
  let questions = [
    {
      title: 'Shortage of breath, chest complaints, e.g. bronchitis or asthma?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Previous heart complaints/surgery/angina pectoris?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Fits/fainting/dizziness?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Joint problems?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Depression/anxiety?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Back/neck problems?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Fits/blackouts/epilepsy?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'High blood pressure?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Stomach/bowel trouble?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Allergies?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Surgical operations?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Any significant disease or infenction e.g. diabetes, ear infection etc.?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
    {
      title: 'Are you taking any medication, pills or injections?',
      isPublished: true,
      account,
      createdBy: defaultUserId,
      createdAt: new Date(),
    },
  ];

  // insert the pre-define questions data
  await Question.insertMany(questions);
};
