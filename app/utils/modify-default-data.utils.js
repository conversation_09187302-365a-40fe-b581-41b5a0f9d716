const teamService = require('../services/team.service');
const memberService = require('../services/member.service');
const functionService = require('../services/function.service');
const teamMemberService = require('../services/team-member.service');
const scopeService = require('../services/scope.service');
const locationService = require('../services/location.service');
const activityService = require('../services/activity.service');
const assetService = require('../services/asset.service');
const projectStringService = require('../services/project-string.service');
const commonUtils = require('../utils/common.utils');

/**
 * Default Team
 *
 * @param {*} req
 * @returns
 */
exports.getDefaultTeam = async req => {
  if (!commonUtils.isValidId(req.body.team)) {
    const exist = await teamService.getTeamByName(
      req.userData.account,
      req.body.project,
      req.body.team,
      true
    );
    if (exist) {
      return exist._id;
    } else {
      const newReq = {
        teamsWfmName: req.body.team,
        project: req.body.project,
        account: req.userData.account,
        isActive: true,
        isDefault: true,
      };
      const createdTeam = await teamService.createTeam(newReq);
      return commonUtils.toObjectId(createdTeam._id);
    }
  } else {
    return req.body.team;
  }
};

/**
 * Create Default Member
 *
 * @param {*} req
 */
exports.getDefaultMember = async (member, project, account) => {
  let functionName = await functionService.getDefaultFunction();
  const memberIds = [];

  for (const userId of member) {
    const member = await memberService.createMember({
      project,
      function: functionName._id,
      user: userId,
      account: account,
    });
    memberIds.push(member._id);
  }

  return memberIds;
};

/**
 * Create Default Member
 *
 * @param {*} req
 */
exports.createDefaultMember = async (member, project, account) => {
  let functionName = await functionService.getDefaultFunction();
  await teamMemberService.createTeamMember({ project, functionName });

  const createdMember = await memberService.createMember({
    project: project,
    function: functionName._id,
    user: member,
    account,
  });

  return createdMember;
};

/**
 * Create Default Function
 *
 * @param {*} req
 */
exports.createDefaultFunction = async (functionName, project, account) => {
  if (!commonUtils.isValidId(functionName)) {
    const filter = {
      functionName,
      account,
      project,
      isDefault: true,
    };
    const exist = await functionService.getFunctionByName(filter);
    if (exist) {
      return exist._id;
    } else {
      const newReq = {
        functionName,
        project,
        account,
        isDefault: true,
      };
      const createdFunction = await functionService.createFunction(newReq);
      return commonUtils.toObjectId(createdFunction._id);
    }
  } else {
    return functionName;
  }
};

/**
 * Create Default Activity
 *
 * @param {*} activity
 * @param {*} account
 * @param {*} project
 * @returns
 */
exports.createDefaultActivity = async (activity, account, project) => {
  if (!commonUtils.isValidId(activity)) {
    const filter = {
      name: activity,
      project,
      account,
      isDefault: true,
    };
    const exist = await activityService.getActivityByProjectIdAndName(filter);

    if (exist) {
      return exist._id;
    } else {
      const cable = await scopeService.getDefaultScope({
        account,
        project,
        defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
      });

      const newReq = {
        name: activity,
        scope: cable._id,
        weight: global.constant.DEFAULT_WEIGHT,
        project: project,
        account,
        isDefault: true,
      };

      const createdActivity = await activityService.createActivity(newReq);
      return commonUtils.toObjectId(createdActivity._id);
    }
  } else {
    return activity;
  }
};

/**
 * Get Pre Created Location In Database
 *
 * @param {*} req
 * @returns
 */
exports.createDefaultlocation = async req => {
  if (!commonUtils.isValidId(req.body.location)) {
    let reqData = req.body;

    let project = reqData.project;
    let title = reqData.location;
    const locationData = {
      title: reqData.location,
      project: project,
      account: req.userData.account,
      isDefault: true,
    };
    const filterData = {
      title,
      project,
      account: req.userData.account.toString(),
      isDefault: true,
    };
    const exist = await locationService.getLocationByName(filterData);

    if (exist) {
      return exist._id;
    } else {
      const createdLocation = await locationService.createLocation(locationData);
      return commonUtils.toObjectId(createdLocation._id);
    }
  } else {
    return req.body.location;
  }
};

/**
 * Create Default Asset
 *
 * @param {*} req
 * @returns
 */
exports.createDefaultAsset = async req => {
  let { cable, project } = req.body;
  const filterData = {
    account: req.userData.account,
    project,
    defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
    deletedAt: null,
    isDefault: true,
  };

  const string = await projectStringService.getDefaultProjectString(filterData);
  if (!commonUtils.isValidId(cable)) {
    let { id } = string;
    const filter = {
      cableName: cable,
      string: id,
      project,
      account: req.userData.account.toString(),
      deletedAt: null,
      isDefault: true,
    };
    const exist = await assetService.getAssetByName(filter);

    if (exist) {
      return exist._id;
    } else {
      const filter = {
        account: req.userData.account,
        project,
        isDefault: true,
      };
      const defaultLocation = await locationService.getDefaultLocation(filter);

      const newReq = {
        project: project,
        fromLocation: defaultLocation[0]._id,
        toLocation: defaultLocation[1]._id,
        manufacturer: process.env.DEFAULT_MANUFACTURER,
        typeMm2: process.env.DEFAULT_TYPEMM2,
        string: string,
        cableName: cable,
        isDefault: true,
        account: req.userData.account.toString(),
      };
      const createdAsset = await assetService.createAsset(newReq);
      return commonUtils.toObjectId(createdAsset._id);
    }
  } else {
    return cable;
  }
};

/**
 * Change Shift Activity Default Data
 *
 * @param {*} modifiedData
 * @param {*} data
 * @param {*} req
 * @returns
 */
exports.changeShiftActivity = async (modifiedData, data, req) => {
  // other project
  // activity
  if (data.project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
    modifiedData.activity = await this.createDefaultActivity(
      modifiedData.activity,
      req.userData.account,
      data.project._id
    );
  }
  // activity
  // location
  if (data.project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
    req.body.project = data.project._id;
    modifiedData.location = await this.createSyncDefaultlocation(modifiedData, data, req);
  }
  // location
  // end other project

  return modifiedData;
};

/**
 * Get Sync Default Team
 *
 * @param {*} req
 * @param {*} account
 * @returns
 */
exports.getSyncDefaultTeam = async (req, account) => {
  if (!commonUtils.isValidId(req.team)) {
    const exist = await teamService.getTeamByName(account, req.project, req.team, true);
    if (exist) {
      return exist._id;
    } else {
      const newReq = {
        teamsWfmName: req.team,
        project: req.project,
        account: account,
        isActive: true,
        isDefault: true,
      };
      const createdTeam = await teamService.createTeam(newReq);
      return commonUtils.toObjectId(createdTeam._id);
    }
  } else {
    return req.team;
  }
};

/**
 * Create Sync Default Asset
 *
 * @param {*} modifiedData
 * @param {*} data
 * @param {*} req
 * @returns
 */
exports.createSyncDefaultAsset = async (modifiedData, data, req) => {
  let { cable } = modifiedData;
  const filterData = {
    account: req.userData.account,
    project: data.project._id,
    defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
    deletedAt: null,
    isDefault: true,
  };

  const string = await projectStringService.getDefaultProjectString(filterData);
  if (!commonUtils.isValidId(cable)) {
    const filter = {
      cableName: cable,
      string: string._id,
      project: data.project._id,
      account: req.userData.account.toString(),
      deletedAt: null,
      isDefault: true,
    };
    const exist = await assetService.getAssetByName(filter);

    if (exist) {
      return exist._id;
    } else {
      const filter = {
        account: req.userData.account,
        project: data.project._id,
        isDefault: true,
      };
      const defaultLocation = await locationService.getDefaultLocation(filter);
      const newReq = {
        project: data.project._id,
        fromLocation: defaultLocation[0]._id,
        toLocation: defaultLocation[1]._id,
        manufacturer: process.env.DEFAULT_MANUFACTURER,
        typeMm2: process.env.DEFAULT_TYPEMM2,
        string: string._id,
        cableName: cable,
        isDefault: true,
        account: req.userData.account.toString(),
      };
      const createdAsset = await assetService.createAsset(newReq);
      return commonUtils.toObjectId(createdAsset._id);
    }
  } else {
    return cable;
  }
};

/**
 * Create Sync Default Location
 *
 * @param {*} modifiedData
 * @param {*} data
 * @param {*} req
 * @returns
 */
exports.createSyncDefaultlocation = async (modifiedData, data, req) => {
  let project = data.project._id;
  let title = modifiedData.location;
  const locationData = {
    title: modifiedData.location,
    project: data.project._id,
    account: req.userData.account,
    isDefault: true,
  };
  const filterData = {
    title,
    project,
    account: req.userData.account.toString(),
    isDefault: true,
  };
  const exist = await locationService.getLocationByName(filterData);

  if (exist) {
    return exist._id;
  } else {
    const createdLocation = await locationService.createLocation(locationData);
    return commonUtils.toObjectId(createdLocation._id);
  }
};

/**
 * Get Default Update Member
 *
 * @param {*} member
 * @param {*} project
 * @param {*} account
 * @returns
 */
exports.getDefaultUpdateMember = async (member, project, account) => {
  let functionName = await functionService.getDefaultFunction();
  const memberIds = [];
  for (const userId of member) {
    const filerData = {
      _id: commonUtils.toObjectId(userId),
      account: account,
    };
    let exist = await memberService.getMemberById(filerData);
    if (!exist) {
      const member = await memberService.createMember({
        project,
        function: functionName._id,
        user: userId,
        account: account,
      });
      memberIds.push(member._id);
    } else {
      memberIds.push(userId);
    }
  }

  return memberIds;
};
