const AWS = require('aws-sdk');
const sharp = require('sharp');
const { BUCKET_ENDPOINT, DO_SPACES_NAME, SPACES_SECRET, SPACES_KEY, CDN_URL } = process.env;
const EventEmitter = require('events'); // Import the EventEmitter class
// Create a new EventEmitter instance
const eventEmitter = new EventEmitter();

// Create an S3 instance
const spacesEndpoint = new AWS.Endpoint(BUCKET_ENDPOINT);
const s3 = new AWS.S3({
  endpoint: spacesEndpoint,
  accessKeyId: SPACES_KEY,
  secretAccessKey: SPACES_SECRET,
});

let mimeTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/webp'];

/**
 * Upload image single
 *
 * @param {*} fileData
 * @returns
 */
exports.uploadImage = async fileData => {
  let { originalname, mimetype, buffer, type } = fileData;

  // Define the S3 paths for original and compressed images
  const originalKeyPath = `images/${type}/${originalname}`;

  const s3Object = s3
    .putObject({
      Bucket: DO_SPACES_NAME,
      Key: originalKeyPath,
      Body: buffer,
      ACL: 'public-read',
      ContentEncoding: 'base64',
      ContentType: mimetype,
    })
    .promise();

  return s3Object
    .then(() => {
      // call single compressed image event
      if (mimeTypes.includes(mimetype)) {
        eventEmitter.once('imageminToolForCompressed', imageCompressionHandler);
        eventEmitter.emit('imageminToolForCompressed', fileData);
      }
      return {
        iconUrl: `${CDN_URL}/${type}/${originalname}`,
      };
    })
    .catch(err => {
      throw new Error(err);
    });
};

// event for single compressed image
const imageCompressionHandler = async fileData => {
  try {
    let { originalname, buffer, mimetype, type } = fileData;

    // Compress the image using sharp
    const compressedBuffer = await this.compressImage(buffer, mimetype);

    const compressedKeyPath = `images/${type}/compressed/${originalname}`;

    const s3CompressObj = await s3
      .putObject({
        Bucket: DO_SPACES_NAME,
        Key: compressedKeyPath,
        Body: compressedBuffer, // Upload compressed buffer
        ACL: 'public-read',
        ContentType: mimetype,
      })
      .promise()
      .then(() => {
        return 'compressed image uploaded successfully';
      })
      .catch(err => {
        throw new Error(err);
      });

    console.log('event end..', s3CompressObj);
    eventEmitter.emit('eventEnd', s3CompressObj);
  } catch (err) {
    throw new Error(err);
  }
};

/**
 * Upload multiple files
 *
 * @param {*} files
 * @returns
 */
exports.uploadMultipleFiles = async files => {
  let prepareFilePaths = [];
  let prepareCompressFiles = [];
  const uploadPromises = files.map(file => {
    // prepare compress files
    if (mimeTypes.includes(file.mimetype)) {
      prepareCompressFiles.push(file);
    }

    let { originalname, mimetype, buffer, type, ...rest } = file;

    const originalKeyPath = `images/${type}/${originalname}`;

    prepareFilePaths.push({
      uploadName: rest.uploadName,
      iconUrl: `${CDN_URL}/${type}/${originalname}`,
      size: rest.size,
    });

    return s3
      .putObject({
        Bucket: DO_SPACES_NAME,
        Key: originalKeyPath,
        Body: buffer,
        ACL: 'public-read',
        ContentEncoding: 'base64',
        ContentType: mimetype,
      })
      .promise();
  });

  // Wait for all uploads to finish
  try {
    // eslint-disable-next-line no-undef
    const results = await Promise.all(uploadPromises)
      .then(() => {
        // call event for compress files
        if (prepareCompressFiles.length === files.length) {
          eventEmitter.once('imageminCompressedForBulk', bulkImageCompressionHandler);
          eventEmitter.emit('imageminCompressedForBulk', prepareCompressFiles);
        }
        return prepareFilePaths;
      })
      .catch(err => {
        throw new Error(err);
      });
    return results;
  } catch (error) {
    console.error('Error uploading files:', error);
    throw error;
  }
};

// Compress bulk files (called once per emit)
const bulkImageCompressionHandler = async files => {
  try {
    console.log('event start..');
    const uploadPromises = files.map(async file => {
      let { originalname, buffer, mimetype, type } = file;

      const compressedBuffer = await this.compressImage(buffer, mimetype);

      const compressedKeyPath = `images/${type}/compressed/${originalname}`;

      return await s3
        .putObject({
          Bucket: DO_SPACES_NAME,
          Key: compressedKeyPath,
          Body: compressedBuffer,
          ACL: 'public-read',
          ContentType: mimetype,
        })
        .promise();
    });

    // eslint-disable-next-line no-undef
    const results = await Promise.all(uploadPromises)
      .then(() => 'compressed images uploaded successfully')
      .catch(err => {
        throw new Error(err);
      });
    console.log(results);
    eventEmitter.emit('eventEnd', results);
  } catch (err) {
    console.log('event error', err);
    throw new Error(err);
  }
};

/**
 * compress image
 *
 * @param {*} buffer
 * @param {*} mimetype
 */
exports.compressImage = async (buffer, mimetype) => {
  return await sharp(buffer).toFormat(mimetype.split('/')[1], { quality: 60 }).toBuffer();
};
