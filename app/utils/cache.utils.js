// Simple in-memory cache utility for equipment data
class MemoryCache {
  constructor() {
    this.cache = new Map();
    this.ttl = new Map(); // Time to live for each key
  }

  /**
   * Set cache with TTL (Time To Live)
   * @param {string} key - Cache key
   * @param {*} value - Value to cache
   * @param {number} ttlSeconds - TTL in seconds (default: 300 = 5 minutes)
   */
  set(key, value, ttlSeconds = 300) {
    const expiryTime = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, value);
    this.ttl.set(key, expiryTime);
  }

  /**
   * Get cached value
   * @param {string} key - Cache key
   * @returns {*} Cached value or null if not found/expired
   */
  get(key) {
    const expiryTime = this.ttl.get(key);
    
    if (!expiryTime || Date.now() > expiryTime) {
      // Cache expired or doesn't exist
      this.delete(key);
      return null;
    }
    
    return this.cache.get(key);
  }

  /**
   * Delete cache entry
   * @param {string} key - Cache key
   */
  delete(key) {
    this.cache.delete(key);
    this.ttl.delete(key);
  }

  /**
   * Clear all cache
   */
  clear() {
    this.cache.clear();
    this.ttl.clear();
  }

  /**
   * Get cache size
   * @returns {number} Number of cached items
   */
  size() {
    return this.cache.size;
  }

  /**
   * Clean expired entries
   */
  cleanExpired() {
    const now = Date.now();
    for (const [key, expiryTime] of this.ttl.entries()) {
      if (now > expiryTime) {
        this.delete(key);
      }
    }
  }

  /**
   * Generate cache key for equipment search
   * @param {Object} filter - Search filter
   * @param {number} page - Page number
   * @param {number} perPage - Items per page
   * @param {Object} sort - Sort criteria
   * @returns {string} Cache key
   */
  generateEquipmentSearchKey(filter, page, perPage, sort) {
    const keyData = {
      filter: JSON.stringify(filter),
      page,
      perPage,
      sort: JSON.stringify(sort)
    };
    return `equipment_search_${Buffer.from(JSON.stringify(keyData)).toString('base64')}`;
  }
}

// Create singleton instance
const equipmentCache = new MemoryCache();

// Clean expired entries every 10 minutes
setInterval(() => {
  equipmentCache.cleanExpired();
}, 10 * 60 * 1000);

module.exports = {
  equipmentCache,
  MemoryCache
};
