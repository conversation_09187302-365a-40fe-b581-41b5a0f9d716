module.exports = {
  /* common */
  ONLY_ADMIN_ACCESS_TO_RESOURCES:
    // prettier-ignore
    // eslint-disable-next-line prettier/prettier
    'Opps! You don\'t have access to use this resource. Please contact admin',
  CONFIG_FILE: 'Config File Retrived Successfully',
  ERROR: 'Internal server error',
  NOT_ALLOWED_TO_LOGIN_FROM_MOBILE: 'User account is not registered',
  NOT_ALLOWED_TO_LOGIN_FROM_ADMINPANEL: 'Client account is not registered',
  TYPE_NOT_DEFINED: 'Type is not defined',
  ONLY_ALPHABETIC_NUMERIC_KEYWORD: 'Only Alpha numeric characters are allowed',
  NO_DATA_FOUND: 'No data found',
  ASSIGN_APPROVER: 'Please assign approver to the project',
  FAILED_ITEMS: 'Some items failed to process',
  DUPLICATE_QR_CODE: 'Duplicate QR code',
  INVALID_ID: 'Invalid id',
  FAILED_TO_PROCESS: 'Warehouse check-in process failed',
  DELETE_USER_RATING: 'You can only delete rating made by you',

  /**User */
  NO_USER: 'No user exist',
  USER_EXIST: 'User fetched successfully',
  USER_LOGIN: 'Login successfully.',
  USER_DELETED: 'User Deleted Successfully',
  USER_UPDATED: 'User Updated Successfully',
  ACCOUNT_UPDATED: 'Account Updated Successfully',
  INVALID_CALLINGNAME: 'Please enter valid usual first name',
  INVALID_FIRSTNAME: 'Please enter valid first name',
  INVALID_LASTNAME: 'Please enter valid last name',
  INVALID_PROFILE_FUNCTION: 'Please enter valid profile function',
  INVALID_EMAIL: 'Please enter valid email address',
  INVALID_TRAVEL_TIME_TO_AIRPORT: 'Please enter valid travel time to airport',
  PASSWORD_REGEX: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z\d@$.!%*#?&]/,
  INVALID_PASSWORD: 'Password should contain atleast one uppercase, one lowercase and one symbol',
  PASSWORD_MIN_LENGTH: 'Password length should be at least 6 characters',
  ALL_USER_LIST: 'User list was retrieved successfully',
  NO_ACCESS: 'You do not have access',
  STATUS_CHANGED: 'Status changed',
  RESTRICTED_EMAIL_UPDATE: 'Unauthorized.Please contact administrator',
  EMAIL_TAKEN: 'Email Already Taken',
  EMPTY_EMAIL: 'Email address cannot be empty',
  INVALID_CONTACT_NUMBER: 'Please enter a valid contact number',
  REQUIRED_CONTACT_NUMBER: 'Contact number is required',
  EMPTY_CONTACT_NUMBER: 'Contact number cannot be empty',
  EMPTY_EMERGENCY_CONTACT_NUMBER: 'Emergency contact number cannot be empty',
  INVALID_CONTACT_NUMBER_FORMAT: 'Invalid format',
  INVALID_EMERGENCY_CONTACT_NUMBER: 'Please enter a valid emergency contact number',
  SOMETHING_WENT_WRONG: 'Something went wrong! Please contact admin',
  EMAIL_ALREADY_EXIST: 'Email Id already exists',
  RESOURCE_NUMBER_ALREADY_EXIST: 'Resource number already exists',
  USER_NOT_REGISTERED: 'User is not registered.',
  UNAUTHORIZED_ROLE_REQUESTED: 'NOT AUTHORIZED',
  PASSWORD_LINK_EXPIRED: 'Reset password link is expired.',
  PASSWORD_RESET_LINK: 'Reset password link has been sent to your registered email id',
  PASWORD_DOES_NOT_MATCH: 'Your new-paswword should match with confirm-password',
  NO_PASSWORD_FIELD_EXISTS: 'Please contact admin to send reset password link via email',
  PASSWORD_RESET_SUCCESSFULLY: 'Password reset successfully...',
  PASSWORD_INCORRECT: 'Please enter correct username or password to login.',
  SEND_AUTHENTICATION_TOKEN: 'Please send authentication token.',
  LOGOUT_SUCCESS: 'Logout successfully.',
  RESET_TOKEN_EXPIRED: 'Token has been expired',
  TOKEN_EXIST: 'Token already exists',
  LIMIT_EXCEED: 'Cannot create more than 3 kin',
  MISSING_FIELDS: 'Missing fields',
  UPDATED_SUCCESSFULLY: 'DB Updated successfully',
  NO_RATING: 'Rating can not be empty',
  SUBMIT_RATING: 'Rating has been added successfully',
  RATING_DELETED: 'Rating has been deleted successfully',
  USER_CANNOT_ACCESS_USER_NOT_FOUND: 'User cannot access or no user exits',
  ENTER_VALID_NEW_PASSWORD: 'Please enter valid new password',
  ENTER_VALID_CONFIRM_PASSWORD: 'Please enter valid confirm password',
  NO_EMAIL: 'No email provided',
  NO_CONTACT_NUMBER: 'No contact number',
  NA: 'N/A',
  MOBILE_ONLY:
    'Your access permissions have changed. You can now log in only from a mobile device.',
  WEB_ONLY:
    'Your account has Web access only. Mobile access is restricted. Please contact your admin.',
  ACCOUNT_INACTIVE: 'Your account has been deactivated.',
  ROLE_INACTIVE: 'Your role has been deactivated.',

  // User kin
  USER_MAXIMUM_KIN: 'Maximum 3 kin can be added',
  USER_KIN_NAME_REQUIRED: 'Kin name must not be empty',
  USER_KIN_RELATIONSHIP_REQUIRED: 'Kin relationship must not be empty',
  USER_KIN_STREET_REQUIRED: 'Kin street must not be empty',
  USER_KIN_CITY_REQUIRED: 'Kin city must not be empty',
  USER_KIN_COUNTRY_REQUIRED: 'Kin country must not be empty',
  USER_KIN_ZIPCODE_REQUIRED: 'Kin zip must not be empty',
  USER_PASSPORT_NUMBER: 'Enter Passport/ID Number',

  /** Project */
  CREATE_PROJECT: 'Project has been created successfully',
  UPDATE_PROJECT: 'Project has been updated successfully',
  DELETE_PROJECT: 'Project has been deleted successfully',
  ALL_PROJECT: 'Project has been retrieved successfully',
  PROJECT_EXIST: 'Project already exist',
  NO_PROJECT: 'Project does not exist',
  SINGLE_PROJECT: 'Project fetched successfully',
  PROJECT_TRANING_MATRICS: 'Project traning matrics fetched successfully',
  INVALID_PROJECT_ID: 'Invalid project id',
  PROJECT_ID_REQUIRED: 'Project id is required',
  PROJECT_REQUIRED: 'Please add project',
  SELECT_VALID_PROJECT: 'Please select valid project',
  SELECT_VALID_PROJECT_NUMBER: 'Please select valid project number',
  INVALID_PROJECT_NUMBER: 'Project number should not be greater than 255 characters',
  INVALID_CLIENT: 'Please select valid client',
  INVALID_CLIENT_LENGTH: 'Client should not be greater than 255 characters',

  /**Site */
  CREATE_SITE: 'Site has been created successfully',
  UPDATE_SITE: 'Site has been update successfully',
  DELETE_SITE: 'Site has been delete successfully',
  ALL_SITE: 'Site has been retrieved successfully',
  SITE_EXIST: 'Site already exist',
  NO_SITE: 'Site does not exist',
  SINGLE_SITE: 'Site fetched successfully',

  /**Location */
  CREATE_LOCATION: 'Location has been created successfully',
  UPDATE_LOCATION: 'Location has been updated successfully',
  DELETE_LOCATION: 'Location has been deleted successfully',
  LIST_LOCATION: 'Location has been fetched successfully',
  LOCATION_EXIST: 'Location already exist',
  NO_LOCATION: 'Location does not exist',
  SINGLE_LOCATION: 'Location fetched successfully',
  INVALID_LOCATION_ID: 'Invalid location id',
  ERROR_LONGITUDE: 'Logitude value is not valid',
  ERROR_LATITUDE: 'Latitude value is not valid',
  LOCATION_REQUIRED: 'Please add location',

  /**Safety Cards */
  CREATE_SAFETY_CARD: 'Safety Card has been created successfully',
  SAFETY_CARD_UPDATE_FORM: 'Safety Card update form',
  SAFETY_CARD_UPDATE: 'Safety Card has been updated successfully',
  ALL_SAFETY_CARD_LIST: 'Safety Card list was retrieved successfully',
  SAFETY_CARD_BY_ID: 'Safety Card was retrieved by id successfully',
  DELETED_SAFETY_CARD: 'Safety Card deleted successfully',
  NO_SAFETY_CARD: 'No Safety Card',
  SAFETY_CARD_COUNT: 'Safety Card count retrieved successfully',
  SAFETY_CARD_RISK_OF_INCIDENT_COUNT: 'Safety Card risk of incident count retrieved successfully',
  INVALID_PROJECT_NAME: 'Please select valid project name',
  INVALID_PROJECT_LOCATION: 'please select valid project location',
  INVALID_TITLE_LENGTH: 'Title should not be greater than 25 characters',
  INVALID_CATEGORY_LENGTH: 'Category should not be greater than 25 characters',

  /**Fields */
  NO_FIELDS: 'No Field Found',
  DELETED_FIELDS: 'Field deleted successfully',
  UPDATED_FIELDS: 'Field updated successfully',
  CREATED_FIELDS: 'Field created successfully',
  DYNAMIC_FIELDS: 'Dynamic fields',
  FIELD_EXIST: 'Field already exist',

  /**Account */
  ALL_ACCOUNT: 'Account has been retrieved successfully',
  ACCOUNT_EXIST: 'Account already exist',
  CREATE_ACCOUNT: 'Account has been created successfully',

  /**Users */
  USER_CREATE: 'User has been created successfully',

  /**Severity */
  CREATE_SEVERITY: 'Severity has been created successfully',
  UPDATE_SEVERITY: 'Severity has been updated successfully',
  DELETE_SEVERITY: 'Severity has been updated successfully',
  ALL_SEVERITY_LIST: 'Severity list was retrieved successfully',
  NO_SEVERITY: 'Severity does not exist',
  SEVERITY_EXIST: 'Severity already exist',
  SINGLE_SEVERITY: 'Severity fetched successfully',

  /**Action */
  CREATE_ACTION: 'Action has been created successfully',
  UPDATE_ACTION: 'Action has been updated successfully',
  DELETE_ACTION: 'Action has been deleted successfully',
  ALL_ACTION_LIST: 'Action list was retrieved successfully',
  NO_ACTION: 'Action does not exist',
  ACTION_EXIST: 'Action already exist',
  SINGLE_ACTION: 'Action fetched successfully',

  /**Saving Rule */
  CREATE_SAVING_RULE: 'Saving rule has been created successfully',
  UPDATE_SAVING_RULE: 'Saving rule has been update successfully',
  DELETE_SAVING_RULE: 'Saving rule has been deleted successfully',
  ALL_SAVING_RULE: 'Saving rule has been retrieved successfully',
  SAVING_RULE_EXIST: 'Saving rule already exist',
  NO_SAVING_RULE: 'Saving rule does not exist',

  /**Likelihood */
  CREATE_LIKELIHOOD: 'Likelihood has been created successfully',
  UPDATE_LIKELIHOOD: 'Likelihood has been updated successfully',
  DELETE_LIKELIHOOD: 'Likelihood has been updated successfully',
  ALL_LIKELIHOOD_LIST: 'Likelihood list was retrieved successfully',
  NO_LIKELIHOOD: 'Likelihood does not exist',
  LIKELIHOOD_EXIST: 'Likelihood already exist',
  SINGLE_LIKELIHOOD: 'Likelihood fetched successfully',
  SELECT_VALID_TITLE: 'Please select valid title',

  /**Type */
  CREATE_TYPE: 'Type has been created successfully',
  UPDATE_TYPE: 'Type has been updated successfully',
  DELETE_TYPE: 'Type has been deleted successfully',
  ALL_TYPE_LIST: 'Type list was retrieved successfully',
  NO_TYPE: 'Type does not exist',
  TYPE_EXIST: 'Type already exist',
  SINGLE_TYPE: 'Type fetched successfully',

  /**Team */
  CREATE_TEAM: 'Team has been created successfully',
  UPDATE_TEAM: 'Team has been updated successfully',
  DELETE_TEAM: 'Team has been deleted successfully',
  ALL_TEAM_LIST: 'Team list was retrieved successfully',
  NO_TEAM: 'Team does not exist',
  TEAM_EXIST: 'Team already exist',
  SINGLE_TEAM: 'Team fetched successfully',

  /**Team Member */
  CREATE_TEAM_MEMBER: 'Team member has been created successfully',
  UPDATE_TEAM_MEMBER: 'Team member has been updated successfully',
  DELETE_TEAM_MEMBER: 'Team member has been deleted successfully',
  ALL_TEAM_MEMBER_LIST: 'Team member list was retrieved successfully',
  NO_TEAM_MEMBER: 'Team member does not exist',
  TEAM_MEMBER_SAME_FUNCTION_EXIST: 'Same member with same function already exists in this shift',
  SINGLE_TEAM_MEMBER: 'Team member fetched successfully',
  SHIFT_REQUIRED: 'Please select shift',
  FUNCTION_REQUIRED: 'Please select function',
  TEAM_MEMBER_PERSONNEL_SHOULD_BE_ARRAY: 'Personnel List Data should be an array',
  TEAM_MEMBER_PERSONNEL_INVALID_TYPE: 'Personnel List Data array must contain at least one object',
  TEAM_MEMBER_ID_REQUIRED:
    'teamMemberId is required for each personnel & teamMemberId must be a valid ID',
  TEAM_MEMBER_INVALID_STATUS: 'isWorking & status must be a boolean value when provided',
  TEAM_MEMBER_STATUS_REQUIRED: 'At least one of isWorking or status is required for each personnel',
  BATCH_UPDATE_TEAM_MEMBERS: 'Team members updated successfully',

  /**Shift */
  CREATE_SHIFT: 'Shift has been created successfully',
  UPDATE_SHIFT: 'Shift has been updated successfully',
  DELETE_SHIFT: 'Shift has been deleted successfully',
  ALL_SHIFT_LIST: 'Shift list was retrieved successfully',
  NO_SHIFT: 'Shift does not exist',
  SHIFT_EXIST: 'Shift already exist',
  SINGLE_SHIFT: 'Shift fetched successfully',
  PROJECT_SELECTION_REQUIRED: 'Please select project',
  MEMBER_SELECTION_REQUIRED: 'Please select members',
  START_DATE_REQUIRED: 'Please select start date',
  TEAM_SELECTION_REQUIRED: 'Please select team',

  /**Licence */
  CREATE_LICENCE: 'Licence has been created successfully',
  UPDATE_LICENCE: 'Licence has been updated successfully',
  DELETE_LICENCE: 'Licence has been deleted successfully',
  ALL_LICENCE_LIST: 'Licence list was retrieved successfully',
  NO_LICENCE: 'Licence does not exist',
  LICENCE_EXIST: 'Licence already exist',
  SINGLE_LICENCE: 'Licence fetched Successfully',
  SELECT_VALID_LICENCE: 'Please select valid licence id',

  /**Category */
  CREATE_CATEGORY: 'Category has been created successfully',
  UPDATE_CATEGORY: 'Category has been updated successfully',
  DELETE_CATEGORY: 'Category has been deleted successfully',
  ALL_CATEGORY_LIST: 'Category list was retrieved successfully',
  NO_CATEGORY: 'Category does not exist',
  CATEGORY_EXIST: 'Category already exist',
  SINGLE_CATEGORY: 'Category fetched Successfully',
  SELECT_VALID_NAME: 'Please select valid name',

  /**Member */
  CREATE_MEMBER: 'Member has been created successfully',
  UPDATE_MEMBER: 'Member has been updated successfully',
  DELETE_MEMBER: 'Member has been deleted successfully',
  ALL_MEMBER_LIST: 'Member list was retrieved successfully',
  NO_MEMBER: 'Member does not exist',
  MEMBER_EXIST: 'Member already exist',
  SINGLE_MEMBER: 'Member fetched Successfully',
  SELECT_VALID_FUNCTION: 'Please select valid function',
  SELECT_VALID_USER: 'Please select valid user',

  /**Function */
  CREATE_FUNCTION: 'Function has been created successfully',
  UPDATE_FUNCTION: 'Function has been updated successfully',
  DELETE_FUNCTION: 'Function has been deleted successfully',
  ALL_FUNCTION_LIST: 'Function list was retrieved successfully',
  NO_FUNCTION: 'Function does not exist',
  FUNCTION_EXIST: 'Function already exist',
  SINGLE_FUNCTION: 'Function fetched Successfully',
  INVALID_FUNCTION_NAME: 'Please select valid function name',

  /**Asset */
  CREATE_ASSET: 'Asset has been created successfully',
  UPDATE_ASSET: 'Asset has been updated successfully',
  DELETE_ASSET: 'Asset has been deleted successfully',
  ALL_ASSET_LIST: 'Asset list was retrieved successfully',
  NO_ASSET: 'Asset does not exist',
  ASSET_EXIST: 'Asset already exist',
  SINGLE_ASSET: 'Asset fetched Successfully',

  /**Permission */
  PERMISSION_EXIST: 'Permission already exist',
  CREATE_PERMISSION: 'Permission has been created successfully',
  ALL_PERMISSION_LIST: 'Permission has been retrieved successfully',
  NO_PERMISSION: 'No Permission Found',
  UPDATE_PERMISSION: 'Permission has been updated successfully',
  DELETE_PERMISSION: 'Permission has been deleted successfully',
  SELECT_VALID_PERMISSION: 'Permission type is Array. Please add valid permissions',

  /**Account-Licence */
  ACCOUNT_LICENCE_EXIST: 'Account already has this permission',
  CREATE_ACCOUNT_LICENCE: 'Request has been created successfully',
  UPDATE_ACCOUNT_LICENCE: 'Request has been updated successfully',
  NO_ACCOUNT_LICENCE: 'No licence and permission exists',

  /**Project String */
  PROJECT_STRING_EXIST: 'ProjectString already exist',
  CREATE_PROJECT_STRING: 'ProjectString has been created successfully',
  ALL_PROJECT_STRING_LIST: 'ProjectString has been retrieved successfully',
  NO_PROJECT_STRING: 'No ProjectString Found',
  UPDATE_PROJECT_STRING: 'ProjectString has been updated successfully',
  DELETE_PROJECT_STRING: 'ProjectString has been deleted successfully',
  SELECT_VALID_FROM_LOCATION: 'Please select valid from location',
  SELECT_VALID_TO_LOCATION: 'Please select valid to location',
  INVALID_NAME: 'Invalid name input',

  /**Scope */
  SCOPE_EXIST: 'Scope already exist',
  CREATE_SCOPE: 'Scope has been created successfully',
  ALL_SCOPE_LIST: 'Scope has been retrieved successfully',
  NO_SCOPE: 'No Scope Found',
  UPDATE_SCOPE: 'Scope has been updated successfully',
  DELETE_SCOPE: 'Scope has been deleted successfully',
  SCOPE_HAS_REPORTS: 'Scope has reports',
  SCOPE_HAS_ACTIVITY: 'Scope has activities',

  /**Activity */
  ACTIVITY_EXIST: 'Activity already exist',
  CREATE_ACTIVITY: 'Activity has been created successfully',
  ALL_ACTIVITY_LIST: 'Activity has been retrieved successfully',
  NO_ACTIVITY: 'No Activity Found',
  UPDATE_ACTIVITY: 'Activity has been updated successfully',
  DELETE_ACTIVITY: 'Activity has been deleted successfully',
  GET_ALL_LIST: 'Records has been retrieved successfully',

  /** File Upload */
  FILE_UPLOAD_SUCCESS: 'File uploaded successfully',
  FILE_UPLOAD_FAILED: 'File upload failed',
  TYPE_FIELD_REQUIRED: 'Type field is required',
  FILE_FORMAT_NOT_EXISTS: 'Requested file format is not exist',
  INVALID_FILE_LIMIT: 'Document can not be upload more than 3',
  FILE_UPLOAD_UPDATE: 'File has been update Successfully',
  RENEW_CERTIFICATE: 'Certificate has been renewed successfully',
  FILES_REQUIRED: 'Please add files',

  /** Contractual Detail */
  CREATE_CONTRACTUAL_DETAIL: 'Contractual detail created successfully',
  CONTRACTUAL_DETAIL_EXIST: 'Contractual detail exist',
  CONTRACTUAL_DETAIL_NOT_EXIST: 'Contractual detail not exist',
  INVALID_BIC: 'Please enter a valid bic/swift code',
  CONTRACTUAL_DETAIL_LIST: 'Contractual detail has been retrieved successfully',
  UPDATE_CONTRACTUAL_DETAIL: 'Contractual detail has been updated successfully',
  DOCUMENT_NOT_EXIST: 'Document not exist',
  CERTIFICATE_TYPE_DOCUMENT_EXIST: 'Document already exist',
  CONTRACTUAL_DOCUMENT_REQUIRED: 'Please add document',
  INSERT_BIRTH_DATE: 'Please insert birth date',
  CONTRACTUAL_BANK_NAME_REQUIRED: 'Please insert bank name',
  CONTRACTUAL_BANK_ACCOUNT_REQUIRED: 'Please insert bank account number',
  CONTRACTUAL_DETAILS_REQUIRED: 'Please insert valid details',

  /** User Certificate */
  CREATE_USER_CERTIFICATE: 'User Certificate created successfully',
  USER_CERTIFICATE_EXIST: 'User Certificate exist',
  USER_CERTIFICATE_NOT_EXIST: 'User Certificate does not exist',
  USER_CERTIFICATE_LIST: 'User Certificate has been retrieved successfully',
  UPDATE_USER_CERTIFICATE: 'User Certificate has been updated successfully',

  /** Remove Document */
  REMOVE_DOCUMENT: 'Document removed successfully',
  CERTIFICATE_ASSIGNED_TO_FUNCTION: 'Certificate is assigned to function',
  CERTIFICATE_ASSIGNED_TO_PROJECT: 'Certificate is assigned to project',

  /** Shift-Activity */
  CREATE_SHIFT_ACTIVITY: 'Shift-Activity has been created successfully',
  UPDATE_SHIFT_ACTIVITY: 'Shift-Activity has been updated successfully',
  DELETE_SHIFT_ACTIVITY: 'Shift-Activity has been deleted successfully',
  SHIFT_ACTVITY_LIST: 'Shift-Activities retrieved successfully',
  SHIFT_ACTVITY_SUMMARY: 'Shift-Activities summary retrieved successfully',
  SHIFT_ACTVITY_NOT_EXIST: 'Shift-Activity not exist',
  SHIFT_ID_NOT_EXIST: 'Please enter shift. Shift is not exists in request body',
  INVALID_END_TIME: 'Invalid End DateTime',
  VALID_END_TIME: 'Valid End DateTime',
  CALCULATE_SHIFT_DURATION_FAIL: 'Error while calculating shift duration',
  INVALID_SHIFT_ID: 'Invalid Shift Id',
  ACTIVITY_REQUIRED: 'Please select activity',

  /**Report-Type */
  REPORT_EXIST: 'Report already exist',
  CREATE_REPORT: 'Report has been created successfully',
  ALL_REPORT_LIST: 'Report has been retrieved successfully',
  NO_REPORT: 'No Report Found',
  UPDATE_REPORT: 'Report has been updated successfully',
  DELETE_REPORT: 'Report has been deleted successfully',
  ASSIGN_ASSET: 'Please assign atleast one asset',
  ASSIGN_LOCATION: 'Please assign atleast one location',
  REPORT_MIGRATION: 'Migration completed, updated successfully',
  INVALID_TYPE: 'Please select valid type',
  INVALID_SORT_ORDER: 'Please select valid sort order',
  DURATION_EMPTY: 'Duration should not be empty',
  INVALID_DURATION: 'Please select valid duration',
  WEIGHTAGE_EMPTY: 'Weightage should not be empty',
  INVALID_WEIGHTAGE: 'Please select valid weightage',
  WEIGHTAGE_SHOULD_GREATER: 'Weightage should be greater than 0 and less than 100',
  DETAILED_PROGRESS_REPORT_SUCCESS: 'Detailed progress report fetched successfully',

  /**Report Parameter */
  CREATE_REPORT_PARAMETER: 'Report Parameter has been created successfully',
  ALL_REPORT_PARAMETER_LIST: 'Report Parameter has been retrieved successfully',
  UPDATE_REPORT_PARAMETER: 'Report Parameter has been updated successfully',
  DELETE_REPORT_PARAMETER: 'Report Parameter has been deleted successfully',
  NO_REPORT_PARAMETER: 'No Report Parameter Found',

  /** Report */
  REPORT_LIST: 'Reports retrieved successfully',
  GET_REPORT: 'Report retrieved successfully',
  REPORT_NOT_EXIST: 'Report not exist',
  REPORT_STATUS: 'Report status updated successfully',
  REPORT_DETAIL_NOT_EXIST: 'Report detail not exist',
  REPORT_SYNC_UP: 'Reports has been sync successfully',
  SHIFT_SYNC_UP: 'Shift has been sync successfully',
  SHIFT_ACTIVITY_SYNC_UP: 'Shift activity has been sync successfully',
  PERSONNEL_LIST: 'Personnel List fetched successfully',
  DAILY_ACTIVITY_LOG: 'Daily Activity Log fetched successfully',
  MEMBERS_SYNC_UP: 'Member has been sync successfully',
  CARD_SYNC_UP: 'Safety card has been sync successfully',
  INVALID_REPORT_ID: 'Invalid report id',
  ERROR_DURING_REPORT_UPDATE: 'Error during report update:',
  REPORT_REQUIRED: 'Please add report',
  REPORT_TYPE_REQUIRED: 'Please enter report-type',
  REPORT_LOCATION_REQUIRED: 'Please select location',
  REPORT_CABLE_REQUIRED: 'Please select cable',

  /** Feedback */
  CREATE_FEEDBACK: 'Feedback submitted successfully',
  FEEDBACK_LIST: 'Feedbacks retrieved successfully',
  GET_FEEDBACK: 'Feedback retrieved successfully',
  FEEDBACK_NOT_EXIST: 'Feedback not exist',
  UPDATE_FEEDBACK: 'Feedback has been updated successfully',
  DELETE_FEEDBACK: 'Feedback has been deleted successfully',
  FEEDBACK_TYPE_REQUIRED: 'Please select type',
  SUBJECT_REQUIRED: 'Please select subject',
  DESCRIPTION_REQUIRED: 'Please enter description',

  /** Question */
  CREATE_QUESTION: 'Question created successfully',
  QUESTION_EXIST: 'Question already exist',
  QUESTION_NOT_EXIST: 'Question not exist',
  GET_ALL_QUESTIONS: 'Questions retrieved successfully',
  GET_QUESTION: 'Question retrieved successfully',
  UPDATE_QUESTION: 'Question has been updated successfully',
  DELETE_QUESTION: 'Question has been deleted successfully',
  QUESTION_REQUIRED: 'Please add questions',
  TITLE_REQUIRED: 'Please enter title',

  /** Role */
  CREATE_ROLE: 'Role created successfully',
  ROLE_EXIST: 'Role already exist',
  ROLE_NOT_EXIST: 'Role does not exist',
  GET_ALL_ROLES: 'Roles retrieved successfully',
  GET_ROLE: 'Role retrieved successfully',
  UPDATE_ROLE: 'Role has been updated successfully',
  DELETE_ROLE: 'Role has been deleted successfully',
  STATUS_CHANGE: 'Role status has been changed',
  BANNED_KEYWORD: 'Cannot create this role. Please contact administrator',
  INVALID_ROLE_ID: 'Invalid role id',
  ISACTIVE_REQUIRED: 'Please enter is active',
  ISASSIGNALLPROJECTS_REQUIRED: 'Please enter assign project',

  /** Role-Agreement */
  ASSIGN_ROLE_AGREEMENT: 'Role agreement assigned successfully',
  ROLE_AGREEMENT_NOT_FOUND: 'Role agreement not found',
  ROLE_AGREEMENT_LIST: 'Role agreements retrieved successfully',
  ROLE_AGREEMENT_READ_VALIDATION: 'Read permission is required for update or delete',
  INVALID_AGREEMENT: 'Invalid agreements',

  /** Warehouse */
  CREATE_WAREHOUSE: 'Warehouse created successfully',
  GET_WAREHOUSES: 'Warehouses retrived successfully',
  WAREHOUSE_NOT_FOUND: 'Warehouse not found',
  GET_WAREHOUSE: 'Warehouse retrieved successfully',
  UPDATE_WAREHOUSE: 'Warehouse has been updated successfully',
  DELETE_WAREHOUSE: 'Warehouse has been deleted successfully',
  WAREHOUSE_EXIST: 'Warehouse already exist',
  WAREHOUSE_NAME_REQUIRED: 'Please enter name',
  WAREHOUSE_STREET_REQUIRED: 'Please enter street',
  WAREHOUSE_CITY_REQUIRED: 'Please enter city',
  WAREHOUSE_STATE_REQUIRED: 'Please enter state',
  WAREHOUSE_COUNTRY_REQUIRED: 'Please enter country',
  WAREHOUSE_ZIPCODE_REQUIRED: 'Please enter zip code',
  WAREHOUSE_EMAIL_REQUIRED: 'Please enter email',
  WAREHOUSE_COUNTER: 'Warehouse counter retrieved successfully',
  WM_REVERT_ORDER_TO_OPEN: 'Order has been reverted to open orders',

  /** Equipment-Category */
  CREATE_EQUIPMENT_CATEGORY: 'Equipment category created successfully',
  GET_EQUIPMENT_CATEGORY: 'Equipment category retrieved successfully',
  EQUIPMENT_CATEGORY_NOT_FOUND: 'Equipment category not found',
  UPDATE_EQUIPMENT_CATEGORY: 'Equipment category has been updated successfully',
  DELETE_EQUIPMENT_CATEGORY: 'Equipment category has been deleted successfully',
  EQUIPMENT_CATEGORY_NAME_EXIST: 'Equipment category name already exist',
  EQUIPMENT_CATEGORY_ABBRIVATION_EXIST: 'Equipment category abbrivation already exist',
  EQUIPMENT_CATEGORY_EXIST: 'Equipment category already exist',
  EQUIPMENT_CATEGORY_NAME_REQUIRED: 'Name is required',
  EQUIPMENT_CATEGORY_ABBRIVATION_REQUIRED: 'Please enter valid abbreviation',
  EQUIPMENT_CATEGORY_REQUIRED: 'Equipment category is required',
  EQUIPMENT_UNIT_REQUIRED: 'Equipment unit is required',
  EQUIPMENT_PRICE_REQUIRED: 'Price is required',

  /** Equipment-Type */
  CREATE_EQUIPMENT_TYPE: 'Equipment type created successfully',
  GET_EQUIPMENT_TYPE: 'Equipment type retrieved successfully',
  EQUIPMENT_TYPE_NOT_FOUND: 'Equipment type not found',
  UPDATE_EQUIPMENT_TYPE: 'Equipment type has been updated successfully',
  DELETE_EQUIPMENT_TYPE: 'Equipment type has been deleted successfully',
  EQUIPMENT_TYPE_FOUND: 'Quantity type as unique is already exist',
  CANNOT_DELETE_EQUIPMENT_TYPE:
    'Cannot delete equipment type. Please delete all related equipment first',
  TYPE_REQUIRED: 'Type is required',
  HS_CODE_REQUIRED: 'HS code is required',

  /** Project-Equipment-Type */
  PROJECT_EQUIPMENT_TYPE_EXIST: 'Project equipment type already exist',
  CREATE_PROJECT_EQUIPMENT_TYPE: 'Project equipment type created successfully',
  PROJECT_EQUIPMENT_TYPE_LIST: 'Project equipment type list retrieved successfully',
  REMOVE_PROJECT_EQUIPMENT_TYPE: 'Project equipment type removed successfully',
  UPDATE_PROJECT_EQUIPMENT_TYPE: 'Project equipment type updated successfully',
  PROJECT_EQUIPMENT_TYPE_DOES_NOT_EXIST: 'Project equipment type does not exist',
  EQUIPMENT_TYPE_IN_USE: 'Equipment type is in use',
  MOVE_EQUIPMENT_TYPE_TO_SUITABLE_STATUS: 'Move equipment type to suitable status',

  /** HS-Code */
  CREATE_HS_CODE: 'HS Code created successfully',
  GET_HS_CODE: 'HS Code retrieved successfully',
  HS_CODE_NOT_FOUND: 'HS Code not found',
  UPDATE_HS_CODE: 'HS Code has been updated successfully',
  DELETE_HS_CODE: 'HS Code has been deleted successfully',
  HS_CODE_EXIST: 'HS Code already exist, it should be unique',

  /** Equipment */
  CREATE_EQUIPMENT: 'Equipment created successfully',
  GET_EQUIPMENT: 'Equipment retrieved successfully',
  EQUIPMENT_NOT_FOUND: 'Equipment not found',
  UPDATE_EQUIPMENT: 'Equipment has been updated successfully',
  DELETE_EQUIPMENT: 'Equipment has been deleted successfully',
  INVALID_WEIGHT: 'Invalid input, weight should be greater than 0',
  BIND_QR_CODE: 'QR code binds successfully with the equipment',
  QR_CODE_ALREADY_BINDED: 'QR code is already binded with equipment',
  DUPLICATE_SERIAL_NUMBER: 'Serial number already exist',
  OUTDATED_QR_CODE: 'QR code is outdated. Please re-scan the QR code.',
  EQUIPMENT_OUT_OF_STOCK: 'Equipment is out of stock.',
  QRCODE_REQUIRED: 'QR code and return order is required.',
  INVALID_QUANTITY_TYPE: 'Scanned equipment is not a rental',
  EQUIPMENT_TYPE_REQUIRED: 'Equipment Type is required',
  WEIGHT_REQUIRED: 'Weight is required',
  EQUIPMENT_QUANTITY_DETAILS: 'Equipment quantity details retrieved successfully',
  EQUIPMENT_SUMMARY: 'Equipment summary retrieved successfully',

  /** Equipment Image Certificate */
  CREATE_EQUIPMENT_IMAGE_CERTIFICATE: 'Equipment Image & Certificate created successfully',
  GET_EQUIPMENTIMAGE_CERTIFICATE: 'Equipment Image & Certificate retrieved successfully',
  IMAGE_CERTIFICATE_NOT_FOUND: 'Equipment Image & Certificate not found',
  UPDATE_IMAGE_CERTIFICATE: 'Equipment Image & Certificate has been updated successfully',
  DELETE_IMAGE_CERTIFICATE: 'Equipment Image & Certificate has been deleted successfully',
  EQUIPMENT_REQUIRED: 'Equipment is required',
  EQUIPMENT_IMAGE_CERTIFICATE_REQUIRED: 'Certificate is required',

  /** Equipment Warehouse */
  CREATE_EQUIPMENT_WAREHOUSE: 'Equipment warehouse created successfully',
  GET_EQUIPMENT_WAREHOUSE: 'Equipment warehouse retrieved successfully',
  EQUIPMENT_WAREHOUSE_NOT_FOUND: 'Equipment warehouse not found',
  UPDATE_EQUIPMENT_WAREHOUSE: 'Equipment warehouse has been updated successfully',
  DELETE_EQUIPMENT_WAREHOUSE: 'Equipment warehouse has been deleted successfully',
  WAREHOUSE_REQUIRED: 'Warehouse is required',

  /**Assigned Project */
  ASSIGNED_PROJECT: 'Project Assigned successfully',
  GET_ASSIGNED_PROJECT: 'Project retrieved successfully',

  /** Equipment Certificate Type */
  CREATE_EQUIPMENT_CERTIFICATE_TYPE: 'Equipment certificate type created successfully',
  GET_EQUIPMENT_CERTIFICATE_TYPE: 'Equipment certificate type retrieved successfully',
  EQUIPMENT_CERTIFICATE_TYPE_NOT_FOUND: 'Equipment certificate type not found',
  UPDATE_EQUIPMENT_CERTIFICATE_TYPE: 'Equipment certificate type has been updated successfully',
  DELETE_EQUIPMENT_CERTIFICATE_TYPE: 'Equipment certificate type has been deleted successfully',
  DUPLICATE_EQUIPMENT_CERTIFICATE_TYPE: 'Equipment certificate type already exist',
  EQUIPMENT_CERTIFICATE_TITLE_REQUIRED: 'Title is required',

  /** Equipment-Unit */
  CREATE_EQUIPMENT_UNIT: 'Equipment unit created successfully',
  GET_EQUIPMENT_UNIT: 'Equipment unit retrieved successfully',
  EQUIPMENT_UNIT_NOT_FOUND: 'Equipment unit not found',
  UPDATE_EQUIPMENT_UNIT: 'Equipment unit has been updated successfully',
  DELETE_EQUIPMENT_UNIT: 'Equipment unit has been deleted successfully',

  /** Equipment-Quantity-Type */
  CREATE_EQUIPMENT_QUANTITY_TYPE: 'Equipment quantity type created successfully',
  GET_EQUIPMENT_QUANTITY_TYPE: 'Equipment quantity type retrieved successfully',
  EQUIPMENT_QUANTITY_TYPE_NOT_FOUND: 'Equipment quantity type not found',
  UPDATE_EQUIPMENT_QUANTITY_TYPE: 'Equipment quantity type has been updated successfully',
  DELETE_EQUIPMENT_QUANTITY_TYPE: 'Equipment quantity type has been deleted successfully',
  PRICE_TYPE_REQUIRED: 'Price type is required',
  QUANTITY_TYPE_REQUIRED: 'Quantity type is required',

  /** Equipment-Order-Request */
  CREATE_EQUIPMENT_ORDER_REQUEST: 'Equipment order request created successfully',
  GET_EQUIPMENT_ORDER_REQUEST: 'Equipment order request retrieved successfully',
  EQUIPMENT_ORDER_REQUEST_NOT_FOUND: 'Equipment order request not found',
  UPDATE_EQUIPMENT_ORDER_REQUEST: 'Equipment order request has been updated successfully',
  DELETE_EQUIPMENT_ORDER_REQUEST: 'Equipment order request has been deleted successfully',
  NO_APPROVER: 'Approver not found',
  CHANGE_EQUIPMENT_ORDER_REQUEST: 'Equipment order request has been changed successfully',
  GET_EQUIPMENT_TYPE_ORDER_HISTORY: 'Equipment type order history retrieved successfully',

  /** Currency-Unit */
  CREATE_CURRENCY_UNIT: 'Currency unit created successfully',
  GET_CURRENCY_UNIT: 'Currency unit retrieved successfully',
  CURRENCY_UNIT_NOT_FOUND: 'Currency unit not found',
  UPDATE_CURRENCY_UNIT: 'Currency unit has been updated successfully',
  DELETE_CURRENCY_UNIT: 'Currency unit has been deleted successfully',
  DEFAULT_CURRENCY_UNIT_EXIST: 'Default currency unit already assigned',
  SYMBOL_REQUIRED: 'Symbol is required',

  /** Assign-Project */
  INSERT_ASSIGN_PROJECT: 'Project assigned successfully',
  DELETE_ASSIGN_PROJECT: 'Assigned project to user remove successfully',
  GET_ASSIGN_PROJECT: 'Get assign project data successfully',
  PROJECT_ALREADY_ASSIGN: 'Project already assigned',
  NOT_PROJECT_MANAGER: 'User is not project manager',

  /** PM Order Request */
  CREATE_PM_ORDER_REQUEST: 'PM order request created successfully',
  GET_PM_ORDER_REQUEST: 'Get orders successfully',
  ADD_EQ_ORDER_IN_QUEUE: 'Equipment orders added to queue',
  PM_ORDER_REQUEST_NOT_FOUND: 'PM order request not found',
  UPDATE_PM_ORDER_REQUEST: 'PM order request has been updated successfully',
  INVALID_ASSIGN_EQUIPMENT: 'Invalid assign equipment quantity',
  ASSIGN_EQUIPMENT: 'Equipment has been assigned successfully',
  ALL_EQUIPMENT_CHECKOUT: 'All equipment has been checkout successfully',
  ORDER_NOT_CHECKOUT: 'Order can not be checkout. Please add all equipment to checkout.',
  CANNOT_CHECK_OUT_BUY_EQUIPMENT: 'Equipment quantity type is buy. It cannot be checkout.',
  CHECKOUT_LIST: 'Checkout list retrieved successfully',
  GET_LINKED_EQUIPMENT: 'Get linked equipment successfully',
  GET_ORDER_STATUS_COUNT: 'Get order status count successfully',
  INVALID_ORDER_ID: 'Invalid order id',
  ORDER_NOT_FOUND: 'Order not found',
  ORDER_REJECTED: 'Order has been rejected successfully',
  MISSING_ORDER_DATA: 'Missing some order data for load pdf',
  PM_PROJECT_REQUIRED: 'Please enter project',
  PM_EQUIPMENT_TYPE_DATA: 'Please add equipment type data',
  QUANTITY_REQUIRED: 'Please add quantity',
  PM_EQUIPMENT_COUNTER: 'PM Equipment counter retrieved successfully',

  /** PM Order Manage Equipment */
  PM_ORDER_DETAILS_NOT_FOUND: 'PM order details not found',
  UPDATE_PM_ORDER_DETAILS: 'PM order details has been updated successfully',
  EQUIPMENT_LINKED_IN_ORDER: 'Equipment already linked in order',
  EQUIPMENT_ALREADY_USED_IN_ORDER: 'Equipment already used in order',
  ASSING_EQUIPMENT_QUANTITY_MORE: 'Assign quantity is more than approved quantity',
  EQUIPMENT_LINKED_SUCCESSFULLY: 'Equipment linked successfully',
  INVALID_QUANTITY: 'Invalid quantity.',
  PM_ORDER_DETAILS_REJECTED: 'PM order details has been rejected successfully',
  EQUIPMENT_NOT_LINKED_IN_ORDER: 'Equipment is not linked in order',
  NEW_EQUIPMENT_NOT_LINKED_IN_ORDER: 'Please link new equipment in order for continue process',
  REQUIRED_EQUIPMENT_NOT_LINK: 'Required equipment not linked',
  REQUIRED_EQUIPMENT_QUANTITY_NOT_LINK: 'Required equipment quantity not linked',
  SCANNED_EQUIPMENT_NOT_IN_ORDER: 'Scanned equipment is not in order or already added in cart',
  SCANNED_QR_NOT_IN_ORDER: 'Scanned equipment is not in order or QR not exist.',
  SCANNED_EQUIPMENT_NOT_LINKED: 'Scanned equipment is not linked.',
  LINKED_EQUIPMENT_UPDATED_SUCCESSFULLY: 'Linked equipment updated successfully',
  LINKED_EQUIPMENT_REMOVED_SUCCESSFULLY: 'Linked equipment removed successfully',
  INVALID_REQUESTED_QUANTITY: 'Requested quantity is more than required quantity.',
  NO_EQUIPMENT_LINKED_IN_ORDER: 'No equipment linked in order',

  /** Checkin Request */
  CHECKIN_REQUEST_LIST: 'Checkin request list retrieved successfully',
  CHECKIN_EQUIPMENT_REQUEST_LIST: 'Checkin equipment request list retrieved successfully',
  CHECKIN_EQUIPMENT_REQUEST: 'Checkin equipment request retrieved successfully',
  EQUIPMENT_CHECKEDIN: 'Equipment added to checkin successfully',
  EQUIPMENT_CHECKEDIN_SUMMARY: 'Equipment checkin summary retrieved successfully',
  EQUIPMENT_MORE_THAN_LINKED: 'Equipment quantity is more than linked quantity',
  LINKED_EQUIPMENT_NOT_FOUND: 'Linked Equipment Not Found',
  ALREADY_LINKED: 'Equipment Already Linked',

  /**Missing Equipment */
  EQUIPMENT_ADDED_IN_MISSING: 'Equipment added to missing successfully',
  EQUIPMENT_ADDED_IN_STOCK: 'Equipment added to in stock successfully',
  USER_ORDERED_EQUIPMENT_EQUIPMENT_LIST: 'User ordered equipment list retrieved successfully',
  ORDER_NUMBER_LIST: 'Order number list retrieved successfully',
  MISSING_EQUIPMENT_LIST: 'Missing equipment list retrieved successfully',

  /**Certificate Type */
  CREATE_CERTIFICATE_TYPE: 'Certificate type created successfully',
  CERTIFICATE_TYPE_EXIST: 'Certificate type already exist',
  CERTIFICATE_TYPE_NOT_EXIST: 'Certificate type not exist',
  CERTIFICATE_TYPE_LIST: 'Certificate type has been retrieved successfully',
  CERTIFICATE_TYPE_UPDATE: 'Certificate type has been updated successfully',
  ERROR_CERTIFICATE_TYPE: 'Invalid validityDate filter value. Use true, false, or all.',

  /** Equipment Serial Number */
  CREATE_EQUIPMENT_SERIAL_NUMBER: 'Equipment serial number created successfully',
  GET_EQUIPMENT_SERIAL_NUMBER: 'Equipment serial number retrieved successfully',
  SERIAL_NUMBER_OVER_LIMIT: 'Serial numbers you trying to added are over equipment quantity.',

  /** Certificate Email */
  CERTIFICATE_NOT_EXIST: 'Certificate does not exist',
  APPROVE_EMAIL_MESSAGE:
    'Congratulations! We are delighted to inform you that your certificate has been approved.',
  APPROVE_IMAGE_SUBJECT: 'Certificate Approval Notification',
  REJECT_IMAGE_SUBJECT: ' Certificate Request Rejection Notification',
  REJECT_EMAIL_MESSAGE:
    'We regret to inform you that your recent certificate request has not been approved.',
  REJECT_REASON: 'Reason for Rejection',
  APPROVE_IMAGE_TYPE: 'approved',
  REJECT_IMAGE_TYPE: 'rejected',

  /** Profile Function */
  PROFILE_FUNCTION_EXIST: 'Profile Function already exist',
  PROFILE_FUNCTION_RETRIVED_SUCCESSFULLY: 'Profile Function has been retrieved successfully',
  PROFILE_FUNCTION_CREATED: 'Profile Function has been created successfully',
  PROFILE_FUNCTION_UPDATED: 'Profile Function has been updated successfully',
  NO_PROFILE_FUNCTION: 'No Profile Function Found',
  DELETE_PROFILE_FUNCTION: 'Profile Function has been deleted successfully',
  SELECT_VALID_PROFILE_NAME: 'Please select valid profile function name',
  SELECT_VALID_PROFILE_STATUS: 'Please select valid profile function status',

  /** Return Order */
  GET_PM_ORDER_EQUIPMENT_LIST: 'Get orders equipment successfully',
  READY_TO_CHECKOUT: 'Equipment add in ready to checkout successfully',
  UPDATE_ORDER_HISTORY_STATUS: 'Equipment order history status has been updated successfully',
  ORDER_HAS_BEEN_RETURN: 'Order has been returned successfully',
  RETURN_ORDER_BODY_EMPTY: 'Return order body is empty',
  RETURN_CART_IS_EMPTY: 'Please add equipment in cart to return',
  REMOVE_CART_EQUIPMENT: 'Equipment has been removed from cart successfully',
  RETURN_ORDER_NOT_CREATED: 'Return order has not been created',
  GET_PM_RETURN_ORDER: 'Get return order successfully',
  RETURN_ORDER_NOT_FOUND: 'Equipment not found in this return order',
  MISSING_EQUIPMENT_ADDED: 'Missing equipment added successfully',
  RETURN_ORDER_UPDATED: 'Return order has been updated successfully',
  INVALID_RETURN_ORDER: 'Invalid return order',
  EQUIPMENT_ALREADY_IN_RETURN_CART: 'Equipment already in return cart',
  EQUIPMENT_NOT_IN_INVENTORY: 'Equipment is not in project inventory',
  RETURN_ORDER_REQUIRED: 'Please add returnOrder',

  /** Inventory History */
  GET_INVENTORY_HISTORY: 'Inventory history retrieved successfully',
  GET_EQUIPMENT_ORDER_HISTORY: 'Equipment order history retrieved successfully',

  /**Report-Question */
  CREATE_REPORT_QUESTION: 'Report question has been created successfully',
  INCORRECT_ANSWER_TITLES: 'Incorrect answer titles',
  GET_REPORT_QUESTION_ANSWERS: 'Report question answers retrieved successfully',
  INVALID_QUESTION_ID: 'Invalid question id',
  REPORT_QUESTION_NOT_EXIST: 'Report question not exist',
  REPORT_QUESTION_IS_PUBLISHED: 'Report question is published, it can not be update or delete.',
  UPDATE_REPORT_QUESTION_AND_ANSWER: 'Report question and answer has been updated successfully',
  DELETE_REPORT_QUESTION_AND_ANSWER: 'Report question and answer has been deleted successfully',
  GET_REPORT_CALCULATION: 'Report calculation retrieved successfully',
  GET_PROJECT_TRACKER: 'Project tracker retrieved successfully',
  GET_PROJECT_TRACKER_SUMMARY: 'Project tracker summary retrieved successfully',
  SORT_ORDER_REQUIRED: 'Please add sortOrder',
  DURATION_REQUIRED: 'Please add duration',
  ANSWERS_REQUIRED: 'Please add answers',

  /** Parameter-type */
  GET_PARAMETER_TYPES: 'Parameter types retrieved successfully',

  /** User-Report */
  CREATE_USER_REPORT: 'User report has been created successfully',
  GET_USER_REPORTS: 'User reports retrieved successfully',
  UPDATE_USER_REPORT: 'User report has been updated successfully',
  INVALID_SIGNTURE_BY_ID: 'Invalid signature by id',
  DELETE_USER_REPORT: 'User report has been deleted successfully',
  USER_REPORT_NOT_EXIST: 'User report not exist',
  SIGNATURE_REQUIRED: 'Please add signature',
  STATUS_REQUIRED: 'Please add status',

  /** User-Report-Answer */
  CREATE_USER_REPORT_ANSWER: 'User report question answer has been submitted successfully',
  UPDATE_USER_REPORT_ANSWER: 'User report question answer has been updated successfully',
  REMOVE_USER_REPORT_ANSWER_TITLE:
    'User report question answer title has been removed successfully',
  NO_USER_REPORT_ANSWER: 'No user report answer found',
  INVALID_ANSWER_TITLE_ID: 'Invalid answer title id',
  ANSWER_TITLE_UNASSIGNED: 'Answer title unassigned from user report question successfully',
  ANSWER_TITLE_ASSIGNED: 'Answer title assigned to user report question successfully',
  ALREADY_EXIST: 'Answer title already exist',
  REPORT_QUESTION_REQUIRED: 'Please add reportQuestion',
  REPORT_QUESTION_ANSWERS_REQUIRED: 'Please add reportQuestionAnswers',
  USER_REPORT_ANSWERS_REQUIRED: 'Please add userReportAnswers',
  USER_PROJECT_REPORT_REQUIRED: 'Please add userProjectReport',
  ANSWER_TITLE_ID_REQUIRED: 'Please add answerTitleId',
  ISPRINTABLE_REQUIRED: 'Please add isPrintable',

  /** Toolbox-Talk */
  CREATE_TOOLBOX_TALK: 'Toolbox talk has been submitted successfully',
  GET_TOOLBOX_TALKS: 'Toolbox talks retrieved successfully',
  DELETE_TOOLBOX_TALK: 'Toolbox talk has been deleted successfully',
  TOOLBOX_TALK_SYNC_UP: 'Toolbox talk has been sync successfully',
  MISSING_TOOLBOX_DATA: 'Missing toolbox talk data for load pdf',
  TEAM_REQUIRED: 'Please add team',
  NOTE_REQUIRED: 'Please add note',
  HOST_REQUIRED: 'Please add host',
  CREATED_BY_SIGNATURE_REQUIRED: 'Please add createdBySignature',

  /** Unit Test Cases */
  INVALID_VALUE: 'Invalid value',
  INVALID_PASSWORD_CHARS:
    'Password should contain at least one uppercase, one lowercase and one symbol',
  INVALID_PASSWORD_LENGTH: 'Password should be atleast 6 characters long',

  /** Project-Document */
  CREATE_PROJECT_DOCUMENT: 'Project Document has been submitted successfully',
  GET_PROJECT_DOCUMENT_LIST: 'Project Documents has been retrieved successfully',
  GET_PROJECT_DOCUMENT: 'Project Document has been retrieved successfully',
  NO_PROJECT_DOCUMENT: 'No Project Document Found',
  UPDATE_PROJECT_DOCUMENT: 'Project Document has been updated successfully',
  RENEW_PROJECT_DOCUMENT: 'Project Document has been renewed successfully',
  DELETE_PROJECT_DOCUMENT: 'Project Document has been deleted successfully',
  REMOVE_DOCUMENT_IN_PROJECT_DOCUMENT: 'Document has been removed successfully',
  DOCUMENT_TITLE_REQUIRED: 'Please add document title',
  DOCUMENT_NUMBER_REQUIRED: 'Please add document number',
  DOCUMENT_TYPE_REQUIRED: 'Please add document type',
  DOCUMENT_TYPE_INVALID: 'Invalid document type',

  /** DB Migration */
  DB_MIGRATION: 'DB Migration completed successfully',

  // Equipment-order-History
  NO_EQUIPMENT_ORDER_HISTORY: 'Equipment order history not found',

  // Version Info
  CREATE_VERSION_INFO: 'Version info has been created successfully',
  GET_VERSION_INFO: 'Version info has been retrieved successfully',
  UPDATE_VERSION_INFO: 'Version info has been updated successfully',
  INVALID_PLATFORM: 'Please enter valid platform',
  INVALID_VERSION_CODE: 'Please enter valid version code',
  INVALID_VERSION_NAME: 'Please enter valid version name',
  INVALID_PACKAGE_NAME: 'Please enter valid package name',
  VERSION_INFO_NOT_EXIST: 'Version info not exist',
  ALREADY_LATEST_VERSION: 'Already on latest version',
  LOWER_VERSION: 'Current version is lower. Please update.',
  DELETED_VERSION_INFO: 'Version info has been deleted successfully',

  //Report Document
  CREATE_REPORT_DOCUMENT: 'Report document has been created successfully',
  REPORT_DOCUMENT_NOT_EXIST: 'Report document does not exist',
  UPDATE_REPORT_DOCUMENT: 'Report documnet has been updated successfully',
  GET_REPORT_DOCUMENT: 'Report document has been retrieved successfully',
  DELETE_REPORT_DOCUMENT: 'Report Document has been deleted successfully',
  REPORT_DOCUMENT_EXIST: 'Report Document already exists',
  DOCUMENT_NAME_REQUIRED: 'Please add document name',
  USER_REPORT_REQUIRED: 'Please add userReport',

  //User Project Report
  USER_PROJECT_REPORT_NOT_EXIST: 'User project report does not exist',
  GET_USER_PROJECT_REPORT: 'User project report has been retrieved successfully',

  // Dpr
  DPR_FETCH: 'DPR has been fetched successfully',

  // Date Format
  SIMPLE_DATE_FORMAT: 'dd-mm-yyyy',

  // Report Statuses
  CHECKED: 'checked',
  CLOSED: 'closed',

  // Seeders
  SEEDER_SUCCESS: 'Seeder executed successfully',
  START_PROCESSING: 'start processing......',
  NO_FOLDER_PATH: 'No folder path provided',
  START_INDEX_SHOULD_BE_LESS: 'Start index should be less than end index',
  END_PROCESS: 'end process......',
  IMAGE_PROCESS_COMPLETED: 'Image processing complete',
  PARAMETER_ALREADY_EXIST: 'Parameter-type already exist',
  MODEL_NAME_NOT_PROVIDED: 'No model name provided',
  METHOD_NAME_NOT_PROVIDED: 'No method name provided',
  NO_FIELD_OR_DEFAULT_PROVIDED: 'No field or default value provided',
  FIELD_NOT_PROVIDED: 'Field is not provided',

  INVALID_SEARCH: 'Invalid search input',

  // DPR
  DPR_CREATE_SUCCESS: 'DPR created successfully',
  DPR_UPDATE_SUCCESS: 'DPR updated successfully',
  DPR_DELETE_SUCCESS: 'DPR deleted successfully',
  VERSION_ID_REQUIRED: 'Please enter version id',
  DPR_NO_REQUIRED: 'Please enter dpr no',
  DPR_DATE_REQUIRED: 'Please enter dpr date',
  NO_DPR: 'Dpr does not exist',
  DPR_DATA_REQUIRED: 'Dpr data is required',
  DPR_ALREADY_EXIST: 'DPR already exist, choose different project or date',
  NO_DPR_DATA: 'No dpr data found, Check the dpr version',

  INVALID_EQUIPMENT_TYPE_ID: 'Invalid equipment type id',

  // Shopping Cart
  SHOPPING_CART_CREATED: 'Shopping Cart created successfully',
  FROM_DATE_REQUIRED: 'From date is required',
  TO_DATE_REQUIRED: 'To date is required',
  SHOPPING_CART_EXIST: 'Shopping Cart already exist',
  SHOPPING_CART_NOT_FOUND: 'Shopping Cart not found',
  REJECT_SHOPPING_CART: 'Shopping Cart rejected successfully',

  // Ce Norms
  CE_NORMS_CREATED: 'CE Norms created successfully',
  CE_NORMS_NAME_REQUIRED: 'Name is required',
  CE_NORMS_MONTH_REQUIRED: 'Month is required',
  CE_NORMS_NOT_FOUND: 'CE Norms not found',
  CE_NORMS_UPDATED: 'CE Norms updated successfully',
  GET_CE_NORMS: 'CE Norms retrieved successfully',
  CE_NORMS_DELETED: 'CE Norms deleted successfully',

  // Manage Sync API
  CHECK_MANAGE_SYNC_API: 'Sync API checked successfully',
  // Not in List
  NOT_EXIST_NOT_IN_LIST: 'Not in list id does not exist',
  DELETED_NOT_IN_LIST: 'Not in list  deleted successfully',

  // Nationality
  CREATE_NATIONALITY: 'Nationality has been created successfully',
  GET_NATIONALITY: 'Nationality has been retrieved successfully',
  EXIST_NATIONALITY: 'Nationality already exist',

  // Logbook
  LOGBOOK_CREATED: 'Logbook created successfully',
  LOGBOOK_UPDATED: 'Logbook updated successfully',
  LOGBOOK_DELETED: 'Logbook deleted successfully',
  LOGBOOK_NOT_FOUND: 'Logbook not found',
  LOGBOOK_FETCHED: 'Logbooks fetched successfully',
  LOGBOOK_DESCRIPTION_REQUIRED: 'Please add description',
  LOGBOOK_ID_REQUIRED: 'Please add logbook id',
  LOGBOOK_USER_ID_REQUIRED: 'Please add user id',
  LOGBOOK_INVALID_USER_ID: 'Invalid user id format',
  LOGBOOK_INVALID_PROJECT_ID: 'Invalid project id format',
  LOGBOOK_INVALID_LOGBOOK_ID: 'Invalid logbook id format',
  LOGBOOK_DESCRIPTION_STRING: 'Description must be a string',

  // Certificate
  NO_CERTIFICATE: 'No certificate exist',
  CERTIFICATE_DELETED: 'Certificate Deleted Successfully',

  // General Document
  NO_GENERAL_DOCUMENT: 'No general document exists.',
  GENERAL_DOCUMENT_DELETED: 'The general document has been deleted successfully.',
  GET_GENERAL_DOCUMENT: 'The general document has been retrieved successfully.',
  DOCUMENT_CREATED: 'The document has been created successfully.',
  DOCUMENT_UPDATED: 'The document has been updated successfully.',
  DOCUMENT_DELETED: 'The document has been deleted successfully.',
  INVALID_DOCUMENT_NAME: 'Please enter document name',
  INVALID_DOCUMENT_URL: 'Please enter document url',

  // Safety notifications
  GET_NON_ACKNOWLEDGED_DOCUMENTS: 'Non-acknowledged documents retrieved successfully',
  FAILED_TO_ACKNOWLEDGE_DOCUMENT: 'Failed to acknowledge document',
  ACKNOWLEDGE_DOCUMENT: 'Document acknowledged successfully',
};
