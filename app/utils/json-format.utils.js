const config = {
  username: '',
  id: '',
  supportedVersion: '',
  screens: [],
  logoUrl:
    'https://firebasestorage.googleapis.com/v0/b/toolbox-suite.appspot.com/o/toolBoxSuitLogoBlocks.png?alt=media&token=********-5b44-4494-96d5-80834bedf7b2',
  contactId: '',
  account: '',
  syncUpTime: '',
};

const screen = {
  title: '',
  description: '',
  type: 'form',
  screenId: '',
  isSafe: false,
  color: '009E3D',
  iconUrl: '',
  properties: [],
  buttons: [],
};

const properties = {
  type: 'options',
  title: 'Project',
  selectedOption: null,
  id: 'Project_Safe',
  hint: 'Select',
  IsRequired: false,
  isSafe: false,
  iconUrl: '',
  default: '',
  options: [],
  range: {},
  parentFieldId: '',
  hasChildField: false,
  isDefaultVisible: true,
  isActive: true,
  isUnique: false,
  dependentIds: [],
};

const option = {
  title: '',
  subTitle: '',
  isVisibleForOptions: [],
  isVisibleForMultiAssetOptions: [],
  id: '',
  iconUrl: '',
  notInList: false,
  hasEmptyValue: true,
  color: '',
  isDefault: false,
  currency: '',
  dependentFieldIds: [],
};

const buttons = {
  title: 'Submit',
  color: '009E3D',
  action: null,
};

const transactionOptions = {
  readConcern: { level: 'snapshot' },
  writeConcern: { w: 'majority' },
  readPreference: 'primary',
};

const safetyCardOrder = ['safe', 'unsafe', 'ncr', 'incident'];
const safetyCardStatusOrder = ['open', 'submitted', 'in_discussion', 'closed', 'archived'];
const orderStatusArray = ['requested', 'approved', 'pre-transit'];

module.exports = {
  config,
  screen,
  properties,
  option,
  buttons,
  transactionOptions,
  safetyCardOrder,
  safetyCardStatusOrder,
  orderStatusArray,
};
