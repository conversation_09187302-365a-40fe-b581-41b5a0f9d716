const locationService = require('../services/location.service');
const projectStringService = require('../services/project-string.service');
const assetService = require('../services/asset.service');
const scopeService = require('../services/scope.service');
const activityService = require('../services/activity.service');
const functionService = require('../services/function.service');

/**
 * Create Default Project Setup
 */
exports.createDefaultProjectSetup = async requestData => {
  try {
    let { account, project, isDefault } = requestData;
    await this.createDefaultLocation(account, project, isDefault);
    await this.createDefaultProjectString(account, project, isDefault);
    await this.createDefaultAsset(account, project, isDefault);
    await this.createDefaultScope(account, project, isDefault);
    await this.createDefaultActivity(account, project, isDefault);
    await this.createDefaultFunction(account, project, isDefault);
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Create Default Location
 *
 * @param {*} account
 * @param {*} project
 * @param {*} isDefault
 * @returns
 */
exports.createDefaultLocation = async (account, project, isDefault) => {
  let locations = [
    {
      title: process.env.DEFAULT_LOCATION_A.replace(/_/g, ' '),
      account: account._id,
      project,
      isDefault,
      defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
      isDeletable: false,
    },
    {
      title: process.env.DEFAULT_LOCATION_B.replace(/_/g, ' '),
      account: account._id,
      project,
      isDefault,
      defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
      isDeletable: false,
    },
  ];

  return await locationService.insertManyLocations(locations);
};

/**
 * Create Default Project String
 *
 * @param {*} account
 * @param {*} project
 * @param {*} isDefault
 * @returns
 */
exports.createDefaultProjectString = async (account, project, isDefault) => {
  const filter = {
    account: account._id,
    project,
  };

  let defaultLocation = await locationService.getDefaultLocation(filter);

  let projectString = {
    name: process.env.DEFAULT_STRING.replace(/_/g, ' '),
    fromLocation: defaultLocation[0]._id,
    toLocation: defaultLocation[1]._id,
    project,
    account: account._id,
    isDefault,
    defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
    isDeletable: false,
  };
  return await projectStringService.createProjectString(projectString);
};

/**
 * Create Default Asset
 *
 * @param {*} account
 * @param {*} project
 * @param {*} isDefault
 * @returns
 */
exports.createDefaultAsset = async (account, project, isDefault) => {
  const filter = {
    account: account._id,
    project,
    defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
  };
  let defaultLocation = await locationService.getDefaultLocation(filter);
  let projectStringFilter = {
    account: account._id,
    project,
    defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
  };
  let defaultProjectString = await projectStringService.getDefaultProjectString(
    projectStringFilter
  );

  let defaultAsset = {
    cableName: process.env.DEFAULT_ASSET.replace(/_/g, ' '),
    project,
    fromLocation: defaultLocation[0]._id,
    toLocation: defaultLocation[1]._id,
    manufacturer: 'Other',
    typeMm2: 'Other',
    string: defaultProjectString._id,
    account: account._id,
    isDefault,
    defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
    isDeletable: false,
  };

  return await assetService.createAsset(defaultAsset);
};

/**
 * Create Default Scope
 *
 * @param {*} account
 * @param {*} project
 * @param {*} isDefault
 * @returns
 */
exports.createDefaultScope = async (account, project, isDefault) => {
  let defaultScope = {
    name: process.env.DEFAULT_SCOPE.replace(/_/g, ' '),
    isDoable: true,
    normDuration: '1',
    sortOrder: 1,
    project,
    account: account._id,
    isDefault,
    defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
    isDeletable: false,
  };

  return await scopeService.createScope(defaultScope);
};

/**
 * Create Default Activity
 *
 * @param {*} account
 * @param {*} project
 * @param {*} isDefault
 * @returns
 */
exports.createDefaultActivity = async (account, project, isDefault) => {
  const filter = {
    account: account._id,
    project,
  };
  let defaultScope = await scopeService.getDefaultScope(filter);

  let defaultActivity = {
    name: process.env.DEFAULT_ACTIVITY.replace(/_/g, ' '),
    scopeId: defaultScope._id,
    weight: 1,
    project,
    account: account._id,
    isDefault,
    defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
    isDeletable: false,
  };

  return await activityService.createActivity(defaultActivity);
};

/**
 * Create Default Function
 *
 * @param {*} account
 * @param {*} project
 * @param {*} isDefault
 * @returns
 */
exports.createDefaultFunction = async (account, project, isDefault) => {
  let defaultFunction = {
    functionName: process.env.DEFAULT_FUNCTION.replace(/_/g, ' '),
    project,
    account: account._id,
    isDefault,
    defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER,
    isDeletable: false,
  };

  return await functionService.createFunction(defaultFunction);
};
