const PDFDocument = require('pdfkit-table');
const axios = require('axios');
const fs = require('fs');
const html_to_pdf = require('html-pdf-node');

/**
 * Generates a PDF from the given HTML content and sends it as a response.
 * @param {string} htmlContent - The HTML content to generate the PDF from.
 * @param {object} res - The Express response object.
 * @param {object} options - Options for PDF generation.
 * @returns {Promise<void>}
 */
exports.generateAndExportPdf = async (htmlContent, res, options) => {
  try {
    const file = { content: htmlContent };
    const pdfBuffer = await html_to_pdf.generatePdf(file, options);

    // Send PDF as response
    res.contentType('application/pdf');
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).send(error.toString());
  }
};

exports.exportPDFTable = async (
  res,
  fileName,
  columns,
  rows,
  label,
  fontSize = 20,
  columnsSize = []
) => {
  try {
    const imgLogo = await axios.get(global.constant.APP_LOGO_PATH, {
      responseType: 'arraybuffer',
    });
    let doc = new PDFDocument({ margin: 20, size: 'A4' });
    // to save on server
    doc.pipe(fs.createWriteStream('./uploads/document.pdf'));
    doc
      .font('Helvetica-Bold')
      .text(label, 20, 20)
      .fontSize(fontSize)
      .image(imgLogo.data, 500, 15, { width: 70, align: 'right' })
      .fontSize(20);

    const table = {
      headers: columns,
      rows: rows,
      addPage: true,
    };

    // add page
    if (doc.y > 0.8 * doc.page.height) {
      doc.addPage({ margin: 20, size: 'A4' });
    }

    doc.table(table, {
      columnSpacing: 5,
      minRowHeight: 30,
      padding: 3,
      y: 50,
      align: 'left',
      wordWrap: true,
      prepareHeader: () => doc.font('Helvetica-Bold').fontSize(8).fillColor('white'),
      prepareRow: (row, indexColumn, indexRow, rectRow, rectCell) => {
        doc.font('Helvetica').fontSize(7);
        doc.fillColor('black');
        if (
          !rectCell ||
          isNaN(rectCell.x) ||
          isNaN(rectCell.y) ||
          isNaN(rectCell.width) ||
          isNaN(rectCell.height)
        ) {
          return;
        }
        indexColumn === 0 && doc.addBackground(rectRow, indexRow % 2 ? '#F7FBFF' : '#FFFFFF', 0.9);
        let { x, y, width, height } = rectCell;
        let extraVal = 0.5;

        if (indexRow === 0 && indexColumn > 0 && indexColumn < row.length) {
          doc.strokeColor('white', 0.5).lineWidth(0).moveTo(x, 40).lineTo(x, y).stroke();
          doc
            .strokeColor('black')
            .lineWidth(0)
            .moveTo(x, y)
            .lineTo(x, y + height)
            .stroke();
        }

        // first line
        if (indexColumn === 0) {
          doc
            .lineWidth(0)
            .moveTo(x + extraVal, y + extraVal)
            .lineTo(x + extraVal, y + extraVal + height)
            .stroke();
        }

        if (indexColumn === row.length - 1) {
          doc
            .lineWidth(0)
            .moveTo(x + width - extraVal, y)
            .lineTo(x + width - extraVal, y + height - extraVal)
            .stroke();
        } else {
          doc
            .lineWidth(0)
            .moveTo(x + width, y)
            .lineTo(x + width, y + height)
            .strokeColor('black')
            .stroke();
        }
      },
      columnsSize,
    });

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=${fileName}.pdf`);
    doc.pipe(res);
    doc.end();
  } catch (err) {
    throw new Error(err);
  }
};

/**
 * Generate report pdf
 *
 * @param {*} res
 * @param {*} tables
 * @param {*} fileName
 * @param {*} label
 * @param {*} tableWidth
 */
exports.exportReportPDF = async (res, tables, fileName, label, tableWidth) => {
  try {
    const imgLogo = await axios.get(global.constant.APP_LOGO_PATH, {
      responseType: 'arraybuffer',
    });
    let doc = new PDFDocument({ margin: 40, size: 'A4' });
    let fistXPos = 0.5;
    // to save on server
    doc.pipe(fs.createWriteStream('./uploads/document.pdf'));
    doc
      .font('Helvetica-Bold')
      .text(label, 20, 20)
      .fontSize(40)
      .image(imgLogo.data, 500, 15, { width: 70, align: 'right' })
      .fontSize(40);

    //Static table1
    doc.table(tables.table1, {
      y: 50,
      padding: 10,
      width: tableWidth,
      minRowHeight: 10,
      columnSpacing: 8,
      prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10).fillColor('white'),
      prepareRow: (row, indexColumn, indexRow, rectRow, rectCell) => {
        doc.font('Helvetica').fontSize(9);
        doc.fillColor('black');
        let { x, y, width, height } = rectCell;

        if (indexRow === 0) {
          let fixedValX = 292.5;
          doc
            .strokeColor('white', 0.5)
            .lineWidth(0)
            .moveTo(fixedValX, 41)
            .lineTo(fixedValX, y)
            .stroke();
        }

        if (indexColumn === 0) {
          doc
            .lineWidth(0)
            .moveTo(x + fistXPos, y)
            .lineTo(x + fistXPos, y + height)
            .strokeColor('black', 1)
            .stroke();
        }

        doc
          .lineWidth(0)
          .moveTo(x + width - fistXPos, y)
          .lineTo(x + width - fistXPos, y + height)
          .strokeColor('black', 1)
          .stroke();
      },
    });

    doc.moveDown(1);

    //Static table2
    doc.table(tables.table2, {
      padding: 10,
      width: tableWidth,
      minRowHeight: 10,
      columnSpacing: 8,
      prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10).fillColor('white'),
      prepareRow: (row, indexColumn, indexRow, rectRow, rectCell) => {
        doc.font('Helvetica').fontSize(9);
        doc.fillColor('black');

        let { x, y, width, height } = rectCell;

        if (indexColumn === 0) {
          doc
            .lineWidth(0)
            .moveTo(x + fistXPos, y)
            .lineTo(x + fistXPos, y + height)
            .strokeColor('black', 1)
            .stroke();
        }

        doc
          .lineWidth(0.1)
          .moveTo(x + width - fistXPos, y)
          .lineTo(x + width - fistXPos, y + height)
          .strokeColor('black', 1)
          .stroke();
      },
    });

    doc.moveDown(1);

    //Remove static table data
    delete tables.table1;
    delete tables.table2;

    //Functionality for the l1, l2, l3 data
    Object.keys(tables).forEach(key => {
      if (doc.y > 0.8 * doc.page.height) {
        doc.addPage({ margin: 20, size: 'A4' });
      }
      let dynMinRowHeight = tables[key].datas[0].hasImage ? 100 : 10;
      let tableOptions = {
        padding: 10,
        width: tableWidth,
        minRowHeight: dynMinRowHeight,
        columnSpacing: 8,
        prepareHeader: () => doc.font('Helvetica-Bold').fontSize(10).fillColor('white'),
        prepareRow: (row, indexColumn, indexRow, rectRow, rectCell) => {
          doc.font('Helvetica').fontSize(9);
          doc.fillColor('black');

          let { x, y, width, height } = rectCell;

          if (indexColumn === 0) {
            doc
              .lineWidth(0)
              .moveTo(x + fistXPos, y)
              .lineTo(x + fistXPos, y + height)
              .strokeColor('black', 1)
              .stroke();
          }

          doc
            .lineWidth(0.1)
            .moveTo(x + width - fistXPos, y)
            .lineTo(x + width - fistXPos, y + height)
            .strokeColor('black', 1)
            .stroke();

          let cellXPosition = rectRow.x + 10;
          let cellYPosition = rectRow.y + 10;
          let cellHeight = rectRow.height - 20;
          if (Buffer.isBuffer(row.name)) {
            doc.image(row.name, cellXPosition, cellYPosition, {
              width: 100,
              height: cellHeight,
              align: 'left',
            });
            row.name = '';
          } else if (row.hasThreePhase !== false) {
            let totalLength = Object.keys(row.name).length;
            let count = 10;
            Object.keys(row.name).forEach(k => {
              rectRow.x = rectRow.x + count;
              if (Buffer.isBuffer(row.name[k])) {
                doc
                  .image(row.name[k], rectRow.x, cellYPosition + 10, {
                    width: cellHeight - 5,
                    height: cellHeight - 5,
                  })
                  .rect(rectRow.x, cellYPosition + 10, cellHeight - 5, cellHeight - 5)
                  .strokeColor('black', 0.5)
                  .stroke()
                  .text(k, rectRow.x, cellYPosition);
              } else {
                doc.text(row.name[k], rectRow.x, cellYPosition);
              }
              count = rectRow.width / totalLength;
              if (totalLength < Object.keys(row.name).length) {
                totalLength = totalLength - 1;
              }
            });
            row.name = '';
          }
        },
      };

      doc.table(tables[key], tableOptions);
      doc.moveDown(1);
    });
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=${fileName}.pdf`);
    doc.pipe(res);
    doc.end();
  } catch (err) {
    throw new Error(err);
  }
};
