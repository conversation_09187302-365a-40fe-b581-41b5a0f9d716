const crypto = require('crypto');
const rateLimit = require('express-rate-limit');

/**
 * Rate limiter middleware using express-rate-limit - internal rate limiting without exposing details
 */
exports.rateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 2, // limit each IP to 2 requests per windowMs
  message: {
    status: false,
    message: 'Too many requests. Please try again later.',
  },
  standardHeaders: false,
  legacyHeaders: false,
  handler: (_, res) => {
    res.status(429).json({
      status: false,
      message: 'Too many requests. Please try again later.',
    });
  },
});

/**
 * Basic authentication middleware for health endpoint
 * Uses environment variables for credentials
 */
exports.basicAuth = (req, res, next) => {
  const healthUsername = process.env.HEALTH_USERNAME;
  const healthPassword = process.env.HEALTH_PASSWORD;

  // Get authorization header
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Basic ')) {
    res.set('WWW-Authenticate', 'Basic realm="Health Endpoint"');
    return res.status(401).json({
      status: false,
      message: 'Authentication required. Use Basic Auth with username and password.',
      hint: 'Authorization: Basic <base64(username:password)>',
    });
  }

  try {
    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
    const [username, password] = credentials.split(':');

    // Verify credentials using constant-time comparison to prevent timing attacks
    const usernameMatch = crypto.timingSafeEqual(
      Buffer.from(username),
      Buffer.from(healthUsername)
    );
    const passwordMatch = crypto.timingSafeEqual(
      Buffer.from(password),
      Buffer.from(healthPassword)
    );

    if (usernameMatch && passwordMatch) {
      next();
    } else {
      res.set('WWW-Authenticate', 'Basic realm="Health Endpoint"');
      return res.status(401).json({
        status: false,
        message: 'Invalid credentials',
      });
    }
  } catch (error) {
    res.set('WWW-Authenticate', 'Basic realm="Health Endpoint"');
    return res.status(401).json({
      status: false,
      message: 'Invalid authorization header format',
    });
  }
};
