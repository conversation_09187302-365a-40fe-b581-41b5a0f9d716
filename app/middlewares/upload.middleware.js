const multer = require('multer');

exports.upload = multer({
  limits: {
    fileSize: global.constant.UPLOAD_FILE_SIZE,
  },
  fileFilter(req, file, cb) {
    if (!file.originalname.match(/\.(jpg|jpeg|png|svg|pdf|jfif|webp)$/)) {
      return cb(new Error('Please upload image file'));
    }
    cb(undefined, true);
  },
});

exports.verifyFile = (req, res, next) => {
  const uploadSingleImage = this.upload.single('file');

  uploadSingleImage(req, res, function (err) {
    if (err) {
      return this.returnFileValidationError(err, res);
    }
    return next();
  });
};

exports.verifyBulkFiles = (req, res, next) => {
  const uploadMultipleImages = this.upload.array('files', 5);

  uploadMultipleImages(req, res, function (err) {
    if (err) {
      return this.returnFileValidationError(err, res);
    }
    return next();
  });
};

exports.returnFileValidationError = (err, res) => {
  return res.status(400).json({
    status: false,
    message: err.message,
  });
};
