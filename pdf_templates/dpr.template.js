exports.dprPdfTemplate = async templateData => {
  const {
    reportingPeriod,
    date,
    dprNumber,
    project,
    client,
    version,
    progressData,
    qhse,
    applicableDocuments,
    timeAnalysis,
    personnelList,
    equipmentList,
    commentsAndSignatures,
  } = templateData.requestData;

  // Generate all sections using helper functions
  const sections = {
    contactDetails: generateContactDetails(progressData.dprMembers || []),
    progressSummary: generateProgressSummary(progressData.progressSummary),
    detailedProgress: generateDetailedProgressSection(progressData?.detailProgress),
    qhseType: generateQhseTypeSection(qhse.qhseSummary),
    applicableDocs: generateApplicableDocuments(
      applicableDocuments.applicableDocumentsSummary.data
    ),
    detailedTimeAnalysis: generateDetailedTimeAnalysis(timeAnalysis.detailedTimeAnalysis),
    dailyActivityLogs: generateDailyActivityLogs(timeAnalysis.dailyActivityLog),
    personnelList: generatePersonalList(personnelList.personnelList),
    equipmentList: generateEquipmentList(equipmentList.equipmentList),
  };

  return `
  <!DOCTYPE html>
<html lang="en">
   <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Daily Progress Report</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 850px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
      }

      .activities-list {
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-height: auto;
        font-size: 10px;
      }

      .title {
        font-size: 32px;
        font-weight: bold;
        color: var(--primary-color);
      }

      .version {
        color: #666;
      }

      .form-row {
        display: flex;
        margin-bottom: 20px;
        gap: 20px;
      }

      .form-group {
        flex: 1;
      }

      .form-group label {
        display: block;
        font-weight: bold;
        margin-bottom: 5px;
        color: #333;
      }

      .form-group input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-sizing: border-box;
      }

      .text-line {
        display: flex;
        align-items: center;
        margin-top: 20px;
      }

      html {
        box-sizing: border-box;
      }

      *,
      *::before,
      *::after {
        box-sizing: inherit;
      }

      :root {
        --primary-color: #191a51;
        --primary-font-color: #323232;
        --info-title-color: #4d5464;
        --info-desc-color: #333843;
        --status-color-green: #2a8c00;
        --status-color-red: #9d0202;
        --circle-bg-color: #d9d9d9;
        --black-color: #000000;
        --info-label-color: #ffffff;
        --table-border: #e0e6f5;
        --table-header-border: #e0e6f51a;
        --white-color: #ffffff;
        --pdf-bg-color: #f6f7ff;
        --status-color-orange: #ff6600;
        --status-color-blue: #191a51;
      }

      .main {
        width: 100%;
      }

      .pdf-header {
        display: flex;
        width: 100%;
      }

      .pdf-header-title {
        font-size: 16px;
        padding: 0;
        margin: 0;
        margin-bottom: 10px;
        font-weight: 600;
      }

      .space-container {
        height: 10px;
      }

      .custom-table {
        width: 100%;
        border-spacing: 0px;
        border-radius: 4px;
      }

      .custom-table tr {
        page-break-inside: avoid;
        background-color: white !important;
      }

      .custom-table th {
        text-align: left;
        padding: 6px 8px;
        background-color: var(--primary-color);
        color: var(--white-color);
        font-size: 12px;
      }

      .custom-table td {
        padding: 6px 8px;
        font-size: 10px;
      }

      .custom-table th:first-child {
        border-right: 1px solid var(--table-border);
        border-top-left-radius: 4px;
      }

      .custom-table td:first-child {
        border-right: 1px solid var(--table-border);
        border-bottom: 1px solid var(--table-border);
        border-left: 1px solid var(--table-border);
      }

      .custom-table th:not(:first-child):not(:last-child) {
        border-right: 1px solid var(--table-border);
      }

      .custom-table td:not(:first-child):not(:last-child) {
        border-right: 1px solid var(--table-border);
        border-bottom: 1px solid var(--table-border);
      }

      .custom-table th:last-child {
        border-right: 0px;
        border-top-right-radius: 4px;
      }

      .custom-table td:last-child {
        border-right: 1px solid var(--table-border);
        border-bottom: 1px solid var(--table-border);
      }

      .custom-table tr:last-child td:first-child {
        border-bottom-left-radius: 4px;
      }

      .custom-table tr:last-child td:last-child {
        border-bottom-right-radius: 4px;
      }

      .safety-table-header {
        padding: 6px 8px;
        font-size: 12px;
        font-weight: 600;
      }

      .safety-first-table-desc {
        padding: 6px 8px;
        font-size: 10px;
        font-weight: 500;
      }

      .text-line p {
        margin: 0;
        padding-right: 10px;
        white-space: nowrap;
        font-size: 16px;
        font-weight: 600;
        color: var(--primary-color);
      }

      .text-line hr {
        flex-grow: 1;
        border: none;
        border-top: 1px solid var(--primary-color);
        margin: 0;
        margin-left: 5px;
      }

      .remarks-section {
        margin-top: 15px;
      }

      .remarks-title {
        color: #2d1b69;
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 8px;
      }

      .remarks-content {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 12px;
        min-height: auto;
        background-color: white;
        color: #666;
        font-size: 10px;
      }

      table {
        width: 100%;
        border-collapse: collapse;
      }

      th,
      td {
        border: 1px solid #ddd;
        padding: 6px 8px;
        text-align: left;
      }

      th {
        background-color: var(--primary-color);
        color: white;
        font-size: 12px;
      }

      td {
        font-size: 10px;
        background-color: white;
      }

      .comment-remark-name-signature {
        background-color: white;
        color: var(--primary-color);
        font-size: 12px;
      }

      /* Updated Personal List table with new columns */
      .personal-list th[data-type='status'] {
        text-align: center;
      }

      .personal-list td[data-type='status'] {
        text-align: center;
      }

      .personal-list th[data-type='travel-day'],
      .personal-list td[data-type='travel-day'] {
        text-align: center;
      }
    </style>
  </head>

  <body>
    <div class="header">
      <div class="title">DPR</div>
      <div class="version">${version}</div>
    </div>

    <table class="custom-table">
      <tr>
        <th class="safety-table-header">Reporting Period</th>
        <th class="safety-table-header">Date</th>
        <th class="safety-table-header">DPR No.</th>
      </tr>

      <tr>
        <td class="safety-first-table-desc">${reportingPeriod ?? 'N/A'}</td>
        <td class="safety-first-table-desc">${date.split('T')[0]}</td>
        <td class="safety-first-table-desc">${dprNumber}</td>
      </tr>
    </table>

    <br>

    <table class="custom-table">
      <tr>
        <th class="safety-table-header">Client</th>
        <th class="safety-table-header">Project</th>
      </tr>

      <tr>
        <td class="safety-first-table-desc">${client}</td>
        <td class="safety-first-table-desc">${project}</td>
      </tr>
    </table>

    <!-- Contact details site section -->
    <div class="text-line">
      <p>Contact details site</p>
      <hr />
    </div>
    <div class="space-container"></div>
    <table class="custom-table">
      <tr>
        <th class="safety-table-header">Function</th>
        <th class="safety-table-header">Name</th>
        <th class="safety-table-header">E-mail address</th>
        <th class="safety-table-header">Telephone Number</th>
      </tr>

      ${sections.contactDetails}
    </table>

    <div class="text-line">
      <p>Activities of the last 24 hrs</p>
      <hr />
    </div>
    <div class="space-container"></div>
    ${
      progressData.last24Hours
        ? `<div class="activities-list">${progressData.last24Hours}</div>`
        : ''
    }

    <div class="text-line">
      <p>Planned activities next 24 hrs</p>
      <hr />
    </div>
    <div class="space-container"></div>
    ${
      progressData.next24Hours
        ? `<div class="activities-list">${progressData.next24Hours}</div>`
        : ''
    }

    <!-- Progress summary section -->
    <div class="text-line">
      <p>Progress summary</p>
      <hr />
    </div>
    <div class="space-container"></div>
    ${sections.progressSummary}

    <!-- Detailed progress section -->
    <div class="text-line">
      <p>Detailed Progress</p>
      <hr />
    </div>
    <div class="space-container"></div>
    <table>
      <thead>
        <tr>
          <th class="location">Location</th>
          <th class="report">Report</th>
          <th class="completed-tasks">Completed Tasks</th>
        </tr>
      </thead>
      <tbody>
        ${sections.detailedProgress}
      </tbody>
    </table>
    ${renderRemarksSection(progressData.remarks)}

    <div class="text-line">
      <p>QHSE</p>
      <hr />
    </div>
    <div class="space-container"></div>
    <table class="custom-table">
      <tr>
        <th class="safety-table-header">QHSE</th>
        <th class="safety-table-header">Previous</th>
        <th class="safety-table-header">Today</th>
        <th class="safety-table-header">Total</th>
        <th class="safety-table-header">Open</th>
      </tr>

      <tr>
        <td>Type</td>
      </tr>
      ${sections.qhseType}
    </table>
    ${renderRemarksSection(qhse.remarks)}

    <!-- Applicable documents section -->
    <div class="text-line">
      <p>Applicable Documents</p>
      <hr />
    </div>
    <div class="space-container"></div>
    <table class="custom-table">
      ${sections.applicableDocs}
    </table>
    ${renderRemarksSection(applicableDocuments.remarks)}

    <!-- Detailed time analysis section -->
    <div class="text-line">
      <p>Detailed Time Analysis</p>
      <hr />
    </div>
    <div class="space-container"></div>
    <table class="custom-table">
      <tr>
        <th class="safety-table-header">Activity</th>
        <th class="safety-table-header">Previous - Hours</th>
        <th class="safety-table-header">Today - Hours</th>
        <th class="safety-table-header">Cumulative - Hours</th>
      </tr>

      ${sections.detailedTimeAnalysis}
    </table>
    ${renderRemarksSection(timeAnalysis.remarks)}

    <!-- Daily activity section -->
    ${
      sections.dailyActivityLogs != ''
        ? '<div class="text-line"><p>Daily Activity Logs</p><hr /></div><div class="space-container"></div>'
        : ''
    }
    ${sections.dailyActivityLogs}

     <!-- Personal list section -->
    <div class="text-line">
      <p>Personal List</p>
      <hr />
    </div>
    <div class="space-container"></div>
    <table>
      <thead>
        <tr>
          <th>No.</th>
          <th>Name</th>
          <th>Job Title</th>
          <th>Team</th>
          <th colspan="2" style="text-align: center">Day</th>
          <th colspan="2" style="text-align: center">Status</th>
        </tr>
        <tr>
          <th colspan="4"></th>
          <th>Travel</th>
          <th>Working</th>
          <th>On</th>
          <th>Off</th>
        </tr>
      </thead>

      <tbody>
        ${sections.personnelList}
      </tbody>
    </table>
    ${renderRemarksSection(personnelList.remarks)}

    <!-- Equipment section -->
    <div class="text-line">
      <p>Equipment</p>
      <hr />
    </div>
    <div class="space-container"></div>
    <table>
      <thead>
        <tr>
          <th>Product Type</th>
          <th>Product Name</th>
          <th>Product Number</th>
          <th>Serial Number</th>
        </tr>
      </thead>

      <tbody>
        ${sections.equipmentList}
      </tbody>
    </table>
    ${renderRemarksSection(equipmentList.remarks)}

    <!-- Comments section -->
    <div class="text-line">
      <p>Comments / Remarks</p>
      <hr />
    </div>
    <div class="space-container"></div>
    <table>
      <tr>
        <th style="width: 40%" class="comment-remark-name-signature" style="${
          commentsAndSignatures.commentOrRemarks.endClientRepresentative
            ? 'height: auto;'
            : 'height: 50px;'
        }">End Client Representative</th>
        <td>${commentsAndSignatures.commentOrRemarks.endClientRepresentative}</td>
      </tr>
      <tr>
        <th class="comment-remark-name-signature" style="${
          commentsAndSignatures.commentOrRemarks.clientRepresentative
            ? 'height: auto;'
            : 'height: 50px;'
        }">Client Representative</th>
        <td>${commentsAndSignatures.commentOrRemarks.clientRepresentative}</td>
      </tr>
      <tr>
        <th class="comment-remark-name-signature" style="${
          commentsAndSignatures.commentOrRemarks.reynardRepresentative
            ? 'height: auto;'
            : 'height: 50px;'
        }">Reynard Representative</th>
        <td>${commentsAndSignatures.commentOrRemarks.reynardRepresentative}</td>
      </tr>
    </table>

    <!-- Name and Signatures section -->
    <div class="text-line">
      <p>Name and Signatures</p>
      <hr />
    </div>
    <div class="space-container"></div>
    <table>
      <tr>
        <th style="width: 40%" class="comment-remark-name-signature">End Client Representative</th>
        <td> <img
            src="${commentsAndSignatures.namesAndSignature.endClientRepresentative.url}"
            alt=""
            width="200"
            height="auto"
          /></td>
      </tr>
      <tr>
        <th class="comment-remark-name-signature">Client Representative</th>
        <td> <img
            src="${commentsAndSignatures.namesAndSignature.clientRepresentative.url}"
            alt=""
            width="200"
            height="auto"
          /></td>
      </tr>
      <tr>
        <th class="comment-remark-name-signature">Reynard Representative</th>
        <td> <img
            src="${commentsAndSignatures.namesAndSignature.reynardRepresentative.url}"
            alt=""
            width="200"
            height="auto"
          /></td>
      </tr>
    </table>

    </body>
</html>
  `;
};

/**
 * Generate contact details table rows
 * @param {Array} dprMembers - Array of member objects
 * @returns {string} - HTML for contact details rows
 */
function generateContactDetails(dprMembers) {
  return dprMembers
    .map(
      member => `
    <tr>
      <td class="safety-first-table-desc">${member.function ?? '-'}</td>
      <td class="safety-first-table-desc">${member.name}</td>
      <td class="safety-first-table-desc">${member.email}</td>
      <td class="safety-first-table-desc">${member.phone}</td>
    </tr>
  `
    )
    .join('');
}

/**
 * Calculate completions for a location group
 * @param {Object} locationGroup - Location group data
 * @param {Object} scopes - Scopes data
 * @returns {Object} - Object with scope completions and overall completion
 */
function calculateCompletions(locationGroup, scopes) {
  const scopeCompletions = Object.keys(scopes).map(scopeName => {
    const scopeReports = scopes[scopeName].reports;
    const locationReports = locationGroup.reportList;
    let totalCompletion = 0;
    let reportCount = 0;

    Object.entries(scopeReports).forEach(([reportTitle]) => {
      if (locationReports[reportTitle]) {
        totalCompletion += locationReports[reportTitle].completion || 0;
        reportCount++;
      }
    });

    return reportCount > 0 ? totalCompletion / reportCount : 0;
  });

  const validCompletions = scopeCompletions.filter(comp => comp > 0);
  const overallCompletion =
    validCompletions.length > 0
      ? (validCompletions.reduce((a, b) => a + b, 0) / validCompletions.length).toFixed(0)
      : '0';

  return { scopeCompletions, overallCompletion };
}

/**
 * Generate a row for a location with no assets
 * @param {Object} locationGroup - Location group data
 * @param {Object} scopes - Scopes data
 * @returns {string} - HTML for empty location row
 */
function generateEmptyLocationRow(locationGroup, scopes) {
  return `
    <tr>
      <td class="location">${locationGroup.title}</td>
      <td class="report">-</td>
      ${Object.keys(scopes)
        .map(scopeName => {
          const scopeReports = scopes[scopeName].reports;
          const totalWeightage = scopes[scopeName].weightage;
          let totalWeightedCompletion = 0;

          Object.entries(scopeReports).forEach(([reportData]) => {
            const reportCompletion = reportData.completion || 0;
            const reportWeightage = reportData.weightage || 0;
            totalWeightedCompletion += (reportCompletion * reportWeightage) / 100;
          });

          const completion =
            totalWeightage > 0 ? totalWeightedCompletion / (totalWeightage / 100) : 0;

          return `<td class="completed-tasks">${completion.toFixed(0)}%</td>`;
        })
        .join('')}
      <td class="completed-tasks">0%</td>
    </tr>
  `;
}

/**
 * Generate progress summary section
 * @param {Object} data - Progress summary data
 * @returns {string} - HTML for progress summary section
 */
function generateProgressSummary(data) {
  // Calculate scope data
  const scopes = data.scopeData.reduce((acc, scope) => {
    acc[scope.name] = {
      weightage: scope.reports.reduce((sum, report) => sum + (report.weightage || 0), 0),
      reports: scope.reports.reduce((reportsAcc, report) => {
        reportsAcc[report.title] = {
          completion: report.reportCompletion || 0,
          weightage: report.weightage || 0,
        };
        return reportsAcc;
      }, {}),
    };
    return acc;
  }, {});

  // Build location map
  // eslint-disable-next-line no-undef
  const locationMap = new Map();
  data.projectTrackerData.forEach(location => {
    if (!locationMap.has(location.locations)) {
      locationMap.set(location.locations, {
        title: location.locations,
        assets: location.assets.map(asset => asset.title),
        reportList: location.reportList.reduce((acc, report) => {
          acc[report.title] = {
            completion: report.reportList[0].completion || 0,
          };
          return acc;
        }, {}),
      });
    }
  });

  // Generate table body
  let tableBody = '';
  let totalCompletions = [];

  locationMap.forEach(locationGroup => {
    const rowCount = locationGroup.assets.length || 1;

    if (locationGroup.assets.length === 0) {
      tableBody += generateEmptyLocationRow(locationGroup, scopes);
    } else {
      locationGroup.assets.forEach((asset, index) => {
        const { scopeCompletions, overallCompletion } = calculateCompletions(locationGroup, scopes);
        totalCompletions.push(parseInt(overallCompletion));

        tableBody += '<tr>';
        if (index === 0) {
          tableBody += `<td class="location" rowspan="${rowCount}">${locationGroup.title}</td>`;
        }
        tableBody += `
          <td class="report">${asset}</td>
          ${scopeCompletions
            .map(completion => `<td class="completed-tasks">${completion.toFixed(0)}%</td>`)
            .join('')}
          <td class="completed-tasks">${overallCompletion}%</td>
        </tr>`;
      });
    }
  });

  // Add total completion row
  const overallCompletion = data.totalCompletions ? data.totalCompletions.toFixed(0) : '0';
  tableBody += `
    <tr>
      <td colspan="${2 + Object.keys(scopes).length}" style="text-align: right;">
        <strong>Total Completion</strong>
      </td>
      <td class="completed-tasks"><strong>${overallCompletion}%</strong></td>
    </tr>
  `;

  return `
    <table class="custom-table">
      <thead>
        <tr>
          <th class="safety-table-header">Location</th>
          <th class="safety-table-header">Asset</th>
          ${Object.keys(scopes)
            .map(scopeName => `<th class="safety-table-header">${scopeName}</th>`)
            .join('')}
          <th class="safety-table-header">Overall</th>
        </tr>
      </thead>
      <tbody>
        ${tableBody}
      </tbody>
    </table>
  `;
}

/**
 * Generate task list for detailed progress
 * @param {Array} questions - Array of question objects
 * @returns {string} - HTML list of tasks
 */
function generateTasksList(questions) {
  if (!questions || !questions.length) {
    return 'No tasks available';
  }
  return questions.map(question => question.title).join('<br />');
}

/**
 * Generate detailed progress section
 * @param {Array} data - Array of location objects
 * @returns {string} - HTML for detailed progress section
 */
function generateDetailedProgressSection(data) {
  return data
    .map(location => {
      const locationRow = `
      <tr>
        <td colspan="3">${location.title}</td>
      </tr>
    `;

      const assetRows = location.assets
        .map(asset => {
          const hasReports = asset.assetsReports && asset.assetsReports.length > 0;

          if (!hasReports) {
            return `
          <tr>
            <td class="location">${asset.cableName}</td>
            <td class="report">No reports available</td>
            <td class="completed-tasks">No tasks completed</td>
          </tr>
        `;
          }

          return asset.assetsReports
            .map((report, reportIndex) => {
              if (reportIndex === 0) {
                return `
            <tr>
              <td class="location" rowspan="${asset.assetsReports.length}">${asset.cableName}</td>
              <td class="report">${report.title}</td>
              <td class="completed-tasks">${generateTasksList(asset.reportQuestions)}</td>
            </tr>
          `;
              } else {
                return `
            <tr>
              <td class="report">${report.title}</td>
              <td class="completed-tasks">${generateTasksList(asset.reportQuestions)}</td>
            </tr>
          `;
              }
            })
            .join('');
        })
        .join('');

      return locationRow + assetRows;
    })
    .join('');
}

/**
 * Generate QHSE type section rows
 * @param {Array} qhseData - Array of QHSE type data
 * @returns {string} - HTML for QHSE type rows
 */
function generateQhseTypeSection(qhseData) {
  // Use slice to avoid modifying the last element (which is incidents)
  return qhseData
    .slice(0, -1)
    .map(
      item => `
    <tr>
      <td class="safety-first-table-desc">${item.type}</td>
      <td class="safety-first-table-desc">${item.previous == 0 ? '-' : item.previous}</td>
      <td class="safety-first-table-desc">${item.today == 0 ? '-' : item.today}</td>
      <td class="safety-first-table-desc">${item.total == 0 ? '-' : item.total}</td>
      <td class="safety-first-table-desc">${item.open == 0 ? '-' : item.open}</td>
    </tr>
  `
    )
    .join('');
}

/**
 * Convert snake_case to Title Case
 * @param {string} type - Snake case string to format
 * @returns {string} - Title case string
 */
function formatTypeName(type) {
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Format date to a readable format
 * @param {string} dateString - Date string to format
 * @returns {string} - Formatted date string
 */
function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

/**
 * Generate applicable documents section
 * @param {Array} documentsData - Array of document type objects
 * @returns {string} - HTML for applicable documents section
 */
function generateApplicableDocuments(documentsData) {
  if (!Array.isArray(documentsData)) return '';

  return documentsData
    .map(docType => {
      if (!docType.type || !Array.isArray(docType.documents) || docType.documents.length === 0) {
        return '';
      }

      const formattedTitle = formatTypeName(docType.type);
      const documentRows = docType.documents
        .map(
          doc => `
      <tr>
        <td class="safety-first-table-desc">${doc.name}</td>
        <td class="safety-first-table-desc">${doc.documentNumber}</td>
        <td class="safety-first-table-desc">${doc.version || 'N/A'}</td>
        <td class="safety-first-table-desc">${formatDate(doc.date)}</td>
      </tr>
    `
        )
        .join('');

      return `
      <tr>
        <td colspan="4" class="safety-table-header" style="background-color: #f8f9ff; color: #191a51; font-weight: bold;">${formattedTitle}</td>
      </tr>
      ${documentRows}
    `;
    })
    .join('');
}

/**
 * Generate detailed time analysis section
 * @param {Array} timeAnalysisData - Array of time analysis objects
 * @returns {string} - HTML for detailed time analysis section
 */
function generateDetailedTimeAnalysis(timeAnalysisData) {
  let sections = [];
  let totalReportedTimeRow = '';

  timeAnalysisData.forEach(scopeWiseActivity => {
    const scopeName = Object.keys(scopeWiseActivity)[0];
    const activityData = Object.values(scopeWiseActivity)[0];

    if (scopeName === 'totalReportedTime') {
      totalReportedTimeRow = `
      <tr>
        <td><strong>Total Reported Time</strong></td>
        <td class="safety-first-table-desc">${activityData.totalPrevious}</td>
        <td class="safety-first-table-desc">${activityData.totalToday}</td>
        <td class="safety-first-table-desc">${activityData.totalCumulative}</td>
      </tr>
      `;
    } else {
      sections.push(`
        <tr>
          <td colspan="4"><strong>${scopeName}</strong></td>
        </tr>
        ${activityData
          .map(
            activity => `
          <tr>
            <td class="safety-first-table-desc">${activity.name}</td>
            <td class="safety-first-table-desc">${activity.previous}</td>
            <td class="safety-first-table-desc">${activity.today}</td>
            <td class="safety-first-table-desc">${activity.cumulative}</td>
          </tr>
        `
          )
          .join('')}
      `);
    }
  });

  return sections.join('') + totalReportedTimeRow;
}

/**
 * Generate daily activity logs section
 * @param {Array} dailyActivityData - Array of daily activity log objects
 * @returns {string} - HTML for daily activity logs section
 */
function generateDailyActivityLogs(dailyActivityData) {
  return dailyActivityData
    .map(
      activityLogs => `
    <table>
      <thead>
        <tr>
          <th colspan="2">${activityLogs.team}</th>
          <th colspan="1">${activityLogs.totalManHours}</th>
          <th colspan="1">${activityLogs.totalTeamHours}</th>
          <th colspan="3">${activityLogs.teamSize} pax</th>
        </tr>
        <tr>
          <th>Start Time</th>
          <th>End Time</th>
          <th>Man Hours</th>
          <th>Team Hours</th>
          <th>Activity</th>
          <th>Location</th>
          <th>Remarks</th>
        </tr>
      </thead>
      <tbody>
        ${activityLogs.shiftActivities
          .map(
            log => `
          <tr>
            <td>${log.startTime.split('T')[1]}</td>
            <td>${log.endTime.split('T')[1]}</td>
            <td>${log.manHours}</td>
            <td>${log.teamHours}</td>
            <td>${log.activityName}</td>
            <td>${log.location}</td>
            <td>${log.remarks ?? '-'}</td>
          </tr>
        `
          )
          .join('')}
      </tbody>
    </table>
    <br>
  `
    )
    .join('');
}

/**
 * Generate personnel list table rows
 * @param {Array} personnelData - Array of personnel objects
 * @returns {string} - HTML for personnel list rows
 */
function generatePersonalList(personnelData) {
  return personnelData
    .map(
      (person, index) => `
    <tr>
      <td>${index + 1}</td>
      <td>${person.name}</td>
      <td>${person.jobTitle ?? '-'}</td>
      <td>${person.team ?? '-'}</td>
      <td>${person.isWorking === false ? '&#10004;' : '-'}</td>
      <td>${person.isWorking === true ? '&#10004;' : '-'}</td>
      ${person.status === true ? '<td>&#10004;</td>' : '<td>-</td>'}
      ${person.status === true ? '<td>-</td>' : '<td>&#10004;</td>'}
    </tr>
  `
    )
    .join('');
}

/**
 * Generate equipment list table rows
 * @param {Array} equipmentData - Array of equipment objects
 * @returns {string} - HTML for equipment list rows
 */
function generateEquipmentList(equipmentData) {
  return equipmentData
    .map(
      equipment => `
    <tr>
      <td>${equipment.equipmentType}</td>
      <td>${equipment.equipmentName}</td>
      <td>${equipment.equipmentNumber}</td>
      <td>${equipment.serialNumber}</td>
    </tr>
  `
    )
    .join('');
}

/**
 * Helper function to render remarks sections consistently
 * @param {string} remarks - Remarks content
 * @returns {string} - HTML for remarks section or empty string
 */
function renderRemarksSection(remarks) {
  if (!remarks)
    return `
  <div class="remarks-section">
    <div class="remarks-title">Remarks</div>
    <div class="remarks-content" style="height: 50px;"></div>
  </div>
  `;
  return `
  <div class="remarks-section">
    <div class="remarks-title">Remarks</div>
    <div class="remarks-content">${remarks}</div>
  </div>
  `;
}
