exports.toolbox = async templateData => {
  return `
        <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Details</title>
    <style>
        html {
            box-sizing: border-box;
        }

        *,
        *::before,
        *::after {
            box-sizing: inherit;
        }

        :root {
            --primary-color: ${templateData.companyPrimaryColor};
            --info-title-color: #4D5464;
            --info-desc-color: #333843;
            --status-color: #9D0202;
            --circle-bg-color: #D9D9D9;
            --circle-text-color: #000000;
            --table-header-border: #E0E6F51A;
            --font-color: #323232;
            --table-border: #E0E6F5;
        }

        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            font-family: Arial, sans-serif;
            width: 100%;
            height: 100%;
            font-size: 12px;
            font-weight: 500;
        }

        .main {
           width: 100%;
            box-sizing: border-box;
            page-break-after: always;
        }

        .header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

        }

        #header-text {
            font-size: 20px;
            font-weight: 600;
            color: var(--font-color);
        }

        .vertical-align {
            display: flex;
            flex-direction: column;
            justify-content: center;
            /* gap: 0; */
        }

        .vertical-align p {
            margin: 0;
            padding: 0;
        }

        .custom-table {
         margin-top: 20px;
         width: 100%;
         border-collapse: separate;
         border-spacing: 0;
        }
         .custom-table th {
            padding: 8px;
            text-align: left;
            border: 1px solid var(--table-header-border);
            padding-left: 10px;

        }

        .custom-table td {
            padding: 8px;
            text-align: left;
            border: 1px solid var(--circle-bg-color);
            padding: 10px;
        }

        .table-header th:first-child {
            border-top-left-radius: 4px;
        }

        .table-header th:last-child {
            border-top-right-radius: 4px;
        }

        .custom-table tbody tr:last-child td:first-child {
            border-bottom-left-radius: 4px;
        }

        .custom-table tbody tr:last-child td:last-child {
            border-bottom-right-radius: 4px;
        }

        .header-box1 {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 10px;
        }

        .header-box {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .title {
            font-size: 12px;
            font-weight: 700;
            color: var(--font-color);
            margin: 0;
        }

        .answer {
            font-size: 12px;
            font-weight: 500;
            color: var(--font-color);
            margin: 0;
        }

        .table-header {
            background-color: var(--primary-color);
            color: var(--circle-bg-color);
        }

        .text-line {
            display: flex;
            align-items: center;
            margin-top: 20px;
        }

        .text-line p {
            margin: 0;
            padding-right: 10px;
            white-space: nowrap;
            font-size: 15px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .text-line hr {
            flex-grow: 1;
            border: none;
            border-top: 1px solid var(--primary-color);
            margin: 0;
            margin-left: 5px;

        }

        .header-title {
            font-size: 12px;
            font-weight: 600;
            color: #FFFFFF;
        }

        @media print {
            body {
                width: 100%;
                height: auto;
                display: block;
            }

            .main {
               width: 100%;
                page-break-after: always;
                box-sizing: border-box;
            }

            .header {
                page-break-inside: avoid;
                display: flex;
                align-items: center;

            }

            .Status-section {
                page-break-inside: avoid;
            }

            .table-header {
                background-color: var(--primary-color) !important;
                color: var(--circle-bg-color);
            }

        }
    </style>
</head>

<body>
    <div class="main">
        <div class="header">
            <div class="vertical-align">

                <p id="header-text">Toolbox talk</p>
            </div>
        </div>
        <div>
            <table class="custom-table">
                <thead>
                    <tr class="table-header">
                        <th>
                            <div class="header-box">
                                <p class="header-title">Project</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Location</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Team</p>
                            </div>

                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Submitted Date</p>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <p class="answer">Hollandas Kust Zuid Fo dfasfdjflkdjas</p>
                        </td>
                        <td>
                            <p class="answer">Ahmedabad</p>
                        </td>
                        <td>
                            <p class="answer">05</p>
                        </td>
                        <td>
                            <p class="answer">12/12/1212</p>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div class="text-line">
                <p>Notes</p>
                <hr>
            </div>
            <div style="font-size: 12px;
            font-weight: 500;";
            color: var(--font-color);">
                <p style="line-height: 1.5;">Note offline Welcome to Gboard clipboard, any text that you copy will be
                    saved here.

                    Welcome to Gboard clipboard, any text that you copy will be saved here. Welcome to Gboard clipboard,
                    any
                    text that you copy will be saved here. Welcome to Gboard clipboard, any text that you copy will be
                    saved
                    here.</p>

                <p style="line-height: 1.5;"> Welcome to Gboard clipboard, any text that you copy will be saved here.
                    Welcome to Gboard clipboard,
                    any
                    text that you copy will be saved here. Welcome to Gboard clipboard, any text that you copy will be
                    saved
                    here.</p>

                <p style="line-height: 1.5;">Welcome to Gboard clipboard, any text that you copy will be saved here.
                    Welcome to Gboard clipboard,
                    any
                    text that you copy will be saved here. Welcome to Gboard clipboard, any text that you copy will be
                    saved
                    here.</p>
                <div class="text-line">
                    <p>Documents</p>

                </div>
                <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M4.6665 4.08337C4.6665 3.11688 5.45001 2.33337 6.4165 2.33337H17.9416C18.4058 2.33337 18.8509 2.51775 19.1791 2.84594L22.8206 6.48748C23.1488 6.81567 23.3332 7.26079 23.3332 7.72491V23.9167C23.3332 24.8832 22.5497 25.6667 21.5832 25.6667H6.4165C5.45001 25.6667 4.6665 24.8832 4.6665 23.9167V4.08337ZM22.1665 8.16671H18.0832C17.761 8.16671 17.4998 7.90554 17.4998 7.58337V3.50004H6.4165C6.09434 3.50004 5.83317 3.76121 5.83317 4.08337V23.9167C5.83317 24.2389 6.09434 24.5 6.4165 24.5H21.5832C21.9053 24.5 22.1665 24.2389 22.1665 23.9167V8.16671Z"
                        fill="#E0E2E7" />
                    <path d="M4.6665 11.0834H23.3332V18.0834H4.6665V11.0834Z" fill="#F21E1E" />
                    <path
                        d="M8.48779 16.9166V12.6742H10.2403C10.5579 12.6742 10.832 12.7363 11.0627 12.8606C11.2947 12.9835 11.4735 13.1555 11.5992 13.3764C11.7249 13.596 11.7877 13.8515 11.7877 14.1429C11.7877 14.4357 11.7235 14.6918 11.595 14.9114C11.468 15.1296 11.2864 15.2988 11.0502 15.4189C10.8141 15.5391 10.5337 15.5991 10.2092 15.5991H9.12789V14.7913H10.0186C10.1733 14.7913 10.3024 14.7643 10.406 14.7105C10.511 14.6566 10.5904 14.5814 10.6442 14.4847C10.6981 14.3866 10.725 14.2727 10.725 14.1429C10.725 14.0117 10.6981 13.8984 10.6442 13.8032C10.5904 13.7065 10.511 13.6319 10.406 13.5794C10.301 13.527 10.1719 13.5007 10.0186 13.5007H9.51318V16.9166H8.48779Z"
                        fill="white" />
                    <path
                        d="M13.8613 16.9166H12.2931V12.6742H13.8592C14.2914 12.6742 14.6636 12.7591 14.9757 12.929C15.2892 13.0975 15.5309 13.3405 15.7007 13.6582C15.8706 13.9744 15.9555 14.3528 15.9555 14.7933C15.9555 15.2353 15.8706 15.615 15.7007 15.9327C15.5323 16.2503 15.2913 16.494 14.9778 16.6639C14.6643 16.8324 14.2921 16.9166 13.8613 16.9166ZM13.3185 16.0424H13.8219C14.0594 16.0424 14.2604 16.0024 14.4247 15.9223C14.5904 15.8408 14.7154 15.7089 14.7996 15.5266C14.8853 15.343 14.9281 15.0985 14.9281 14.7933C14.9281 14.4881 14.8853 14.2451 14.7996 14.0642C14.714 13.8819 14.5877 13.7507 14.4206 13.6706C14.2548 13.5891 14.0505 13.5484 13.8074 13.5484H13.3185V16.0424Z"
                        fill="white" />
                    <path
                        d="M16.5542 16.9166V12.6742H19.4502V13.5069H17.5796V14.377H19.2658V15.2118H17.5796V16.9166H16.5542Z"
                        fill="white" />
                </svg>
                <svg width="14" height="5" viewBox="0 0 14 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M2.18092 4.91664H0.612793V0.674213H2.17884C2.6111 0.674213 2.98328 0.759144 3.29538 0.929007C3.60887 1.09749 3.85054 1.34054 4.0204 1.65817C4.19027 1.97442 4.2752 2.35282 4.2752 2.79335C4.2752 3.23527 4.19027 3.61505 4.0204 3.93268C3.85192 4.25031 3.61094 4.49405 3.29745 4.66392C2.98397 4.8324 2.61179 4.91664 2.18092 4.91664ZM1.63818 4.04247H2.14156C2.37909 4.04247 2.58002 4.00242 2.74436 3.92232C2.91008 3.84084 3.03506 3.70896 3.1193 3.52666C3.20493 3.34299 3.24774 3.09855 3.24774 2.79335C3.24774 2.48815 3.20493 2.2451 3.1193 2.06419C3.03368 1.8819 2.90732 1.7507 2.74022 1.6706C2.5745 1.58912 2.37011 1.54838 2.12706 1.54838H1.63818V4.04247Z"
                        fill="white" />
                    <path
                        d="M8.87806 2.79543C8.87806 3.2622 8.7883 3.65786 8.60877 3.98239C8.42924 4.30693 8.18618 4.55344 7.8796 4.72192C7.5744 4.8904 7.23191 4.97464 6.85214 4.97464C6.47098 4.97464 6.12781 4.88971 5.82261 4.71985C5.51741 4.54998 5.27504 4.30348 5.09551 3.98032C4.91736 3.65579 4.82829 3.26082 4.82829 2.79543C4.82829 2.32865 4.91736 1.93299 5.09551 1.60846C5.27504 1.28392 5.51741 1.03742 5.82261 0.868934C6.12781 0.700452 6.47098 0.616211 6.85214 0.616211C7.23191 0.616211 7.5744 0.700452 7.8796 0.868934C8.18618 1.03742 8.42924 1.28392 8.60877 1.60846C8.7883 1.93299 8.87806 2.32865 8.87806 2.79543ZM7.82989 2.79543C7.82989 2.51923 7.79053 2.28584 7.71181 2.09526C7.63447 1.90468 7.52261 1.76037 7.37623 1.66232C7.23122 1.56427 7.05653 1.51524 6.85214 1.51524C6.64913 1.51524 6.47444 1.56427 6.32805 1.66232C6.18166 1.76037 6.06911 1.90468 5.9904 2.09526C5.91306 2.28584 5.87439 2.51923 5.87439 2.79543C5.87439 3.07162 5.91306 3.30501 5.9904 3.49559C6.06911 3.68617 6.18166 3.83048 6.32805 3.92853C6.47444 4.02659 6.64913 4.07561 6.85214 4.07561C7.05653 4.07561 7.23122 4.02659 7.37623 3.92853C7.52261 3.83048 7.63447 3.68617 7.71181 3.49559C7.79053 3.30501 7.82989 3.07162 7.82989 2.79543Z"
                        fill="white" />
                    <path
                        d="M13.3276 2.21126H12.2919C12.2781 2.10493 12.2498 2.00895 12.207 1.92333C12.1641 1.8377 12.1075 1.76451 12.0371 1.70375C11.9667 1.64298 11.8831 1.59672 11.7864 1.56496C11.6912 1.53181 11.5855 1.51524 11.4695 1.51524C11.2637 1.51524 11.0863 1.56565 10.9371 1.66646C10.7894 1.76727 10.6754 1.91297 10.5953 2.10355C10.5166 2.29412 10.4773 2.52475 10.4773 2.79543C10.4773 3.07715 10.5173 3.3133 10.5974 3.50388C10.6789 3.69307 10.7928 3.83601 10.9392 3.93268C11.087 4.02797 11.2617 4.07561 11.4633 4.07561C11.5765 4.07561 11.6794 4.06111 11.7719 4.03211C11.8658 4.00311 11.948 3.96099 12.0185 3.90575C12.0903 3.84913 12.149 3.78077 12.1945 3.70067C12.2415 3.61919 12.2739 3.52735 12.2919 3.42516L13.3276 3.43138C13.3097 3.61919 13.2551 3.80424 13.164 3.98654C13.0742 4.16883 12.9506 4.33524 12.7932 4.48577C12.6358 4.63492 12.4438 4.75368 12.2173 4.84206C11.9922 4.93045 11.734 4.97464 11.4426 4.97464C11.0587 4.97464 10.7148 4.8904 10.411 4.72192C10.1085 4.55205 9.86962 4.30486 9.69423 3.98032C9.51885 3.65579 9.43115 3.26082 9.43115 2.79543C9.43115 2.32865 9.52023 1.93299 9.69837 1.60846C9.87652 1.28392 10.1175 1.03742 10.4213 0.868934C10.7251 0.700452 11.0656 0.616211 11.4426 0.616211C11.6994 0.616211 11.937 0.652117 12.1552 0.723929C12.3734 0.79436 12.5653 0.897934 12.731 1.03465C12.8968 1.16999 13.0314 1.3364 13.135 1.53388C13.2386 1.73137 13.3028 1.95716 13.3276 2.21126Z"
                        fill="white" />
                </svg>

            </div>
        </div>
        <table class="custom-table">
            <thead>
                <tr class="table-header">
                    <th>
                        <div class="header-box">
                            <p class="header-title">Host Signature</p>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div style="display: flex; justify-content: space-between;">
                            <p class="answer" style="margin-top: 10px;">Jhon Done</p>
                            <img style="width: 70px; height: 30px;"
                                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png">
                        </div>

                    </td>
                </tr>
            </tbody>
        </table>
        <table class="custom-table">
            <thead>
                <tr class="table-header">
                    <th colspan="4" >
                        <div class="header-box">
                            <p class="header-title">Attendance Details</p>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div style="display: flex; justify-content: space-between;">
                            <p class="answer">John Doe</p>
                            <img style="width: 70px; height: 30px;"
                                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png">
                        </div>
                    </td>
                    <td>
                        <div style="display: flex; justify-content: space-between;">
                            <p class="answer">John Doe</p>
                            <img style="width: 70px; height: 30px;"
                                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png">
                        </div>
                    </td>
                    <td>
                        <div style="display: flex; justify-content: space-between; ">
                            <p class="answer">John Doe</p>
                            <img style="width: 70px; height: 30px;"
                                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png">
                        </div>
                    </td>
                    <td>
                        <div style="display: flex; justify-content: space-between; ">
                            <p class="answer">John Doe</p>
                            <img style="width: 70px; height: 30px;"
                                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div style="display: flex; justify-content: space-between;">
                            <p class="answer">John Doe</p>

                            <img style="width: 70px; height: 30px;"
                                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png">
                        </div>
                    </td>
                    
                </tr>
            </tbody>
        </table>

    </div>
</body>

</html>
      `;
};
