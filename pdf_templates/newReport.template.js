exports.report = async templateData => {
  return `
  <!DOCTYPE html>
    <html lang="en">
    
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Report</title>
        <style>
            html {
                box-sizing: border-box;
            }
    
            *,
            *::before,
            *::after {
                box-sizing: inherit;
            }
    
            :root {
                --primary-color: ${templateData.companyPrimaryColor};
                --primary-font-color: #323232;
                --info-title-color: #4D5464;
                --info-desc-color: #333843;
                --status-color: #009A38;
                --circle-bg-color: #D9D9D9;
                --black-color: #000000;
                --info-label-color: #FFFFFF;
                --table-border: #E0E6F5;
                --table-header-border: #E0E6F51A;
                --white-color: #FFFFFF;
                --pdf-bg-color: #F6F7FF;
            }
    
            body {
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                font-family: Arial, sans-serif;
                width: 100%;
                height: 100%;
                color: var(--primary-font-color);
                font-size: 12px;
                font-weight: 500;
            }
    
            .main {
                width: 100%;
            }
    
            .pdf-header {
                display: flex;
                width: 100%;
            }
    
            .pdf-header-title {
                font-size: 20px;
                padding: 0;
                margin: 0;
                margin-bottom: 10px;
                font-weight: 600;
            }
    
            .space-container {
                height: 10px;
            }
    
            .custom-table {
                width: 100%;
                border-spacing: 0px;
                border-radius: 4px;
            }
    
            .custom-table tr {
                page-break-inside: avoid;
            }
    
            .custom-table th {
                text-align: left;
                padding-left: 10px;
                background-color: var(--primary-color);
                color: var(--white-color);
            }
    
            .custom-table td {
                padding-left: 10px;
            }
    
            .custom-table th:first-child {
                border-right: 1px solid var(--table-border);
                border-top-left-radius: 4px;
            }
    
            .custom-table td:first-child {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
                border-left: 1px solid var(--table-border);
            }
    
            .custom-table th:not(:first-child):not(:last-child) {
                border-right: 1px solid var(--table-border);
            }
    
            .custom-table td:not(:first-child):not(:last-child) {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
            }
    
            .custom-table th:last-child {
                border-right: 0px;
                border-top-right-radius: 4px;
            }
    
            .custom-table td:last-child {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
            }
    
            .custom-table tr:last-child td:first-child {
                border-bottom-left-radius: 4px;
            }
    
            .custom-table tr:last-child td:last-child {
                border-bottom-right-radius: 4px;
            }
    
            .safety-table-header {
                padding: 10px;
                font-size: 12px;
                font-weight: 600;
            }
    
            .safety-first-table-desc {
                padding: 15px;
                font-size: 12px;
                font-weight: 500;
            }
    
            .full-info-container {
                width: 100%;
                display: flex;
                flex-direction: column;
            }
    
            .full-info-container-title {
                width: 100%;
                padding: 10px 10px;
                border-radius: 2px;
                background-color: var(--primary-color);
                color: var(--white-color);
                font-weight: bold;
                font-size: 12px;
                font-weight: 600;
                margin-top: 5px;
            }
    
            .full-info-container-desc {
                width: 100%;
                padding: 10px 10px;
                border: 2px;
                text-align: justify;
                font-weight: 500;
                font-size: 12px;
                background-color: var(--white-color);
            }
    
            .image-table-td {
                width: 33.33%;
                height: 300px;
                padding: 10px;
                text-align: center;
                vertical-align: middle;
            }
    
            .image-bg-container {
                background-color: var(--table-border);
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                border-radius: 8px;
                height: 300px;
    
            }
    
            .image-bg-container-img {
                max-width: 100%;
                max-height: 100%;
                object-fit: contain;
            }
    
            .text-line {
                display: flex;
                align-items: center;
                margin-top: 20px;
            }
    
            .text-line p {
                margin: 0;
                padding-right: 10px;
                white-space: nowrap;
                font-size: 15px;
                font-weight: 600;
                color: var(--primary-color);
            }
    
            .text-line hr {
                flex-grow: 1;
                border: none;
                border-top: 1px solid var(--primary-color);
                margin: 0;
            }
    
            .flex-div-container {
                display: flex;
                justify-content: space-between;
                gap: 5px;
            }
    
            .custom-checkbox {
                width: 14px;
                height: 14px;
                border: 1px solid var(--primary-font-color);
                border-radius: 3px;
            }
    
            .custom-checkbox span {
                display: none;
            }
    
            .custom-checked-checkbox {
                width: 14px;
                height: 14px;
                border: 1px solid var(--primary-font-color);
                border-radius: 3px;
                background-color: var(--primary-color);
                color: var(--white-color);
                display: flex;
                justify-content: center;
                align-items: center;
                padding-top: 1px;
            }
    
            .sign-container {
                width: 80px;
                height: 20px;
                display: flex;
                justify-content: end;
                align-items: center;
            }
    
            .margin-bottom {
                margin-bottom: 5px;
            }
    
            .image-answer-container {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 10px;
                margin-left: 10px;
            }
    
            .image-answer {
                width: 80px;
                height: 80px;
                border-radius: 4px;
                object-fit: contain;
            }
    
            .custom-hr {
                flex-grow: 1;
                border: none;
                border-top: 1px solid var(--table-border);
                margin: 0 10px;
            }
        </style>
    </head>
    
    <body>
        <div class="main">
            <div class="pdf-header">
                <p class="pdf-header-title">Report Details</p>
            </div>
            <table class="custom-table">
                <tr>
                    <th class="safety-table-header">Project</th>
                    <th class="safety-table-header">Report</th>
                    <th class="safety-table-header">Location</th>
                    <th class="safety-table-header">Assets</th>
                </tr>
                <tr>
                    <td class="safety-first-table-desc">Project A</td>
                    <td class="safety-first-table-desc">UI Team</td>
                    <td class="safety-first-table-desc">Location A</td>
                    <td class="safety-first-table-desc">05</td>
                </tr>
            </table>
            <div class="space-container"></div>
            <div class="full-info-container">
                <span class="full-info-container-title">Q1 : Picture Question Example</span>
                <div class="image-answer-container">
                    <img class="image-answer"
                        src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png"
                        alt="item">
                    <img class="image-answer" src="https://via.placeholder.com/300" alt="item">
                </div>
                <span class="full-info-container-desc">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                    dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip
                    ex ea commodo consequat.
                    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                    Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                    laborum.
                </span>
                <hr class="custom-hr">
                <div class="image-answer-container">
                    <img class="image-answer" src="https://via.placeholder.com/300" alt="item">
                    <img class="image-answer" src="https://via.placeholder.com/300" alt="item">
                </div>
                <span class="full-info-container-desc">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                    dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip
                    ex ea commodo consequat.
                    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                    Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est
                    laborum.
                </span>
            </div>
            <div class="space-container"></div>
            <table class="custom-table" style="page-break-inside: avoid; width: calc(33.33% * 3);">
                <tr>
                    <th class="safety-table-header">Lorem ipsum dolor sit amet</th>
                    <th class="safety-table-header">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do</th>
                    <th class="safety-table-header">Lorem ipsum dolor sit amet, consectetur adipielit, sed do eiusmod tempor
                        incididunt..</th>
                </tr>
    
                <tr>
                    <td class="image-table-td">
                        <div class="image-bg-container">
                            <img class="image-bg-container-img"
                                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png"
                                alt="logo">
                        </div>
                    </td>
                    <td class="image-table-td">
                        <div class="image-bg-container">
                            <img class="image-bg-container-img" src="./images/Image_2.jpg" alt="logo">
                        </div>
                    </td>
                    <td class="image-table-td">
                        <div class="image-bg-container">
                            <img class="image-bg-container-img" src="./images/Image_1.png" alt="logo">
                        </div>
                    </td>
                </tr>
            </table>
            <div class="space-container"></div>
            <table class="custom-table" style="page-break-inside: avoid; width: calc(33.33% * 2);">
                <tr>
                    <th class="safety-table-header">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do</th>
                    <th class="safety-table-header">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do</th>
                </tr>
                <tr>
                    <td class="image-table-td">
                        <div class="image-bg-container">
                            <img class="image-bg-container-img" src="./images/Image_2.jpg" alt="logo">
                        </div>
                    </td>
                    <td class="image-table-td">
                        <div class="image-bg-container">
                            <img class="image-bg-container-img" src="./images/Image_1.png" alt="logo">
                        </div>
                    </td>
                </tr>
            </table>
            <div class="space-container"></div>
            <div style="page-break-inside: avoid;">
                <div class="text-line">
                    <p>Range Details</p>
                    <hr>
                </div>
                <div class="space-container"></div>
                <table class="custom-table" style="width: calc(25% * 4)">
                    <tr>
                        <th class="safety-table-header" style="width: 25%;">Lorem ipsum dolor sit amet</th>
                        <th class="safety-table-header" style="width: 25%;">Lorem ipsum dolor sit amet</th>
                        <th class="safety-table-header" style="width: 25%;">Lorem ipsum dolor sit amet</th>
                        <th class="safety-table-header" style="width: 25%;">Lorem ipsum dolor sit amet</th>
                    </tr>
                    <tr>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container">
                                <span>5</span>
                                <span>(0-10)</span>
                            </div>
                        </td>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container">
                                <span>7</span>
                                <span>(0-10)</span>
                            </div>
                        </td>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container">
                                <span>4</span>
                                <span>(0-10)</span>
                            </div>
                        </td>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container">
                                <span>9</span>
                                <span>(0-10)</span>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="space-container"></div>
            <table class="custom-table" style="width: calc(25% * 2)">
                <tr>
                    <th class="safety-table-header" style="width: 25%;">Lorem ipsum dolor sit amet</th>
                    <th class="safety-table-header" style="width: 25%;">Lorem ipsum dolor sit amet</th>
                </tr>
                <tr>
                    <td class="safety-first-table-desc">
                        <div class="flex-div-container">
                            <span>5</span>
                            <span>(0-10)</span>
                        </div>
                    </td>
                    <td class="safety-first-table-desc">
                        <div class="flex-div-container">
                            <span>7</span>
                            <span>(0-10)</span>
                        </div>
                    </td>
                </tr>
            </table>
            <div class="space-container"></div>
            <div style="page-break-inside: avoid;">
                <div class="text-line">
                    <p>Boolean Details</p>
                    <hr>
                </div>
                <div class="space-container"></div>
                <table class="custom-table" style="width: calc(25% * 4)">
                    <tr>
                        <th class="safety-table-header" style="width: 25%;">Lorem ipsum dolor sit amet</th>
                        <th class="safety-table-header" style="width: 25%;">Lorem ipsum dolor sit amet</th>
                        <th class="safety-table-header" style="width: 25%;">Lorem ipsum dolor sit amet</th>
                        <th class="safety-table-header" style="width: 25%;">Lorem ipsum dolor sit amet</th>
                    </tr>
                    <tr>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container">
                                <span>5</span>
                                <span>(0-10)</span>
                            </div>
                        </td>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container">
                                <span>7</span>
                                <span>(0-10)</span>
                            </div>
                        </td>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container">
                                <span>4</span>
                                <span>(0-10)</span>
                            </div>
                        </td>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container">
                                <span>9</span>
                                <span>(0-10)</span>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="space-container"></div>
            <div style="page-break-inside: avoid;">
                <div class="text-line">
                    <p>Checkbox Details</p>
                    <hr>
                </div>
                <div class="space-container"></div>
                <table class="custom-table" style="width: calc(33.33% * 3)">
                    <tr>
                        <th class="safety-table-header" style="width: 33.33%;">Lorem ipsum dolor sit amet</th>
                        <th class="safety-table-header" style="width: 33.33%;">Lorem ipsum dolor sit amet</th>
                        <th class="safety-table-header" style="width: 33.33%;">Lorem ipsum dolor sit amet</th>
                    </tr>
                    <tr>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container margin-bottom">
                                <span>Option 1</span>
                                <div class="custom-checked-checkbox">
                                    <span>&#10003</span>
                                </div>
                            </div>
                            <div class="flex-div-container margin-bottom">
                                <span>Option 2</span>
                                <div class="custom-checkbox">
                                    <span>&#10003</span>
                                </div>
                            </div>
                            <div class="flex-div-container">
                                <span>Option 3</span>
                                <div class="custom-checkbox">
                                    <span>&#10003</span>
                                </div>
                            </div>
                        </td>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container margin-bottom">
                                <span>Option 1</span>
                                <div class="custom-checked-checkbox">
                                    <span>&#10003</span>
                                </div>
                            </div>
                            <div class="flex-div-container margin-bottom">
                                <span>Option 2</span>
                                <div class="custom-checkbox">
                                    <span>&#10003</span>
                                </div>
                            </div>
                            <div class="flex-div-container">
                                <span>Option 3</span>
                                <div class="custom-checkbox">
                                    <span>&#10003</span>
                                </div>
                            </div>
                        </td>
                        <td class="safety-first-table-desc">
                            <div class="flex-div-container margin-bottom">
                                <span>Option 1</span>
                                <div class="custom-checkbox">
                                    <span>&#10003</span>
                                </div>
                            </div>
                            <div class="flex-div-container margin-bottom">
                                <span>Option 2</span>
                                <div class="custom-checkbox">
                                    <span>&#10003</span>
                                </div>
                            </div>
                            <div class="flex-div-container">
                                <span>Option 3</span>
                                <div class="custom-checkbox">
                                    <span>&#10003</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="space-container"></div>
            <div class="space-container"></div>
            <table class="custom-table" style="width: calc(33.33% * 3); page-break-inside: avoid;">
                <tr>
                    <th class="safety-table-header" style="border-right: 0px">
                        Sign-Off
                    </th>
                    <th style="border-right: 0px"></th>
                    <th></th>
                </tr>
                <tr>
                    <td class="safety-first-table-desc" style="width: 33.33%;">
                        <div class="flex-div-container">
                            <span>Peter John</span>
                            <div class="sign-container">
                                <img class="image-bg-container-img" src="images/Image_1.png" alt="sign">
                            </div>
                        </div>
                    </td>
                    <td class="safety-first-table-desc" style="width: 33.33%;">
                        <div class="flex-div-container">
                            <span>Tim Namber</span>
                            <div class="sign-container">
                                <img class="image-bg-container-img" src="images/Image_1.png" alt="sign">
                            </div>
                        </div>
                    </td>
                    <td class="safety-first-table-desc" style="width: 33.33%;">
                        <div class="flex-div-container">
                            <span>Tiffie Cuthbert</span>
                            <div class="sign-container">
                                <img class="image-bg-container-img"
                                    src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png"
                                    alt="sign">
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
            <div class="space-container"></div>
            <div class="space-container"></div>
        </div>
    </body>
    
    </html>
      `;
};
