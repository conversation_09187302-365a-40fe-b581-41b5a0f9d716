const commonFunctionsUtils = require('../app/utils/common-function.utils');
const commonUtils = require('../app/utils/common.utils');
/**
 * Generate location Progress details pdf
 *
 * @param {*} templateData
 * @returns
 */
exports.locationProgressList = async templateData => {
  const [location, assetPerLocation, multiple_assets] = templateData.requestData.reports;
  const getLocationData = await this.generateRow(location, '1.');
  const getAssetPerLocationData = await this.generateRow(assetPerLocation, '2.');
  const multipleAssetData = await this.generateRow(multiple_assets, '3.');
  return `
    <!DOCTYPE html>
    <html lang="en">
    
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Location Progress</title>
        <style>
            html {
                box-sizing: border-box;
            }
    
            *,
            *::before,
            *::after {
                box-sizing: inherit;
            }
    
            :root {
                --primary-color: ${templateData.companyPrimaryColor};
                --primary-font-color: #323232;
                --info-title-color: #4D5464;
                --info-desc-color: #333843;
                --status-color-green: #2A8C00;
                --status-color-red: #9D0202;
                --circle-bg-color: #D9D9D9;
                --black-color: #000000;
                --info-label-color: #FFFFFF;
                --table-border: #E0E6F5;
                --table-header-border: #E0E6F51A;
                --white-color: #FFFFFF;
                --pdf-bg-color: #F6F7FF;
                --status-color-orange: #ff6600;
                --status-color-blue: #191A51;
            }
    
            body {
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                font-family: sans-serif;
                width: 100%;
                height: 100%;
                color: var(--primary-font-color);
                font-size: 12px;
                font-weight: 500;
            }
    
            .main {
                width: 100%;
               
            }
    
            .pdf-header {
                display: flex;
                width: 100%;
            }
    
            .pdf-header-title {
                font-size: 20px;
                padding: 0;
                margin: 0;
                margin-bottom: 10px;
                font-weight: 600;
            }
    
            .space-container {
                height: 20px;
            }
    
            .custom-table {
                width: 100%;
                border-spacing: 0px;
                border-radius: 4px;
            }
    
            .custom-table tr {
                page-break-inside: avoid;
            }
    
            .custom-table th {
                text-align: left;
                padding-left: 10px;
                background-color: var(--primary-color);
                color: var(--white-color);
            }
    
            .custom-table td {
                padding-left: 10px;
            }
    
            .custom-table th:first-child {
                border-right: 1px solid var(--table-border);
                border-top-left-radius: 4px;
            }
    
            .custom-table td:first-child {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
                border-left: 1px solid var(--table-border);
            }
    
            .custom-table th:not(:first-child):not(:last-child) {
                border-right: 1px solid var(--table-border);
            }
    
            .custom-table td:not(:first-child):not(:last-child) {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
            }
    
            .custom-table th:last-child {
                border-right: 0px;
                border-top-right-radius: 4px;
            }
    
            .custom-table td:last-child {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
            }
    
            .custom-table tr:last-child td:first-child {
                border-bottom-left-radius: 4px;
            }
    
            .custom-table tr:last-child td:last-child {
                border-bottom-right-radius: 4px;
            }
    
            .safety-table-header {
                padding: 10px;
                font-size: 12px;
                font-weight: 600;
            }
    
            .safety-first-table-desc {
                padding: 14px;
                font-size: 12px;
                font-weight: 500;
            }
    
            .text-line {
                display: flex;
                align-items: center;
                margin-top: 20px;
            }
    
            .text-line p {
                margin: 0;
                padding-right: 10px;
                white-space: nowrap;
                font-size: 15px;
                font-weight: 600;
                color: var(--primary-color);
            }
    
            .text-line hr {
                flex-grow: 1;
                border: none;
                border-top: 1px solid var(--primary-color);
                margin: 0;
                margin-left: 5px;
    
            }
    
            .Overall-container {
                display: flex;
                border: 1px solid var(--table-border);
                border-radius: 4px;
            }
    
            .Overall-title {
                width: 50%;
                background-color: var(--primary-color);
                color: var(--white-color);
                padding: 10px;
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
            }
    
            .Overall-percentage {
                width: 50%;
                text-align: right;
                padding: 10px;
                font-weight: 600;
            }
        </style>
    </head>
    
    <body>
        <div class="main">
            <div class="pdf-header">
                <p class="pdf-header-title">Location Progress (${
                  templateData?.requestData?.locationTitle
                })</p>
            </div>
            ${
              location?.reportData.length !== 0
                ? `
            <div class="text-line">
                <p>Location based report</p>
                <hr>
            </div>
            <div class="space-container"></div>
            <table class="custom-table">
                <tr>
                    <th class="safety-table-header">No.</th>
                    <th class="safety-table-header">Report Name</th>
                    <th class="safety-table-header">Location/ Asset</th>
                    <th class="safety-table-header">Scope</th>
                    <th class="safety-table-header">Status</th>
                    <th class="safety-table-header">Completion Date</th>
                    <th class="safety-table-header">Completion (%)</th>
                </tr>
                ${getLocationData}
            </table>
            `
                : ''
            }
            ${
              assetPerLocation?.reportData.length !== 0
                ? `
            <div class="text-line">
                <p>Asset Based Report</p>
                <hr>
            </div>
            <div class="space-container"></div>
            <table class="custom-table">
                <tr>
                    <th class="safety-table-header">No.</th>
                    <th class="safety-table-header">Report Name</th>
                    <th class="safety-table-header">Location/ Asset</th>
                    <th class="safety-table-header">Scope</th>
                    <th class="safety-table-header">Status</th>
                    <th class="safety-table-header">Completion Date</th>
                    <th class="safety-table-header">Completion (%)</th>
                </tr>
                ${getAssetPerLocationData}
            </table>
            `
                : ''
            }
            ${
              multiple_assets.reportData.length !== 0
                ? `
            <div class="text-line">
                <p>Multiple Asset based Report</p>
                <hr>
            </div>
            <div class="space-container"></div>
            <table class="custom-table">
                <tr>
                    <th class="safety-table-header">No.</th>
                    <th class="safety-table-header">Report Name</th>
                    <th class="safety-table-header">Location/ Asset</th>
                    <th class="safety-table-header">Scope</th>
                    <th class="safety-table-header">Status</th>
                    <th class="safety-table-header">Completion Date</th>
                    <th class="safety-table-header">Completion (%)</th>
                </tr>
               ${multipleAssetData}
            </table>
            `
                : ''
            }
            <div class="space-container"></div>
            <div class="Overall-container">
                <span class="Overall-title">Overall Completion %</span>
                <span class="Overall-percentage">${
                  Math.floor((templateData?.requestData?.totalCompletion ?? 0) * 100) / 100
                }%</span>
            </div>
            <div class="space-container"></div>
            <div class="space-container"></div>
        </div>
    </body>
    
    </html>
      `;
};

/**
 * Generate row Data
 *
 * @param {*} data
 * @param {*} prefix
 * @returns
 */

exports.generateRow = async (data, prefix) => {
  let count = 1;
  let rows = '';

  const isLocationReport = data.reportType === 'Location based report';
  for (let entry of data.reportData) {
    const statusColor = await this.setColor(entry?.status);
    const completionDate = entry.completionDate
      ? await commonFunctionsUtils.formatDateUTC(entry.completionDate)
      : ' ';

    const totalDuration = entry?.totalDuration || 0;
    const completedDuration = entry?.completedDuration || 0;
    const percentage = await this.handlePercentage(totalDuration, completedDuration);
    rows += `
        <tr>
          <td class="safety-first-table-desc">${prefix}${count}</td>
          <td class="safety-first-table-desc">${entry?.report?.title}</td>
          <td class="safety-first-table-desc">${
            isLocationReport
              ? commonUtils.alterStringFromRequestString(entry?.location?.title)
              : `${commonUtils.alterStringFromRequestString(
                  entry?.asset?.cableName ? entry?.asset?.cableName : entry?.asset?.title
                )} (
                  ${entry?.asset?.fromLocation} - ${entry?.asset?.toLocation}
                )`
          }</td>
          <td class="safety-first-table-desc">${
            entry?.scope.length === 0
              ? ' '
              : commonUtils.alterStringFromRequestString(entry?.scope?.name)
          }</td>
          <td class="safety-first-table-desc" style="color: var(${
            statusColor || '--primary-color'
          });">${
      entry?.status ? commonUtils.alterStringFromRequestString(entry.status[0]) : ' '
    }</td>
          <td class="safety-first-table-desc">${
            Number(percentage) === 100 ? completionDate : ' '
          }</td>
          <td class="safety-first-table-desc">${percentage}%</td>
        </tr>
      `;

    count++;
  }

  return rows;
};

/**
 * Get Percentage
 *
 * @param {*} total
 * @param {*} completed
 * @returns
 */
exports.handlePercentage = async (total, completed) => {
  const percentage = total > 0 ? (completed / total) * 100 : 0;
  return percentage.toFixed(2); // Format to two decimal places
};

/**
 * Set status color
 *
 * @param {*} status
 * @returns
 */

exports.setColor = async status => {
  if (status) {
    if (status[0] === 'submitted') {
      return '--status-color-orange';
    } else if (status[0] === 'open') {
      return '--status-color-red';
    } else if (status[0] === 'checked') {
      return '--status-color-blue';
    } else {
      return '--status-color-green';
    }
  }
};
