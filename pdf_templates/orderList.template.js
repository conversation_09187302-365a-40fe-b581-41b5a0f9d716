exports.orderList = async templateData => {
  return `
        <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Details</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
    <style>
        html {
            box-sizing: border-box;
        }

        *,
        *::before,
        *::after {
            box-sizing: inherit;
        }

        :root {
            --primary-color: ${templateData.companyPrimaryColor};
            --info-title-color: #4D5464;
            --info-desc-color: #333843;
            --status-color: #9D0202;
            --circle-bg-color: #D9D9D9;
            --circle-text-color: #000000;
            --table-header-border: #E0E6F51A;
            --total-amount: #CD6B35;
            --font-color: #323232;
        }
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            font-family: Arial, sans-serif;
            width: 100%;
            height: 100%;
            font-size: 12px;
            font-weight: 500;
        }

        .main {
            width: 100%;
            box-sizing: border-box;
            page-break-after: always;
        }

        .header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

        }

        #header-text {
            font-size: 20px;
            font-weight: 600;
            color: var(--font-color);
        }

        .vertical-align {
            display: flex;
            flex-direction: column;
            justify-content: center;
            /* gap: 0; */
        }

        .vertical-align p {
            margin: 0;
            padding: 0;
        }
        .custom-table {
         margin-top: 20px;
         width: 100%;
         border-collapse: separate;
         border-spacing: 0;
        }
         .custom-table th {
            padding: 8px;
            text-align: left;
            border: 1px solid var(--table-header-border);
            padding-left: 10px;

        }

        .custom-table td {
            padding: 8px;
            text-align: left;
            border: 1px solid var(--circle-bg-color);
            padding: 10px;
        }

        .table-header th:first-child {
            border-top-left-radius: 10px;
        }

        .table-header th:last-child {
            border-top-right-radius: 10px;
        }

        .custom-table tbody tr:last-child td:first-child {
            border-bottom-left-radius: 10px;
        }

        .custom-table tbody tr:last-child td:last-child {
            border-bottom-right-radius: 10px;
        }
        .header-box1 {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 10px;
        }

        .header-box {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .title {
            font-size: 12px;
            font-weight: 700;
            color: var(--font-color);
            margin: 0;
        }

        .answer {
            font-size: 12px;
            font-weight: 500;
            color: var(--font-color);
            margin: 0;
        }

        .table-header {
            background-color: var(--primary-color);
            color: var(--circle-bg-color);
        }

        .text-line {
            display: flex;
            align-items: center;
            margin-top: 20px;
        }

        .text-line p {
            margin: 0;
            padding-right: 10px;
            white-space: nowrap;
            font-size: 15px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .text-line hr {
            flex-grow: 1;
            border: none;
            border-top: 1px solid var(--primary-color);
            margin: 0;
            margin-left: 5px;

        }

        .content-text {
            font-size: 12px;
            color: var(--font-color);
        }

        .header-title {
            font-size: 12px;
            font-weight: 600;
            color: #FFFFFF;
        }

        @media print {
            body {
                width: 100%;
                height: auto;
                display: block;
            }

            .main {
                width: 100%;
                max-width: 794px;
                margin: 0 auto;
                page-break-after: always;
            }

            .header {
                page-break-inside: avoid;
                display: flex;
                align-items: center;

            }

            .Status-section {
                page-break-inside: avoid;
            }

            .table-header {
                background-color: var(--primary-color) !important;
                color: var(--circle-bg-color);
            }
        }
    </style>
</head>

<body>
    <div class="main">
        <div class="header">
            <div class="vertical-align">
                <p id="header-text">Manifest Order #12348</p>
            </div>
        </div>
        <div>
            <table class="custom-table">
                <thead>
                    <tr class="table-header">
                        <th>
                            <div class="header-box">
                                <p class="header-title">Project</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">

                                <p class="header-title">Requested By</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Date</p>
                            </div>

                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Status</p>
                            </div>

                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <p class="answer">Hollandas Kust Zuid Fo</p>
                        </td>
                        <td>
                            <p class="answer">H0202001</p>
                        </td>
                        <td>
                            <p class="answer">05</p>
                        </td>
                        <td>
                            <p class="answer">12/12/1212</p>
                        </td>
                    </tr>
                </tbody>
            </table>
            <table class="custom-table">
                <thead>
                    <tr class="table-header" >
                        <th >
                            <div class="header-box">
                                <p class="header-title">Request Items</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">

                                <p class="header-title">Requested Quantity</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">

                                <p class="header-title">From Date to:</p>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <p class="answer">4</p>
                        </td>
                        <td>
                            <p class="answer">6 Qty</p>
                        </td>
                        <td style="display: flex; justify-content: space-between;">
                            <div>
                                <p class="title">From</p>
                                <p class="answer">12-12-1212</p>
                            </div>
                            <div>
                                <p class="title">To</p>
                                <p class="answer">12-12-1212</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div class="text-line">
                <p>Order Details</p>
                <hr>
            </div>
            <table class="custom-table">
                <thead>
                    <tr class="table-header" >
                        <th>
                            <div class="header-box">
                                <p class="header-title">No</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Equipment Type</p>
                            </div>
                        </th>
                        <th >
                            <div class="header-box">
                                <p class="header-title">Approverd Qty</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Expected Rental days</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Price Per Equipment</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Total Amount</p>
                            </div>
                        </th>
                        <th>
                            <div class="header-box">
                                <p class="header-title">Status</p>
                            </div>
                        </th>

                    </tr>
                </thead>
                <tbody>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is tool</td>
                        <td>20</td>
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Approved</td>

                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                        l</td>
                        <td>20</td>
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Approved</td>

                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is tool</td>
                        <td>20</td>
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Requested</td>

                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is tool</td>
                        <td>20</td>
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Requested</td>

                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is tool</td>
                        <td>20</td>
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Approved</td>

                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is tool</td>
                        <td>20</td>
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Approved</td>

                    </tr>
                        <tr class="content-text">
                        <td>1</td>
                        <td>This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                        l</td>
                        <td>20</td>
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Approved</td>

                    </tr>
                    
                  
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                        l</td>
                        <td>20</td>
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Rejected</td>

                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                        l.This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            l.This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            l.This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            l.This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            l.This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            lThis is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            lThis is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            l</td>
                        <td>20</td>
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Rejected</td>

                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfal</td>
                        <td>20</td>
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Requested</td>

                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfal</td>
                        <td>20</td> 
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Requested</td>

                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                         l</td>
                        <td>20</td>          
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Rejected</td>

                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa   l</td>
                        <td>20</td>    
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Rejected</td>
                    </tr>
                    <tr class="content-text">
                        <td>1</td>
                        <td>This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                        l.This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            l.This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            l.This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            l.This is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            lThis is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            lThis is too fdsfa sd fdfasfads af dafa df afasdf dfd fasfdfsdfasdf asdfadf asdfa
                            l</td>
                        <td>20</td>  
                        <td>5</td>
                        <td>$ 250</td>
                        <td>$ 1000</td>
                        <td>Requested</td>

                    </tr>
                </tbody>
            </table>
        </div>
        <table class="custom-table">
            <thead>
                <tr class="table-header">
                    <th>
                        <div class="header-box">
                            <p class="header-title">Details</p>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                            <p class="answer">Total weight</p>
                            <p class="answer">4kg</p>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                            <p class="answer">Total Value</p>
                            <p style="color:var(--total-amount); font-size:15px; font-weight:700; margin:0;">$16</p>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        <table class="custom-table" style="page-break-inside:avoid">
            <thead>
                <tr class="table-header">
                    <th colspan="3">
                        <div style="display: flex; justify-content: center; align-items: center;">
                            <p class="header-title">Sender</p>
                        </div>
                    </th>
                    <th colspan="3">
                        <div style="display: flex; justify-content: center; align-items: center;">
                            <p class="header-title">Reciver</p>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div>
                            <p class="title">Name</p>
                            <p class="answer">Jhon Doe</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Date</p>
                            <p class="answer">15/02/2023</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Contact No.</p>
                            <p class="answer">+91 9898889899</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Name</p>
                            <p class="answer">Jhon Doe</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Date</p>
                            <p class="answer">15/02/2023</p>
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Contact No.</p>
                            <p class="answer">+91 9898889899</p>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div>
                            <p class="title">Signature</p>
                        </div>
                    </td>
                    <td colspan="2">
                        <div style="display: flex; justify-content: center; align-items: center;">
                            <img style="width: 70px; height: 30px;"
                                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png">
                        </div>
                    </td>
                    <td>
                        <div>
                            <p class="title">Signature</p>
                        </div>
                    </td>
                    <td colspan="2">
                        <div style="display: flex; justify-content: center; align-items: center;">
                            <img style="width: 70px; height: 30px;"
                                src="https://reynard-dev-fb469d2.sfo3.cdn.digitaloceanspaces.com/images/Mail_Images/reynard_logo_pdf.png">
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</body>

</html>
      `;
};
