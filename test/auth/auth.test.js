const request = require('supertest');
const app = require('../../app/server');
const { DEFAULT_PASSWORD } = process.env;

// login
describe('POST /api/auths/login', () => {
  it('returns 200 and message "Logged in successfully" with correct credentials', async () => {
    const response = await request(app).post('/api/auths/login').send({
      email: '<EMAIL>',
      password: DEFAULT_PASSWORD,
      userAgent:
        '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36',
      location: 'Rotterdam',
    });
    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      data: { response },
      message: 'Login successfully.',
      status: true,
    });
  });

  it('returns 400 with missing email field', async () => {
    const response = await request(app).post('/login').send({
      password: DEFAULT_PASSWORD,
      userAgent:
        '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36',
      location: 'Rotterdam',
    });
    expect(response.status).toBe(404);
    expect(response.body).toEqual({ message: 'Email and password are required fields' });
  });

  it('returns 400 with missing password field', async () => {
    const response = await request(app).post('/login').send({
      email: '<EMAIL>',
      userAgent:
        '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36',
      location: 'Rotterdam',
    });
    expect(response.status).toBe(404);
    expect(response.body).toEqual({ message: 'Email and password are required fields' });
  });

  it('returns 401 and message "Invalid credentials" with incorrect credentials', async () => {
    const response = await request(app).post('/api/auths/login').send({
      email: '<EMAIL>',
      password: DEFAULT_PASSWORD,
      userAgent:
        '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36',
      location: 'Rotterdam',
    });
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'User is not registered.',
      status: false,
    });
  });

  it('returns 400 with missing username or password', async () => {
    const response = await request(app).post('/api/auths/login').send({});
    expect(response.status).toBe(422);
  });
});

// forget-password
describe('POST /api/auths/forget-password', () => {
  it('returns 200 and message "Password reset email sent successfully" with valid email', async () => {
    const response = await request(app).post('/api/auths/forget-password').send({
      email: '<EMAIL>',
    });
    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      message: 'Reset password link has been sent to your registered email id',
      status: true,
    });
  });

  it('returns 400 and message "Email not found" with invalid email', async () => {
    const response = await request(app).post('/api/auths/forget-password').send({
      email: '<EMAIL>',
    });
    expect(response.status).toBe(400);
    expect(response.body).toEqual({ message: 'User is not registered.', status: false });
  });
});

// logout
describe('GET /api/auths/logout', () => {
  it('returns 200 and message "Password reset email sent successfully" with valid email', async () => {
    const response = await request(app).get('/api/auths/logout');
    expect(response.status).toBe(200);
    expect(response.body).toEqual({ message: 'Logout successfully.' });
  });
  it('returns 400 and message "Please send authentication token." with valid email', async () => {
    const response = await request(app).get('/api/auths/logout');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({ message: 'Please send authentication token.', status: false });
  });
});
