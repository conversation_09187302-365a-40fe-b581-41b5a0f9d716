const request = require('supertest');
const app = require('../../app/server');

// create locations
describe('POST /api/locations', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.m4KGHeX7ut2fRp42YOriWVJB2ruro6Xb1QeN2InZwI4';
  const locationData = {
    title: 'GOC Location 3',
    project: '64119ddc6c1d88fde480fd47',
  };
  it('returns 200 and message Location has been created successfully', async () => {
    const response = await request(app)
      .post('/api/locations')
      .set('Authorization', `Bearer ${token}`)
      .send(locationData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Location has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/locations')
      .set('Authorization', `Bearer ${token}`)
      .send({
        title: ' ',
        project: '64119ddc6c1d88fde480fd47',
      });
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 400 with message Site already exist', async () => {
    const response = await request(app)
      .post('/api/locations')
      .set('Authorization', `Bearer ${token}`)
      .send(locationData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Location already exist',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/locations').send(locationData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll locations
describe('GET /api/locations', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.m4KGHeX7ut2fRp42YOriWVJB2ruro6Xb1QeN2InZwI4';
  const projectId = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message get all locations List', async () => {
    const response = await request(app)
      .get(`/api/locations?project=${projectId}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({ data: [{}], status: true });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/locations');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update locations
describe('PATCH /api/locations/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.m4KGHeX7ut2fRp42YOriWVJB2ruro6Xb1QeN2InZwI4';
  const id = '6412bb11963bd50565409898';
  const updateData = {
    title: 'New testing',
    projectId: '64119ddc6c1d88fde480fd47',
  };
  it('returns 200 and message update locations', async () => {
    const response = await request(app)
      .patch(`/api/locations/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({ data: {}, status: true });
  });

  it('returns 400 with message locations does not exist', async () => {
    const response = await request(app)
      .post('/api/locations/6412a388268e9b25cef57a30')
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(404);
    expect(response.body).toMatchObject({});
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).patch(`/api/locations/${id}`).send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  Get locationsById
describe('GET /api/locations/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.m4KGHeX7ut2fRp42YOriWVJB2ruro6Xb1QeN2InZwI4';
  const id = '6412bb11963bd50565409898';

  it('returns 200 and message get locations by id', async () => {
    const response = await request(app)
      .get(`/api/locations/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Location fetched successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/locations/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Locations does not exist', async () => {
    const response = await request(app)
      .get('/api/locations/64119ddc6c1d88fde480fd40')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Location does not exist',
      status: false,
    });
  });
});

//  Delete siteById
describe('DELETE /api/locations/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.m4KGHeX7ut2fRp42YOriWVJB2ruro6Xb1QeN2InZwI4';
  const id = '6412bb11963bd50565409898';

  it('returns 200 and message delete locations by id', async () => {
    const response = await request(app)
      .delete(`/api/locations/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Location has been deleted successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).delete(`/api/locations/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 400 with message Location does not exist', async () => {
    const response = await request(app)
      .delete('/api/locations/6412b7a6644aa8e810eb73fb')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Location does not exist',
      status: false,
    });
  });
});
