const request = require('supertest');
const app = require('../../app/server');

// create project-strings
describe('POST /api/project-strings', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const projectStringsData = {
    name: 'String 1',
    fromLocation: '63e61df2d0c36c38cdfca032',
    toLocation: '63e61df9d0c36c38cdfca036',
    project: '64119ddc6c1d88fde480fd47',
  };

  it('returns 200 and message ProjectString has been created successfully', async () => {
    const response = await request(app)
      .post('/api/project-strings')
      .set('Authorization', `Bearer ${token}`)
      .send(projectStringsData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'ProjectString has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/project-strings')
      .set('Authorization', `Bearer ${token}`)
      .send(projectStringsData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message ProjectString already exist', async () => {
    const response = await request(app)
      .post('/api/project-strings')
      .set('Authorization', `Bearer ${token}`)
      .send(projectStringsData);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'ProjectString already exist',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/project-strings').send(projectStringsData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll project-strings
describe('GET /api/project-strings', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';

  it('returns 200 and message ProjectString has been retireved successfully', async () => {
    const response = await request(app)
      .get('/api/project-strings')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}],
      message: 'ProjectString has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/project-strings');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update project-strings
describe('PATCH /api/project-strings/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '6418254579020ec836ff6b7b';
  const updateData = {
    name: 'String 12',
    fromLocation: '63e61dfed0c36c38cdfca03a',
    toLocation: '63e61e01d0c36c38cdfca03e',
  };
  it('returns 200 and message ProjectString has been updated successfully', async () => {
    const response = await request(app)
      .patch(`/api/project-strings/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'ProjectString has been updated successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post(`/api/project-strings/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send();
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message No ProjectString Found', async () => {
    const response = await request(app)
      .post('/api/project-strings/6412a388268e9b25cef57a30')
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(404);
    expect(response.body).toMatchObject({
      message: 'No ProjectString Found',
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).patch(`/api/project-strings/${id}`).send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// get list of project string by project
describe('GET /api/project-strings/list/:projectId', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const projectId = '64119ddc6c1d88fde480fd47';

  it('returns 200 and message ProjectString has been retireved successfully', async () => {
    const response = await request(app)
      .get(`/api/project-strings/list/${projectId}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'ProjectString has been retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/project-strings/list/${projectId}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message No ProjectString Found', async () => {
    const response = await request(app)
      .get('/api/project-strings/list/64119ddc6c1d88fde480fd40')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'No ProjectString Found',
      status: false,
    });
  });
});

//  Delete project-stringsById
describe('DELETE /api/project-strings/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '64182661732b088ac9987d56';

  it('returns 200 and message ProjectString has been deleted successfully', async () => {
    const response = await request(app)
      .delete(`/api/project-strings/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'ProjectString has been deleted successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).delete(`/api/project-strings/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message No ProjectString Found', async () => {
    const response = await request(app)
      .delete('/api/project-strings/6412b7a6644aa8e810eb73fb')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'No ProjectString Found',
      status: false,
    });
  });
});
