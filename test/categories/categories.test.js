const request = require('supertest');
const app = require('../../app/server');

// create categories
describe('POST /api/categories', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const categoriesData = {
    categoryName: 'Test Card Category',
    iconUrl: 'https://via.placeholder.com/150',
    isVisibleForIncidentCard: true,
    isVisibleForSafeCard: true,
    isVisibleForUnsafeCard: false,
  };

  it('returns 200 and message Category has been created successfully', async () => {
    const response = await request(app)
      .post('/api/categories')
      .set('Authorization', `Bearer ${token}`)
      .send(categoriesData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Category has been created successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post('/api/categories')
      .set('Authorization', `Bearer ${token}`)
      .send(categoriesData);
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message Category already exist', async () => {
    const response = await request(app)
      .post('/api/categories')
      .set('Authorization', `Bearer ${token}`)
      .send(categoriesData);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Category already exist',
      status: false,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).post('/api/categories').send(categoriesData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

// getAll categories
describe('GET /api/categories', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';

  it('returns 200 and message Category list was retireved successfully', async () => {
    const response = await request(app)
      .get('/api/categories')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: [{}, {}],
      message: 'Category list was retireved successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get('/api/categories');
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  update categories
describe('PATCH /api/categories/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '6417e5ea2104071615d7014d';
  const updateData = {
    categoryName: '2',
    iconUrl: '2',
    isLifeSaving: '2',
    isVisibleForSafeCard: false,
    isVisibleForUnsafeCard: true,
  };
  it('returns 200 and message Category has been updated successfully', async () => {
    const response = await request(app)
      .patch(`/api/categories/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Category has been updated successfully',
      status: true,
    });
  });

  it('returns 422 with message Required field cannot be empty.', async () => {
    const response = await request(app)
      .post(`/api/categories/${id}`)
      .set('Authorization', `Bearer ${token}`)
      .send();
    expect(response.status).toBe(422);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Required field cannot be empty.',
      status: false,
    });
  });

  it('returns 404 with message Category does not exist', async () => {
    const response = await request(app)
      .post('/api/categories/6412a388268e9b25cef57a30')
      .set('Authorization', `Bearer ${token}`)
      .send(updateData);
    expect(response.status).toBe(404);
    expect(response.body).toMatchObject({
      message: 'Category does not exist',
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).patch(`/api/categories/${id}`).send(updateData);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });
});

//  Get categoriesById
describe('GET /api/categories/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '6417e5ea2104071615d7014d';

  it('returns 200 and message Category fetched Successfully', async () => {
    const response = await request(app)
      .get(`/api/categories/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Category fetched Successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).get(`/api/categories/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message Category does not exist', async () => {
    const response = await request(app)
      .get('/api/categories/64119ddc6c1d88fde480fd40')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Category does not exist',
      status: false,
    });
  });
});

//  Delete categoriesById
describe('DELETE /api/categories/:id', () => {
  const token =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.I513fTkTfDgrekZVxyysoPEtU5Rmtr3nM-7HB7Hj7hE';
  const id = '6417e9d6a64bc07b01a0280a';

  it('returns 200 and message Category has been deleted successfully', async () => {
    const response = await request(app)
      .delete(`/api/categories/${id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
    expect(response.body).toMatchObject({
      data: {},
      message: 'Category has been deleted successfully',
      status: true,
    });
  });

  it('returns 400 with message Please send authentication token.', async () => {
    const response = await request(app).delete(`/api/categories/${id}`);
    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: 'Please send authentication token.',
      status: false,
    });
  });

  it('returns 404 with message Category does not exist', async () => {
    const response = await request(app)
      .delete('/api/categories/6412b7a6644aa8e810eb73fb')
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Category does not exist',
      status: false,
    });
  });
});
