name: Bump version
on:
  pull_request:
    types:
      - closed
    branches:
      - development
      - qa
      - staging
      - main
jobs:
  build:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v3
      with:
        ref: ${{ github.sha }}
        fetch-depth: '0'

    - name: Bump version and push tag
      uses: anothrNick/github-tag-action@1.55.0 # Don't use @master unless you're happy to test the latest version
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        WITH_V: true
        PRERELEASE: true
